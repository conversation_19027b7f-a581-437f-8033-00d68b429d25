package com.intelliquor.cloud.shop.salesmanManage.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.intelliquor.cloud.shop.common.exception.BusinessException;
import com.intelliquor.cloud.shop.common.exception.RestResponse;
import com.intelliquor.cloud.shop.common.utils.AirUtils;
import com.intelliquor.cloud.shop.common.utils.ObjectUtil;
import com.intelliquor.cloud.shop.salesmanManage.dao.*;
import com.intelliquor.cloud.shop.salesmanManage.model.CloudDealerStaffModel;
import com.intelliquor.cloud.shop.salesmanManage.model.SalesmanSettingsModel;
import com.intelliquor.cloud.shop.salesmanManage.service.CloudDealerStaffService;
import com.intelliquor.cloud.shop.salesmanManage.util.MapUtils;
import com.intelliquor.cloud.shop.salesmanManage.util.constants.ColumnConstant;
import com.intelliquor.cloud.shop.salesmanManage.util.constants.StatusConstant;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * 云销业务员信息服务实现层
 *
 * <AUTHOR>
 * @date 2021/03/03
 */
@Service
@SuppressWarnings("all")
public class CloudDealerStaffServiceImpl implements CloudDealerStaffService {
    private Logger log = LoggerFactory.getLogger(CloudDealerStaffServiceImpl.class);
    @Autowired
    CloudDealerStaffDao cloudDealerStaffDao;

    @Autowired
    private SalesmanCustomerRelationDao salesmanCustomerRelationDao;
    @Autowired
    private SalesmanVisitPlanDao salesmanVisitPlanDao;
    @Autowired
    private SalesmanVisitPlanInstanceDao salesmanVisitPlanInstanceDao;
    @Autowired
    private SalesmanVisitPlanInstanceDetailDao salesmanVisitPlanInstanceDetailDao;
    @Autowired
    private SalesmanVisitPlanUserDao salesmanVisitPlanUserDao;
    @Autowired
    private SalesmanSettingsDao salesmanSettingsDao;


    /**
     * 插入
     *
     * @param salesmanStaffModel 业务人员模型
     * @return {@link RestResponse}
     */
    @Override
    public RestResponse insert(CloudDealerStaffModel salesmanStaffModel) {
        log.info("CloudDealerStaffServiceImpl|insert|salesmanStaffModel={}", JSONObject.toJSONString(salesmanStaffModel));

        // 1.校验手机号是否注册过
        CloudDealerStaffModel salesmanStaff = cloudDealerStaffDao.selectByPhone(salesmanStaffModel.getPhone(), StatusConstant.DELETE_STATUS.NO_DEL, salesmanStaffModel.getCompanyId(), StatusConstant.STAFF_TYPE.SALESMAN);
        if (ObjectUtil.isNotEmpty(salesmanStaff)) {
            throw new BusinessException("手机号已注册，不可重复注册");
        }

        salesmanStaffModel.setIsDelete(StatusConstant.DELETE_STATUS.NO_DEL);
        salesmanStaffModel.setCreateTime(new Date());
        cloudDealerStaffDao.insertSelective(salesmanStaffModel);
        return RestResponse.success("新增成功");
    }

    /**
     * 查询列表
     *
     * @param param 参数
     * @return {@link List<CloudDealerStaffModel>}
     */
    @Override
    public List<CloudDealerStaffModel> queryList(MapUtils param) {
        param.put(ColumnConstant.IS_DELETE, StatusConstant.DELETE_STATUS.NO_DEL);
        return cloudDealerStaffDao.queryList(param);
    }

    /**
     * 通过主键查询
     *
     * @param id        id
     * @param companyId 公司标识
     * @return {@link CloudDealerStaffModel}
     */
    @Override
    public CloudDealerStaffModel selectByPrimaryKey(Integer id, Integer companyId) {
        return cloudDealerStaffDao.selectByPrimaryKey(id, companyId);
    }

    /**
     * 根据主键选择性更新
     *
     * @param salesmanStaffModel 业务人员模型
     * @return {@link RestResponse}
     */
    @Override
    public RestResponse updateByPrimaryKeySelective(CloudDealerStaffModel salesmanStaffModel) {
        if (ObjectUtil.isNotEmpty(salesmanStaffModel.getPhone())) {
            // 1.校验手机号是否注册过
            CloudDealerStaffModel salesmanStaff = cloudDealerStaffDao.selectByPhone(salesmanStaffModel.getPhone(), StatusConstant.DELETE_STATUS.NO_DEL, salesmanStaffModel.getCompanyId(), StatusConstant.STAFF_TYPE.SALESMAN);
            if (ObjectUtil.isNotEmpty(salesmanStaff) && !salesmanStaffModel.getId().equals(salesmanStaff.getId())) {
                throw new BusinessException("手机号已注册，不可重复注册");
            }
        }
        salesmanStaffModel.setUpdateTime(new Date());
        cloudDealerStaffDao.updateByPrimaryKeySelective(salesmanStaffModel);
        return RestResponse.success("操作成功");
    }

@Override
public CloudDealerStaffModel selectByPhone(String phone, Integer companyId) {
    CloudDealerStaffModel salesmanStaff =cloudDealerStaffDao.selectByPhone(phone,StatusConstant.DELETE_STATUS.NO_DEL, companyId,StatusConstant.STAFF_TYPE.SALESMAN);
    if(salesmanStaff == null || salesmanStaff.getType() != StatusConstant.STAFF_TYPE.SALESMAN){
        throw new BusinessException("该业务员不存在");
    }
    if(salesmanStaff.getVisitorType().equals(StatusConstant.VISITOR_TYPE.DEALER)){
        salesmanStaff.setCustomersName(cloudDealerStaffDao.selectDealerNamebyId(salesmanStaff.getDealerId()));
    }else{
        List<SalesmanSettingsModel> SettingList = salesmanSettingsDao.selectByCompanyId(companyId, "customersName");
        String customersName = SettingList.get(0).getParameterValue();
        salesmanStaff.setCustomersName(customersName);
    }
    return salesmanStaff;
}

    @Override
    public CloudDealerStaffModel getByOpenId(String openId, Integer companyId) {
        CloudDealerStaffModel salesmanStaff = cloudDealerStaffDao.selectByOpenId(openId, StatusConstant.DELETE_STATUS.NO_DEL, companyId, StatusConstant.STAFF_TYPE.SALESMAN);
        if(salesmanStaff == null || salesmanStaff.getType() != StatusConstant.STAFF_TYPE.SALESMAN){
            throw new BusinessException("该业务员不存在");
        }
        if(salesmanStaff.getVisitorType().equals(StatusConstant.VISITOR_TYPE.DEALER)){
            salesmanStaff.setCustomersName(cloudDealerStaffDao.selectDealerNamebyId(salesmanStaff.getDealerId()));
        }else{
            List<SalesmanSettingsModel> SettingList = salesmanSettingsDao.selectByCompanyId(companyId, "customersName");
            String customersName = SettingList.get(0).getParameterValue();
            salesmanStaff.setCustomersName(customersName);
        }
        return salesmanStaff;

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteById(Integer id,Integer companyId) {
        CloudDealerStaffModel staffModel = cloudDealerStaffDao.selectByPrimaryKey(id,companyId);
        if(staffModel == null || staffModel.getIsDelete() == 0){
            return;
        }
        cloudDealerStaffDao.deleteByPrimaryKey(id);
        //删除绑定的客户关系
        salesmanCustomerRelationDao.deleteByStaffId(id,companyId);
        //删除计划，终止进行中的实例
        salesmanVisitPlanDao.deleteByStaffId(id,companyId);
        salesmanVisitPlanInstanceDao.deleteByStaffId(id,companyId);
        salesmanVisitPlanInstanceDetailDao.deleteByStaffId(id,companyId);
        salesmanVisitPlanUserDao.deleteByStaffId(id,companyId);
        salesmanVisitPlanUserDao.deleteByStaffId(id,companyId);
    }

    /**
     * 修改业务员状态
     *
     * @param salesmanStaffModel
     * @return void
     * @auther: tms
     * @date: 2021/04/23 11:26
     */
    @Override
    public void mdyStaffStatus(CloudDealerStaffModel salesmanStaffModel) {
        salesmanStaffModel.setOpenId("");
        salesmanVisitPlanUserDao.mdyStaffStatus(salesmanStaffModel);
    }

}
