package com.intelliquor.cloud.shop.salesmanManage.service.impl;

import com.intelliquor.cloud.shop.salesmanManage.dao.SalesmanNewsInfoDao;
import com.intelliquor.cloud.shop.salesmanManage.model.SalesmanNewsInfoModel;
import com.intelliquor.cloud.shop.salesmanManage.service.SalesmanNewsInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
* 内容管理 服务实现层
* <AUTHOR>
*/
@Service
public class SalesmanNewsInfoServiceImpl implements SalesmanNewsInfoService {

    @Autowired
    private SalesmanNewsInfoDao salesmanNewsInfoDao;

    /**
    * 查询数据
    *
    * @return
    */
    @Override
    public List<SalesmanNewsInfoModel> selectList(Map<String, Object> searchMap) {
        return salesmanNewsInfoDao.selectList(searchMap);
    }


    /**
    * 新增数据
    *
    * @param model
    */
    @Override
    public void insert(SalesmanNewsInfoModel model) {
        salesmanNewsInfoDao.insert(model);
    }

    /**
    * 更新数据
    *
    * @param model
    */
    @Override
    public void update(SalesmanNewsInfoModel model) {
        salesmanNewsInfoDao.update(model);
    }

    /**
    * 删除数据
    *
    * @param id
    */
    @Override
    public void delete(Integer id) {
        salesmanNewsInfoDao.delete(id);
    }

    /**
    * 根据ID查询数据
    *
    * @param id
    */
    @Override
    public SalesmanNewsInfoModel getById(Integer id) {
        return salesmanNewsInfoDao.getById(id);
    }
}