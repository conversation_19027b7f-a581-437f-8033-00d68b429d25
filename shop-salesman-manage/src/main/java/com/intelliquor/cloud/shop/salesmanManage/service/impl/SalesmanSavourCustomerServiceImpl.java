package com.intelliquor.cloud.shop.salesmanManage.service.impl;

import com.intelliquor.cloud.shop.salesmanManage.dao.SalesmanSavourCustomerDao;
import com.intelliquor.cloud.shop.salesmanManage.model.SalesmanSavourCustomerModel;
import com.intelliquor.cloud.shop.salesmanManage.model.resp.SalesmanSavourCustomerResp;
import com.intelliquor.cloud.shop.salesmanManage.service.SalesmanSavourCustomerService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
* 描述：品鉴会客户表 服务实现层
* <AUTHOR>
* @date 2021-04-14
*/
@Service
public class SalesmanSavourCustomerServiceImpl implements SalesmanSavourCustomerService {

    @Autowired
    private SalesmanSavourCustomerDao salesmanSavourCustomerDao;

    /**
    * 查询数据
    *
    * @return
    */
    @Override
    public List<SalesmanSavourCustomerModel> selectList(Map<String, Object> searchMap) {
        return salesmanSavourCustomerDao.selectList(searchMap);
    }


    /**
    * 新增数据
    *
    * @param model
    */
    @Override
    public void insert(SalesmanSavourCustomerModel model) {
        salesmanSavourCustomerDao.insert(model);
    }

    /**
    * 更新数据
    *
    * @param model
    */
    @Override
    public void update(SalesmanSavourCustomerModel model) {
        salesmanSavourCustomerDao.update(model);
    }

    /**
    * 删除数据
    *
    * @param id
    */
    @Override
    public void delete(Integer id) {
        salesmanSavourCustomerDao.delete(id);
    }

    /**
    * 根据ID查询数据
    *
    * @param id
    */
    @Override
    public SalesmanSavourCustomerResp getById(Integer id) {
        return salesmanSavourCustomerDao.getById(id);
    }


    /**
     * 查询数据
     *
     * @return
     */
    @Override
    public List<SalesmanSavourCustomerResp> selectCustomerList(Map<String, Object> searchMap) {
        return salesmanSavourCustomerDao.selectCustomerList(searchMap);
    }

}