package com.intelliquor.cloud.shop.salesmanManage.service;

import com.intelliquor.cloud.shop.salesmanManage.model.SalesmanInspectRecordModel;
import com.intelliquor.cloud.shop.salesmanManage.model.resp.SalesmanInspectRecordResp;
import com.intelliquor.cloud.shop.salesmanManage.model.resp.SalesmanInspectResultResp;

import java.util.List;
import java.util.Map;

public interface SalesmanInspectRecordService {

    /**
     * 查询数据
     *
     * @return
     */
    List<SalesmanInspectRecordModel> selectList(Map<String, Object> searchMap);


    /**
     * 新增数据
     *
     * @param model
     */
    void insert(SalesmanInspectRecordModel model);

    /**
     * 更新数据
     *
     * @param model
     */
    void update(SalesmanInspectRecordModel model);

    /**
     * 删除数据
     *
     * @param id
     */
    void delete(Integer id);

    /**
     * 根据ID查询数据
     *
     * @param id
     */
    SalesmanInspectRecordModel getById(Integer id);

    /**
     * 扫码结果
     * @param qrcode
     * @param companyId
     * @param longitude
     * @param latitude
     * @return
     */
    SalesmanInspectResultResp scan(String qrcode,Integer companyId,Double longitude, Double latitude);

    /**
     * 查询数据信息
     *
     * @param searchMap
     * @return
     */
    List<SalesmanInspectRecordResp> selectListByCondition(Map<String, Object> searchMap);
}
