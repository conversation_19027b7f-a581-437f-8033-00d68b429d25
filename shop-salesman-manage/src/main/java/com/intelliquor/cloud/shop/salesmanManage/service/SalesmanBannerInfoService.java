package com.intelliquor.cloud.shop.salesmanManage.service;

import com.intelliquor.cloud.shop.salesmanManage.model.SalesmanBannerInfoModel;

import java.util.List;
import java.util.Map;

public interface SalesmanBannerInfoService {

    /**
     * 功能描述: 查询列表
     *
     * @param param
     */
    List<SalesmanBannerInfoModel> queryList(Map<String,Object> param);

    /**
     * 功能描述: 根据id获取
     *
     * @param id
     */
    SalesmanBannerInfoModel findById(Integer id);


    /**
     * 功能描述: 添加
     *
     * @param param
     * @return java.lang.Integer
     */
    Integer insert(SalesmanBannerInfoModel param);

    /**
     * 功能描述: 修改
     *
     * @param param
     * @return java.lang.Integer
     */
    Integer update(SalesmanBannerInfoModel param);

    /**
     * 功能描述: 根据id删除
     * @param id
     * @return java.lang.Integer
     */
    Integer delete(Integer id);
}
