package com.intelliquor.cloud.shop.dealer.dao;

import com.intelliquor.cloud.shop.dealer.model.PayInfoModel;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 描述：商户信息 Dao接口
 *
 * <AUTHOR>
 * @date 2019-10-11
 */
public interface PayInfoDao {


    /**
     * 查询数据信息
     *
     * @param searchMap
     * @return
     */
    List<PayInfoModel> selectList(Map<String, Object> searchMap);

    /**
     * 新增
     *
     * @param model
     * @return
     */
    Integer insert(PayInfoModel model);

    /**
     * 更新
     *
     * @param model
     * @return
     */
    Integer update(PayInfoModel model);

    /**
     * 删除
     *
     * @param id
     * @return
     */
    Integer delete(Integer id);

    /**
     * 根据ID查询
     *
     * @param id
     * @return
     */
    PayInfoModel getById(Integer id);

    /**
     * 根据公司ID查询
     *
     * @param companyId
     * @return
     */
    PayInfoModel getByCompanyId(@Param(value = "companyId") Integer companyId);

    /**
     * 根据公司ID查询
     *
     * @param appId
     * @return
     */
    PayInfoModel getByAppId(@Param(value = "appId") String appId);

}