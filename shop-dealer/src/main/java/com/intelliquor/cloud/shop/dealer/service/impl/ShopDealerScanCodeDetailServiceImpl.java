package com.intelliquor.cloud.shop.dealer.service.impl;

import com.intelliquor.cloud.shop.dealer.dao.ShopDealerScanCodeDetailDao;
import com.intelliquor.cloud.shop.dealer.model.DealerScanCodeDetailModel;
import com.intelliquor.cloud.shop.dealer.service.ShopDealerScanCodeDetailService;
import com.intelliquor.cloud.shop.dealer.util.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Auther: tianms
 * @Date: 2021/01/20 11:13
 * @Description: 分销商扫码进货明细-service impl
 */
@Service("shopDealerScanCodeDetailService")
public class ShopDealerScanCodeDetailServiceImpl implements ShopDealerScanCodeDetailService {

    @Autowired
    private ShopDealerScanCodeDetailDao shopDealerScanCodeDetailDao;

    /**
     * 功能描述: 商品数量列表
     *
     * @param mapUtils
     * @return java.util.List<com.intelliquor.cloud.shop.dealer.model.DealerScanCodeDetailModel>
     * @auther: tms
     * @date: 2021/01/20 11:26
     */
    @Override
    public List<DealerScanCodeDetailModel> queryGoodsNumList(MapUtils mapUtils) {
        return shopDealerScanCodeDetailDao.queryGoodsNumList(mapUtils);
    }
}
