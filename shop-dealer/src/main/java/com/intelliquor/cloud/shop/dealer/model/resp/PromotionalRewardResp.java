package com.intelliquor.cloud.shop.dealer.model.resp;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @Author: MAX
 * @CreateTime: 2023-03-17  10:41
 */
@Data
public class PromotionalRewardResp implements Serializable {
    private static final long serialVersionUID = -928850694765582054L;

    /**
     * 应上账金额
     */
    @JsonProperty("should")
    private BigDecimal totalAmount;

    /**
     * 已上账金额
     */
    @JsonProperty("has")
    private BigDecimal accountAmount;
}
