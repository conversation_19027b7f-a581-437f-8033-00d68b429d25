package com.intelliquor.cloud.shop.dealer.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


/**
* 描述：国台商品基础信息实体类
* <AUTHOR>
* @date 2021-12-17
*/
@Data
public class CloudDealerGtProductBasicModel {


    @ApiModelProperty(value = "")
    private Integer id;

    @ApiModelProperty(value = "商品Id")
    private String productId;

    @ApiModelProperty(value = "商品编码")
    private String productCode;

    @ApiModelProperty(value = "商品名称")
    private String productName;

    @ApiModelProperty(value = "瓶箱比例（规格）")
    private Integer bottleBoxRatio=0;
}