package com.intelliquor.cloud.shop.dealer.model.resp;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.intelliquor.cloud.shop.common.model.resp.PageInfoResp;
import lombok.Data;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * 积分下单
 * @TableName t_promotional_score
 */
@Data
public class PromotionalScoreRtnResp implements Serializable {
    /**
     * 待发货积分
     */
   /* @JsonProperty("wait_send")
    private BigDecimal waitSend;*/

    /**
     * 在途积分
     */
    /*@JsonProperty("in_transit")
    private BigDecimal inTransit;*/

    /**
     * 已完成积分
     */
    /*@JsonProperty("finish")
    private BigDecimal finish;*/


    /**
     *  @author: HLQ
     *  @Date: 2023/5/5 15:04
     *  @Description:待上账
     */
    @JsonProperty("remain_reward")
    private BigDecimal remainReward;

    /**
     * 已上账的奖励，即上账到中台成功的奖励之和
     */
    @JsonProperty("has_reward")
    private BigDecimal hasReward;

   private  List<PromotionalScoreNewResp> data = new ArrayList<>();
    @JsonProperty("__paging")
    private PageInfoResp page;
}
