package com.intelliquor.cloud.shop.dealer.service.impl;

import com.intelliquor.cloud.shop.dealer.dao.ShopDealerScanBalanceDao;
import com.intelliquor.cloud.shop.dealer.model.DealerRewardScanBalanceModel;
import com.intelliquor.cloud.shop.dealer.service.ShopDealerScanBalance;
import com.intelliquor.cloud.shop.dealer.util.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @Auther: tianms
 * @Date: 2021/01/20 10:33
 * @Description: 分销商进货批次-service impl
 */
@Service("shopDealerScanBalance")
public class ShopDealerScanBalanceImpl implements ShopDealerScanBalance {

    @Autowired
    private ShopDealerScanBalanceDao shopDealerScanBalanceDao;


    /**
     * 功能描述: 分销商扫码和瓶数量
     *
     * @param param
     * @return java.lang.Integer
     * @auther: tms
     * @date: 2020/12/18 11:06
     */
    @Override
    public DealerRewardScanBalanceModel gtScanCodAndBottleNum(MapUtils param) {
        return shopDealerScanBalanceDao.gtScanCodAndBottleNum(param);
    }

    /**
     * 功能描述: 获取最新的一次进货详情
     *
     * @param mapUtils
     * @return com.intelliquor.cloud.shop.dealer.model.DealerRewardScanBalanceModel
     * @auther: tms
     * @date: 2021/01/20 10:36
     */
    @Override
    public DealerRewardScanBalanceModel getScanCodeNews(MapUtils mapUtils) {
        return shopDealerScanBalanceDao.getScanCodeNews(mapUtils);
    }

    /**
     * 功能描述: 获取（指定经销商下）分销商的进货次数
     *
     * @param dealerCode
     * @param parentId
     * @param startDate
     * @param endDate
     * @return java.lang.Integer
     * @auther: tms
     * @date: 2021/02/22 11:30
     */
    @Override
    public Integer distributorPurchaseNum(String dealerCode, Integer parentId, String startDate, String endDate, Integer companyId) {
        return shopDealerScanBalanceDao.distributorPurchaseNum(dealerCode, parentId, startDate, endDate, companyId);
    }

}
