package com.intelliquor.cloud.shop.dealer.model.resp;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.intelliquor.cloud.shop.common.annotation.Excel;
import com.intelliquor.cloud.shop.common.annotation.ExcelTitle;
import com.intelliquor.cloud.shop.common.annotation.LevelExcelTitle;
import com.intelliquor.cloud.shop.common.annotation.Sheet;
import com.intelliquor.cloud.shop.common.utils.BeanUtil;
import com.intelliquor.cloud.shop.dealer.service.CallZyService;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;
import java.util.Map;

/***
 * <AUTHOR>
 * @date 2020-12-19
 * 分销商进货列表实体类
 */
@Excel(fileName = "分销商进货列表.xls")
@Sheet(sheetNames = {"分销商进货列表"}, numPerSheet = 50000, numOfSheet = {}, groupNumber = 1)
@LevelExcelTitle(titleNames = {"分销商进货列表"}, titleLevel =1, colSpans = {7}, groupNumber = 1)
@Data
public class DistributorPurchaseResp implements Serializable {

    /**
     * 进货Id
     */
    private Integer id;

    /**
     * 分销商编码
     */
    @ExcelTitle(titleName = "分销商编码", index = 1, groupNumber = 1, width = 5000)
    private String distributorCode;
    /**
     * 分销商名称
     */
    @ExcelTitle(titleName = "分销商名称", index = 2, groupNumber = 1, width = 5000)
    private String distributorName;

    /**
     * 经销商编码
     */
    @ExcelTitle(titleName = "经销商编码", index = 3, groupNumber = 1, width = 5000)
    private String dealerCode;
    /**
     * 经销商名称
     */
    @ExcelTitle(titleName = "经销商名称", index = 4, groupNumber = 1, width = 5000)
    private String dealerName;

    /**
     * 业务区域
     */
    @ExcelTitle(titleName = "业务区域", index = 5, groupNumber = 1, width = 5000)
    private String dealerArea;

    /**
     * 扫码瓶数
     */
    @ExcelTitle(titleName = "扫码瓶数", index = 6, groupNumber = 1, width = 5000)
    private Integer bottleNum;

    /**
     * 扫码进货时间
     */
    @ExcelTitle(titleName = "扫码进货时间", index = 7, groupNumber = 1, width = 5000)
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    private Integer companyId;

//    public void setDealerArea(String dealerArea) {
//        String busAreaName = "";
//        this.dealerArea = dealerArea;
//        if (this.companyId == null) {
//            return;
//        }
//        // 获取智盈业务区域列表
//        Map<String, String> areaMap = BeanUtil.getBean(CallZyService.class).getAreaList(this.companyId);
//        busAreaName = areaMap.get(String.valueOf(this.dealerArea));
//        if (StringUtils.isEmpty(busAreaName)) {
//            busAreaName = "";
//        }
//        this.dealerArea = busAreaName;
//    }
}
