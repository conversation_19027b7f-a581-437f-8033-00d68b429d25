package com.intelliquor.cloud.shop.dealer.service;


import com.intelliquor.cloud.shop.dealer.dao.WechatHelpInfoDao;
import com.intelliquor.cloud.shop.dealer.model.WechatHelpInfoModel;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;

/**
 * 描述：帮助中心-帮助信息 服务实现层
 * 
 * <AUTHOR>
 * @date 2019/07/3
 */
@Service
public class WechatHelpInfoService {

    @Autowired
    private WechatHelpInfoDao wechatHelpInfoDao;

    /**
     * 查询数据
     *
     * @return
     */
    public List<WechatHelpInfoModel> selectList(Map<String, Object> searchMap) {
        return wechatHelpInfoDao.selectList(searchMap);
    }


    /**
     * 查询数据
     *
     * @return
     */
    public List<WechatHelpInfoModel> selectListByName(String question,Integer companyId) {
        return wechatHelpInfoDao.selectListByName(question,companyId);
    }



    /**
     * 新增数据
     *
     * @param model
     */
    public void insert(WechatHelpInfoModel model) {
        wechatHelpInfoDao.insert(model);
    }

    /**
     * 有用
     *
     * @param id
     */
    @Transactional(rollbackFor = Exception.class)
    public void doUseful(Integer id) {
        WechatHelpInfoModel model = wechatHelpInfoDao.getById(id);
        int newUserfulNum = model.getUsefulNum() + 1;
        model.setUsefulNum(newUserfulNum);
        wechatHelpInfoDao.update(model);
    }

    /**
     * 无用
     *
     * @param id
     */
    public void doUseless(Integer id) {
        WechatHelpInfoModel model = wechatHelpInfoDao.getById(id);
        int newUserlessNum = model.getUselessNum() + 1;
        model.setUselessNum(newUserlessNum);
        wechatHelpInfoDao.update(model);
    }

    /**
     * 更新数据
     *
     * @param model
     */
    public void update(WechatHelpInfoModel model) {
        wechatHelpInfoDao.update(model);
    }


    /**
     * 删除数据
     *
     * @param id
     */
    public void delete(Integer id) {
        wechatHelpInfoDao.delete(id);
    }

    /**
     * 根据ID查询数据
     *
     * @param id
     */
    public WechatHelpInfoModel getById(Integer id) {
        return wechatHelpInfoDao.getById(id);
    }
}