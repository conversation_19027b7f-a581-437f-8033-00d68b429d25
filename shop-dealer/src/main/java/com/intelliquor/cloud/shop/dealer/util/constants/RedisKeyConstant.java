package com.intelliquor.cloud.shop.dealer.util.constants;

/**
 * @Auther: tianms
 * @Date: 2020/12/16 15:26
 * @Description: redis key前缀常量
 */
public class RedisKeyConstant {

    // 云商登录验证码前缀，后面加手机号 例：CLOUD_DEALER_LOGIN_VER_CODE_156666666666
    public static final String CLOUD_DEALER_LOGIN_VER_CODE_ = "CLOUD_DEALER_LOGIN_VER_CODE_";

    // 云商用户注册前缀，防止用户重复注册，后面加用户openId 例：CLOUD_DEALER_REGISTER_xxxxxxxxxx
    public static final String CLOUD_DEALER_REGISTER_ = "CLOUD_DEALER_REGISTER_";

    // 云商提现前缀，CLOUD_DEALER_DRAW_MONEY_ + 公司id + 经销商/分销商id
    public static final String CLOUD_DEALER_DRAW_MONEY = "CLOUD_DEALER_DRAW_MONEY:";

    // 云商扫码前缀，CLOUD_DEALER_SCAN_CODE_ + 码 + 人
    public static final String CLOUD_DEALER_SCAN_CODE_ = "CLOUD_DEALER_SCAN_CODE_:";

    // 云商pda登录的用户信息
    public static final String CLOUD_DEALER_PAD_ = "CLOUD_DEALER_PAD_";

    // 云商注册提报前缀 CLOUD_DEALER_REGISTER_REPORT_ + 手机号 + 商户id
    public static final String CLOUD_DEALER_REGISTER_REPORT_ = "CLOUD_DEALER_REGISTER_REPORT_";

    // 云商注册提报前缀 CLOUD_DEALER_REPORT_EXAMINE_ + 审核数据id
    public static final String CLOUD_DEALER_REPORT_EXAMINE_ = "CLOUD_DEALER_REPORT_EXAMINE_";


}
