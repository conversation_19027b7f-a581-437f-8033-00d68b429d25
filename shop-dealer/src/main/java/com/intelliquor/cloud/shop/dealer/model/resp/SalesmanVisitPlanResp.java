package com.intelliquor.cloud.shop.dealer.model.resp;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.intelliquor.cloud.shop.dealer.model.SalesmanVisitPlanInstanceModel;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

/**
* 描述：拜访计划实体类
* <AUTHOR>
* @date 2021-03-10
*/
@Data
public class SalesmanVisitPlanResp {


    /**
     * 主键
     */
    private Integer id;

    /**
     * 计划类型 1-周期计划 2-突击计划
     */
    private Integer planType;

    /**
     * 计划类型描述 1-周期计划 2-突击计划
     */
    private String planTypeStr;

    /**
     * 计划名称
     */
    private String planName;

    /**
     * 业务员Id
     */
    private Integer staffId;
    /**
     * 业务员名称
     */
    private String staffName;

    /**
     * 业务员电话
     */
    private String staffPhone;

    /**
     * 拜访对象类型 1-常规拜访（即终端店）、2-潜在拜访（即客户）
     */
    private Integer visitType;

    /**
     * 拜访对象类型描述 1-常规拜访（即终端店）、2-潜在拜访（即客户）
     */
    private String visitTypeStr;

    /**
     * 拜访对象Id，多个用逗号分隔
     */
    private String visitUser;

    /**
     * 拜访对象数量
     */
    private Integer totalNum;

    /**
     * 计划周期 1-天 2-周 3-月 4-年
     */
    private Integer planPeriod;
    /**
     * 计划周期描述 1-天 2-周 3-月 4-年
     */
    private String planPeriodStr;

    /**
     * 计划开始时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date beginTime;

    /**
     * 计划结束时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    /**
     * 是否不限制结束时间 0-否 1-是，为1时end_time不生效
     */
    private Integer endTimeFlag;

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /**
     * 创建人，云商小程序创建任务时为具体的业务员Id
     */
    private Integer createUser;

    /**
     * 云商小程序修改任务时为具体的业务员
     */
    private Integer updateUser;

    private Integer companyId;

    /**
     * 是否删除 0-否 1-是
     */
    private Integer isDelete;

    /**
     * 是否停用 0-否 1-是
     */
    private Integer status;

    /**
     * 拜访状态  0-未开始 1-进行中 2-超期未完成 3-成功完成 4-已失访
     */
    private Integer visitStatus;
    /**
     * 突击拜访时，展示实例的状态 0-未开始 1-进行中 2-超期未完成 3-成功完成 4-已失访
     * @see com.intelliquor.cloud.shop.dealer.util.enums.VisitStatusEnum
     */
    private String statusStr;

    /**
     * 已完成次数
     */
    private Integer completeCount = 0;

    /**
     * 拜访对象集合-常规拜访（终端）
     */
    List<SalesmanVisitPlanUserResp> shopList;

    /**
     * 拜访对象集合-潜在拜访（客户）
     */
    List<SalesmanVisitPlanUserResp> customerList;

    /**
     * 每个周期实例的完成情况
     */
    List<SalesmanVisitPlanInstanceModel> instanceList;

    /**
     * 拜访对象名称模糊查询
     */
    private String userNameLike;

    /**
     * 拜访对象名称
     */
    private String userNames;
}