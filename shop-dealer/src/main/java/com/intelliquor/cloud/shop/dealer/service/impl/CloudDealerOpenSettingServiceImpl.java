package com.intelliquor.cloud.shop.dealer.service.impl;

import com.intelliquor.cloud.shop.dealer.dao.CloudDealerOpenSettingDao;
import com.intelliquor.cloud.shop.dealer.model.CloudDealerOpenSettingModel;
import com.intelliquor.cloud.shop.dealer.service.CloudDealerOpenSettingService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
* 描述：拆箱率设置 服务实现层
* <AUTHOR>
* @date 2021-12-14
*/
@Service
public class CloudDealerOpenSettingServiceImpl implements CloudDealerOpenSettingService {

    @Autowired
    private CloudDealerOpenSettingDao cloudDealerOpenSettingDao;

    /**
    * 查询数据
    *
    * @return
    */
    @Override
    public List<CloudDealerOpenSettingModel> selectList(Map<String, Object> searchMap) {
        return cloudDealerOpenSettingDao.selectList(searchMap);
    }


    /**
    * 新增数据
    *
    * @param model
    */
    @Override
    public void insert(CloudDealerOpenSettingModel model) {
        if(model.getId() == null){
            cloudDealerOpenSettingDao.insert(model);
        }else{
            model.setUpdateTime(new Date());
            cloudDealerOpenSettingDao.update(model);
        }
    }

    /**
    * 更新数据
    *
    * @param model
    */
    @Override
    public void update(CloudDealerOpenSettingModel model) {
        cloudDealerOpenSettingDao.update(model);
    }

    /**
    * 删除数据
    *
    * @param id
    */
    @Override
    public void delete(Integer id) {
        cloudDealerOpenSettingDao.delete(id);
    }

    /**
    * 根据ID查询数据
    *
    * @param id
    */
    @Override
    public CloudDealerOpenSettingModel getById(Integer id) {
        return cloudDealerOpenSettingDao.getById(id);
    }

    /**
     * 获取拆箱率设置
     * @param companyId
     * @param type
     * @return
     */
    @Override
    public CloudDealerOpenSettingModel getOpenSetting(Integer companyId, Integer type) {
        return cloudDealerOpenSettingDao.getOpenSetting(companyId,type);
    }
}