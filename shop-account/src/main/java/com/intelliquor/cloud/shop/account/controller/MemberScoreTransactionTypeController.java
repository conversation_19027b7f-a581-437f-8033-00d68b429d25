package com.intelliquor.cloud.shop.account.controller;

import com.intelliquor.cloud.shop.account.entity.MemberScoreTransactionType;
import com.intelliquor.cloud.shop.account.service.IMemberScoreTransactionTypeService;
import com.intelliquor.cloud.shop.common.entity.Response;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <p>
 * 会员积分交易类型表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-13
 */
@RestController
@RequestMapping("/account/member-score-transaction-type")
@Api(tags = "会员积分交易类型管理")
public class MemberScoreTransactionTypeController {

    @Autowired
    private IMemberScoreTransactionTypeService memberScoreTransactionTypeService;

    @ApiOperation("获得所有的交易类型")
    @GetMapping("/list")
    public Response<List<MemberScoreTransactionType>> list() {
        return Response.ok(memberScoreTransactionTypeService.list());

    }
}