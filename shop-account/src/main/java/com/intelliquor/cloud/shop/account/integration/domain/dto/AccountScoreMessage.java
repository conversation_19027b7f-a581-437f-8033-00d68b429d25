package com.intelliquor.cloud.shop.account.integration.domain.dto;


import com.intelliquor.cloud.shop.account.dto.ProductInfo;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
public class AccountScoreMessage {
    // 会员唯一ID
    private String memberId;

    // 事项来源类型
    private String transactionSource;

    //子账户id
    private Long memberScoreDetailId;

    // 积分变动值
    private BigDecimal points;

    // 关联商品信息列表
    private List<ProductInfo> product;

    // 二维码信息（可为null）
    private String qrcode;

    // 交易时间
    private String transactionTime;

    // 业务备注（可为null）
    private String remark;

    // 业务单号
    private String businessNo;
    //业务来源
    private String businessSource;

    // 扩展信息
    private String ext;

}
