package com.intelliquor.cloud.shop.system.ops.controller;

import com.intelliquor.cloud.shop.common.exception.RestResponse;
import com.intelliquor.cloud.shop.common.model.UserContext;
import com.intelliquor.cloud.shop.common.model.req.ReissueSalesRewardReq;
import com.intelliquor.cloud.shop.common.model.resp.ReissueSalesRewardResp;
import com.intelliquor.cloud.shop.common.service.ISalesRewardReissueService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponses;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 运维-动销奖励管理接口
 * <p>
 * 提供动销奖励重新发放的运维接口，主要功能包括：
 * 1. 接收需要重新发放奖励的记录ID列表
 * 2. 检查记录是否已存在正常奖励或异常奖励
 * 3. 对符合条件的记录重新发放奖励
 * 4. 返回详细的处理结果，包括已存在奖励和异常奖励的记录IDs
 * </p>
 *
 * <AUTHOR>
 * @since 2024/03/21
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/ops/sales-reward")
@Api(tags = "运维-动销奖励管理接口")
public class OpsSalesRewardController {

    private final UserContext userContext;

    private final ISalesRewardReissueService salesRewardReissueService;

    /**
     * 重新发放动销奖励
     * <p>
     * 处理流程：
     * 1. 接收并验证请求参数
     * 2. 调用服务层处理重新发放逻辑
     * 3. 根据处理结果构建响应信息
     * </p>
     *
     * @param req 请求参数，包含需要重新发放奖励的记录ID列表
     * @return 处理结果，包含已存在奖励和异常奖励的记录IDs
     */
    @PostMapping("/reissue")
    @ApiOperation(value = "重新发放动销奖励运维接口",
            notes = "根据terminal_scan_detail表的ID列表重新发放动销奖励，返回已存在奖励和异常奖励的记录IDs")
    @ApiResponses({
            @io.swagger.annotations.ApiResponse(code = 200, message = "操作成功"),
            @io.swagger.annotations.ApiResponse(code = 400, message = "参数校验失败"),
            @io.swagger.annotations.ApiResponse(code = 500, message = "系统异常")
    })
    public RestResponse<ReissueSalesRewardResp> reissueSalesReward(@Valid @RequestBody final ReissueSalesRewardReq req) {
        Integer userId = userContext.getUserInfo().getUserId();
        String username = Optional.ofNullable(userContext.getUserInfo().getUsername()).orElse("未知用户");
        log.info("用户名:{}, 用户id:{}, 开始处理重新发放动销奖励请求，请求参数: {}", username, userId, req);
        try {
            // 调用服务层处理重新发放逻辑
            final ReissueSalesRewardResp result = salesRewardReissueService.reissueSalesReward(req);

            // 构建响应消息
            final String responseMessage = buildResponseMessage(result);
            log.info("重新发放动销奖励处理完成: {}", responseMessage);

            return RestResponse.success(responseMessage, result);
        } catch (Exception e) {
            final String errorMessage = String.format("重新发放动销奖励失败: %s", e.getMessage());
            log.error(errorMessage, e);
            return RestResponse.error(errorMessage);
        }
    }

    /**
     * 构建响应消息
     * <p>
     * 根据处理结果构建详细的响应消息，包括：
     * - 已存在正常奖励的记录数量和IDs
     * - 已存在异常奖励的记录数量和IDs
     * </p>
     *
     * @param result 处理结果
     * @return 格式化的响应消息
     */
    private String buildResponseMessage(final ReissueSalesRewardResp result) {
        final StringBuilder message = new StringBuilder("重新发放动销奖励");
        boolean hasExistingRecords = false;

        // 处理已存在正常奖励的记录
        if (CollectionUtils.isNotEmpty(result.getExistingRewardIds())) {
            final String existingIds = result.getExistingRewardIds().stream()
                    .map(String::valueOf)
                    .distinct()
                    .collect(Collectors.joining(","));
            message.append(String.format("，发现%d条记录已存在奖励（IDs: %s）",
                    result.getExistingRewardIds().stream().distinct().count(), existingIds));
            hasExistingRecords = true;
        }

        // 处理已存在异常奖励的记录
        if (CollectionUtils.isNotEmpty(result.getExistingExceptionIds())) {
            final String exceptionIds = result.getExistingExceptionIds().stream()
                    .map(String::valueOf)
                    .distinct()
                    .collect(Collectors.joining(","));
            message.append(String.format("%s%d条记录存在异常奖励（IDs: %s）",
                    hasExistingRecords ? "，" : "，发现",
                    result.getExistingExceptionIds().stream().distinct().count(), exceptionIds));
            hasExistingRecords = true;
        }

        // 添加结果提示
        if (hasExistingRecords) {
            message.append("，请检查");
        } else {
            message.append("成功，所有记录均已处理");
        }

        return message.toString();
    }
} 