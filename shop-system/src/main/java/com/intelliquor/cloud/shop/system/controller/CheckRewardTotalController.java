package com.intelliquor.cloud.shop.system.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.intelliquor.cloud.shop.common.exception.BusinessException;
import com.intelliquor.cloud.shop.common.exception.RestResponse;
import com.intelliquor.cloud.shop.common.model.CheckOpenBottleRewardModel;
import com.intelliquor.cloud.shop.common.model.CheckRewardTotalModel;
import com.intelliquor.cloud.shop.common.model.CheckSaleRewardModel;
import com.intelliquor.cloud.shop.common.model.req.CheckRewardDetailsReq;
import com.intelliquor.cloud.shop.common.model.req.CheckRewardTotalReq;
import com.intelliquor.cloud.shop.common.model.req.CheckSaleRewardReq;
import com.intelliquor.cloud.shop.common.model.req.CheckStatisticsReq;
import com.intelliquor.cloud.shop.common.model.resp.CheckRewardTotalResp;
import com.intelliquor.cloud.shop.common.model.resp.CheckSaleRewardResp;
import com.intelliquor.cloud.shop.common.model.resp.CheckStatisticsResp;
import com.intelliquor.cloud.shop.common.service.ICheckRewardTotalService;
import com.intelliquor.cloud.shop.common.utils.DateUtils;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @description 控盘奖励记录记录
 * @createDate 2023-10-24
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("check/reward/total")
public class CheckRewardTotalController {

    private final ICheckRewardTotalService checkRewardTotalService;

    /**
     * 获取控盘奖励统计分页列表
     *
     * @param page  页码
     * @param limit 条数
     * @return 控盘奖励统计分页列表
     */
    @GetMapping("getCheckRewardTotalModelPageList")
    public RestResponse<List<CheckRewardTotalResp>> getCheckRewardTotalModelPageList(@RequestParam(value = "page", defaultValue = "1") int page,
                                                                                     @RequestParam(value = "limit", defaultValue = "10") int limit,
                                                                                     CheckRewardTotalReq checkRewardTotalReq) {
        try {
            PageHelper.startPage(page, limit);
            List<CheckRewardTotalModel> checkRewardTotalModelList = checkRewardTotalService.list(new QueryWrapper<CheckRewardTotalModel>().lambda()
                    .like(StringUtils.isNotBlank(checkRewardTotalReq.getOrderCode()), CheckRewardTotalModel::getOrderCode, checkRewardTotalReq.getOrderCode())
                    .like(StringUtils.isNotBlank(checkRewardTotalReq.getDealerName()), CheckRewardTotalModel::getDealerName, checkRewardTotalReq.getDealerName())
                    .eq(StringUtils.isNotBlank(checkRewardTotalReq.getRewardType()), CheckRewardTotalModel::getRewardType, checkRewardTotalReq.getRewardType())
                    .between(StringUtils.isNotBlank(checkRewardTotalReq.getStartTime()) && StringUtils.isNotBlank(checkRewardTotalReq.getEndTime()), CheckRewardTotalModel::getOrderTime, checkRewardTotalReq.getStartTime(), checkRewardTotalReq.getEndTime())
                    .eq(CheckRewardTotalModel::getDelFlag, false)
                    .orderByDesc(CheckRewardTotalModel::getCreateTime));
            PageInfo<CheckRewardTotalModel> checkRewardTotalPageInfo = new PageInfo<>(checkRewardTotalModelList);

            CheckRewardTotalResp checkRewardTotalResp = checkRewardTotalService.statistics(checkRewardTotalReq);

            checkRewardTotalResp.setCheckRewardTotalModelPageInfo(checkRewardTotalPageInfo);
            return RestResponse.success("查询成功", checkRewardTotalResp);
        } catch (BusinessException e) {
            return RestResponse.error(Integer.parseInt(e.getCode()), e.getMessage());
        }
    }

    @PostMapping("/export")
    public RestResponse<String> export(@RequestBody CheckRewardTotalReq checkRewardTotalReq){
        try{
            checkRewardTotalService.export(checkRewardTotalReq);
            return RestResponse.success("导出成功");
        }catch(BusinessException e){
            return RestResponse.error(Integer.parseInt(e.getCode()),e.getMessage());
        }catch(Exception e){
            return RestResponse.error(e.getMessage());
        }
    }

    @PostMapping("getSaleStatisticsData")
    public RestResponse<CheckStatisticsResp> getSaleStatisticsData(@RequestBody CheckStatisticsReq checkStatisticsReq) {
        try{
            CheckStatisticsResp statisticsData = checkRewardTotalService.findStatisticsData(checkStatisticsReq);
            if (Objects.isNull(statisticsData)) {
                statisticsData = new CheckStatisticsResp();
                statisticsData.setRewardNum(BigDecimal.ZERO);
                statisticsData.setReceivingNum(0);
                statisticsData.setDecapNum(0);
                statisticsData.setAbnormalNum(BigDecimal.ZERO);
            }
            return RestResponse.success("查询成功", statisticsData);
        }catch(BusinessException e){
            return RestResponse.error(Integer.parseInt(e.getCode()),e.getMessage());
        }catch(Exception e){
            return RestResponse.error(e.getMessage());
        }
    }

    @PostMapping("findCheckSaleRewardDetail")
    public RestResponse<List<CheckSaleRewardModel>> findCheckSaleRewardDetail(@RequestBody CheckRewardDetailsReq checkRewardDetailsReq) {
        try{
            List<CheckSaleRewardModel> checkSaleRewardModelList = checkRewardTotalService.findCheckSaleRewardDetail(checkRewardDetailsReq);
            return RestResponse.success("查询成功", new PageInfo<>(checkSaleRewardModelList));
        }catch(BusinessException e){
            return RestResponse.error(Integer.parseInt(e.getCode()),e.getMessage());
        }catch(Exception e){
            return RestResponse.error(e.getMessage());
        }
    }

    @PostMapping("findCheckOpenBottleRewardDetail")
    public RestResponse<List<CheckOpenBottleRewardModel>> findCheckOpenBottleRewardDetail(@RequestBody CheckRewardDetailsReq checkRewardDetailsReq) {
        try{
            List<CheckOpenBottleRewardModel> checkOpenBottleRewardModelList = checkRewardTotalService.findCheckOpenBottleRewardDetail(checkRewardDetailsReq);
            return RestResponse.success("查询成功", new PageInfo<>(checkOpenBottleRewardModelList));
        }catch(BusinessException e){
            return RestResponse.error(Integer.parseInt(e.getCode()),e.getMessage());
        }catch(Exception e){
            return RestResponse.error(e.getMessage());
        }
    }
}
