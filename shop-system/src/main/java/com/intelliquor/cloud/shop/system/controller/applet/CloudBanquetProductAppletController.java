package com.intelliquor.cloud.shop.system.controller.applet;

import com.github.pagehelper.PageInfo;
import com.intelliquor.cloud.shop.common.entity.PageResponse;
import com.intelliquor.cloud.shop.common.model.UserContext;
import com.intelliquor.cloud.shop.system.model.CloudBanquetProductModel;
import com.intelliquor.cloud.shop.system.service.CloudBanquetProductService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 云宴商品相关接口控制器 小程序端调用
 * <AUTHOR>
 * @date 2022-4-26
 */
@RestController
@RequestMapping("/applet/cloud/banquet/product")
public class CloudBanquetProductAppletController {

    @Autowired
    private CloudBanquetProductService cloudBanquetProductService;

    @Autowired
    private UserContext userContext;

    /**
     * @param productName 云宴商品名称
     * @param productCode 云宴商品编码
     * @param productStatus 云宴商品状态
     * <AUTHOR>
     * @date 2022-4-27
     * @return 返回当前公司id下的云宴商品
     */
    @GetMapping("/selectCloudBanquetProduct")
    public PageResponse<PageInfo<CloudBanquetProductModel>> selectCloudBanquetProduct(@RequestParam("productName")String productName,
                                                                                      @RequestParam("productName")String productCode,
                                                                                      @RequestParam("productStatus")Integer productStatus,
                                                                                      @RequestParam(value = "page",defaultValue = "1")Integer page,
                                                                                      @RequestParam(value = "limit",defaultValue = "30")Integer limit){
        //从userContext里获取公司id 用户id
        Integer companyId = userContext.getShopUserModel().getCompanyId();
        return cloudBanquetProductService.selectCloudBanquetProduct(productName,productCode,productStatus,companyId,page,limit);
    }
}
