package com.intelliquor.cloud.shop.system.controller;

import com.github.pagehelper.PageInfo;
import com.intelliquor.cloud.shop.common.exception.BusinessException;
import com.intelliquor.cloud.shop.common.exception.RestResponse;
import com.intelliquor.cloud.shop.common.model.CheckSaleRewardModel;
import com.intelliquor.cloud.shop.common.model.req.CheckOpenBottleRewardReq;
import com.intelliquor.cloud.shop.common.model.req.CheckSaleRewardReq;
import com.intelliquor.cloud.shop.common.model.resp.CheckSaleRewardResp;
import com.intelliquor.cloud.shop.common.service.ICheckSaleRewardService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @description 动销奖励稽核记录
 * @createDate 2023-10-24
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("check/sale/reward")
public class CheckSaleRewardController {

    private final ICheckSaleRewardService checkSaleRewardService;

    /**
     * 获取动销稽核分页列表
     *
     * @param page  页码
     * @param limit 条数
     * @return 动销稽核分页列表
     */
    @GetMapping("getCheckSaleRewardModelPageList")
    public RestResponse<List<CheckSaleRewardResp>> getCheckSaleRewardModelPageList(@RequestParam(value = "page", defaultValue = "1") int page,
                                                                                   @RequestParam(value = "limit", defaultValue = "10") int limit,
                                                                                   CheckSaleRewardReq checkSaleRewardReq) {
        try {
            List<CheckSaleRewardModel> checkSaleRewardModelList = checkSaleRewardService.getPageList(page, limit, checkSaleRewardReq);
            PageInfo<CheckSaleRewardModel> checkSaleRewardPageInfo = new PageInfo<>(checkSaleRewardModelList);

            CheckSaleRewardResp checkSaleRewardResp = checkSaleRewardService.statistics(checkSaleRewardReq);

            checkSaleRewardResp.setCheckSaleRewardPageInfo(checkSaleRewardPageInfo);
            return RestResponse.success("查询成功", checkSaleRewardResp);
        } catch (BusinessException e) {
            return RestResponse.error(Integer.parseInt(e.getCode()), e.getMessage());
        }
    }


    @PostMapping("/export")
    public RestResponse<String> export(@RequestBody CheckSaleRewardReq checkSaleRewardReq){
        try{
            checkSaleRewardService.export(checkSaleRewardReq);
            return RestResponse.success("导出成功");
        }catch(BusinessException e){
            return RestResponse.error(Integer.parseInt(e.getCode()),e.getMessage());
        }catch(Exception e){
            return RestResponse.error(e.getMessage());
        }

    }
}
