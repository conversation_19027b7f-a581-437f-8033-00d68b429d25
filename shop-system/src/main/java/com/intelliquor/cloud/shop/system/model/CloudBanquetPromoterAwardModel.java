package com.intelliquor.cloud.shop.system.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 云宴推介人奖励实体类
 * <AUTHOR>
 * @date 2022-5-19
 */
@Data
public class CloudBanquetPromoterAwardModel {

    /**
     * 主键id
     * */
    private Integer id;

    /**
     * 公司id
     * */
    private Integer companyId;

    /**
     * 终端id
     * */
    private Integer shopId;

    /**
     * 微信id
     * */
    private String openId;

    /**
     * 店长id
     * */
    private Integer shopUserId;

    /**
     * 奖励类型:0单场奖励,1月累计奖励
     * */
    private Integer awardType;

    /**
     * 奖励发放状态:0终端未发放,1终端已发放,2经销商已发放
     * */
    private Integer awardStatus;

    /**
     * 订单号
     * */
    private String orderNo;

    /**
     * 宴席日期:宴席开始时间
     * */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date banquetUseTime;

    /**
     * 推介人姓名
     * */
    private String promoterName;

    /**
     * 推介人手机号
     * */
    private String promoterPhone;

    /**
     * 推介人类型
     * */
    private Integer promoterType;

    /**
     * 创建时间
     * */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 更新时间
     * */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;


}
