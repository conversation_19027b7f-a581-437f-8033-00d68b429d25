package com.intelliquor.cloud.shop.system.dao;
import com.intelliquor.cloud.shop.system.model.RewardGuideScanRecordModel;
import java.util.Map;
import java.util.List;
/**
* 描述：导购奖励扫码记录表 Dao接口
* <AUTHOR>
* @date 2019-09-03
*/
public interface RewardGuideScanRecordDao {


    /**
    * 查询数据信息
    *
    * @param searchMap
    * @return
    */
    List<RewardGuideScanRecordModel> selectList(Map<String, Object> searchMap);

    /**
    * 新增
    *
    * @param model
    * @return
    */
    Integer insert(RewardGuideScanRecordModel model);

    /**
    * 更新
    *
    * @param model
    * @return
    */
    Integer update(RewardGuideScanRecordModel model);

    /**
    * 删除
    *
    * @param id
    * @return
    */
    Integer delete(Integer id);

    /**
    * 根据ID查询
    *
    * @param id
    * @return
    */
    RewardGuideScanRecordModel getById(Integer id);


    RewardGuideScanRecordModel getByCode(String code);

}