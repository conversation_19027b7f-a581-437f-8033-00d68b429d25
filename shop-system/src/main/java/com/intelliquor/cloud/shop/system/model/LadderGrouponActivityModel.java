package com.intelliquor.cloud.shop.system.model;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.intelliquor.cloud.shop.system.model.resp.LadderGrouponDataResp;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
* 描述：阶梯拼团活动实体类
* <AUTHOR>
* @date 2020-08-27
*/
@Data
public class LadderGrouponActivityModel {

    @ApiModelProperty(value = "主键")
    private Integer id;

    @ApiModelProperty(value = "活动名称")
    private String name;

    @ApiModelProperty(value = "商品编码")
    private String goodsCode;

    @ApiModelProperty(value = "商品名称")
    private String goodsName;

    @ApiModelProperty(value = "一件对应多少瓶")
    private Integer standard;

    @ApiModelProperty(value = "商品原价")
    private BigDecimal goodsOriginalPrice;

    @ApiModelProperty(value = "拼团价，用于前端实时展示")
    private BigDecimal goodsGroupPrice;

    @ApiModelProperty(value = "拼团价JSON")
    private String activityPriceJson;

    @ApiModelProperty(value = "拼团价List")
    List<LadderGrouponPriceModel> prices;

    @ApiModelProperty(value = "活动开始时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date beginTime;

    @ApiModelProperty(value = "活动结束时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    @ApiModelProperty(value = "活动开始标志（给前端用）true-开启")
    private boolean beginTimeFlag;

    @ApiModelProperty(value = "展示开始时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date showBeginTime;

    @ApiModelProperty(value = "展示结束时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date showEndTime;

    @ApiModelProperty(value = "开团有效时长(秒)")
    private Long effectiveDuration;

    private String effectiveDurationJson;

    @Data
    public static class EffectiveDurationBody {
        /**
         * 日
         */
        private int days;

        /**
         * 时
         */
        private int hours;

        /**
         * 分
         */
        private int minutes;

    }

    public LadderGrouponActivityModel.EffectiveDurationBody getEffectiveDurationBody() {
        return JSONObject.parseObject(effectiveDurationJson, LadderGrouponActivityModel.EffectiveDurationBody.class);
    }

    public void setEffectiveDurationBody(LadderGrouponActivityModel.EffectiveDurationBody effectiveDurationBody) {
        this.effectiveDurationJson = JSONArray.toJSONString(effectiveDurationBody);
    }

    @ApiModelProperty(value = "是否模拟成团0:否 1:是")
    private Integer isAutoComplete;

    @ApiModelProperty(value = "多少分钟后自动成团")
    private Integer autoCompleteTime;

    @ApiModelProperty(value = "是否限购 0不限制，1限制")
    private Integer isLimit;

    @ApiModelProperty(value = "拼团单次限购数量，默认0件")
    private Integer upperLimitNum;

    @ApiModelProperty(value = "活动整体是否限购 0不限制，1限制")
    private Integer isLimitTotal;

    @ApiModelProperty(value = "活动消费者购买最大数量")
    private Integer totalUpperLimitNum;

    @ApiModelProperty(value = "是否开启库存0:否1:是")
    private Integer hasStock;

    @ApiModelProperty(value = "库存")
    private Integer stock;

    @ApiModelProperty(value = "已成团数量")
    private Integer groupBuyNum;

    @ApiModelProperty(value = "是否开启团长奖励 0：未开启，1开启")
    private Integer isGrouperRebateStatus;

    @ApiModelProperty(value = "团长奖励类型0-实付金额百分比，1-每笔订单X元")
    private Integer grouperRebateType;

    @ApiModelProperty(value = "团长奖励，实付金额的百分比或每笔订单X元")
    private BigDecimal grouperRebate;

    @ApiModelProperty(value = "预计可赚")
    private BigDecimal expectMoney;

    @ApiModelProperty(value = "发货方式 1自提 2邮寄")
    private Integer sendType;

    @ApiModelProperty(value = "邮寄运费")
    private BigDecimal freight;

    @ApiModelProperty(value = "是否开启资金流向 0 不开启，1 开启")
    private Integer moneyFlow;

    @ApiModelProperty(value = "渠道ID")
    private String channel;

    @ApiModelProperty(value = "门店范围JSON")
    private String rangeJson;

    @ApiModelProperty(value = "门店范围LIST")
    List<LadderGrouponActivityRangeModel> ranges;

    @ApiModelProperty(value = "主图")
    private String mainImg;

    @ApiModelProperty(value = "商品详情")
    private String goodsDetail;

    @ApiModelProperty(value = "1有效0无效")
    private Integer status;
    @ApiModelProperty(value = "1有效0无效")
    private String statusDesc;

    @ApiModelProperty(value = "0未删除1已删除")
    private Integer isDelete;

    @ApiModelProperty(value = "商户ID")
    private Integer companyId;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @ApiModelProperty(value = "更新时间，时间戳")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    @ApiModelProperty(value = "服务器时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date serverTime;

    @ApiModelProperty(value = "排序id")
    private Integer sortId;

    @ApiModelProperty(value = "是否通知终端 0否;1是")
    private Integer notice;

    /**TaskModelEnum.LADDERGROUPON
     * 10：阶梯拼团
     */
    private Integer model;

    /**
     * 活动参团情况实体
     */
    private LadderGrouponDataResp ladderGrouponDataResp;

    /**
     * 排序字段默认为id
     */
    private String sortCode = "id";

    /**
     * 排序规则默认为降序排列(DESC/ASC)
     */
    private String sortRole = "DESC";

    /**
     * 操作人
     */
    private Integer createUser;

    @ApiModelProperty(value = "拼团价，云众使用")
    private BigDecimal nextGoodsGroupPrice;

}