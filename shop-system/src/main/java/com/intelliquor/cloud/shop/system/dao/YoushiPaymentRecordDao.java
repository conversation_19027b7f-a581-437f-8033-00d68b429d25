package com.intelliquor.cloud.shop.system.dao;

import com.intelliquor.cloud.shop.system.model.YoushiPaymentRecordModel;
import com.intelliquor.cloud.shop.system.model.excel.YoushiPaymentRecordModelExcel;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 描述：优市订单 Dao接口
 *
 * <AUTHOR>
 * @date 2019-08-06
 */
public interface YoushiPaymentRecordDao {


    /**
     * 查询数据信息
     *
     * @param searchMap
     * @return
     */
    List<YoushiPaymentRecordModel> selectList(Map<String, Object> searchMap);

    /**
     * 景芝大屏-终端扫码明细
     * @param searchMap
     * @return
     */
    List<YoushiPaymentRecordModel> selectjzdpTerminalScanCodeDetailList(Map<String, Object> searchMap);
    long selectjzdpTerminalScanCodeDetailCount(Map<String, Object> searchMap);

    List<YoushiPaymentRecordModelExcel> excelList(Map<String, Object> searchMap);

    /**
     * 新增
     *
     * @param model
     * @return
     */
    Integer insert(YoushiPaymentRecordModel model);

    /**
     * 更新
     *
     * @param model
     * @return
     */
    Integer update(YoushiPaymentRecordModel model);

    /**
     * 删除
     *
     * @param id
     * @return
     */
    Integer delete(Integer id);

    /**
     * 逻辑删除
     *
     * @param id
     * @return
     */
    Integer setDelete(Integer id);

    /**
     * 根据ID查询
     *
     * @param id
     * @return
     */
    YoushiPaymentRecordModel getById(Integer id);

    YoushiPaymentRecordModel getByBalanceId(Integer balanceId);

    int addAmount(@Param("id") Integer id, @Param("payAmount") BigDecimal payAmount);

    YoushiPaymentRecordModel getByTransaction(@Param("transaction") String Transaction);
    YoushiPaymentRecordModel getDetailByTransaction(@Param("transaction") String Transaction);
    YoushiPaymentRecordModel getByTransaction2(@Param("transaction") String Transaction);

    /**
     * 查询云仓订单
     *
     * @param shopId
     * @return
     */
    List<YoushiPaymentRecordModel> selectStorageOrderList(@Param(value = "shopId") Integer shopId);

    /**
     * 查询云仓订单-云商小程序
     *
     * @param dealerName
     * @return
     */
    List<YoushiPaymentRecordModel> selectStorageOrderListForYunshang(@Param(value = "dealerName") String dealerName, @Param(value = "payStatus") Integer payStatus);

    /**
     * 累计进货终端数
     *
     * @param companyId
     * @return
     */
    int selectTotalPurchaseTerminalCount(@Param(value = "companyId") Integer companyId);


    /**
     * 累计进货额
     *
     * @param companyId
     * @return
     */
    BigDecimal selectTotalPurchaseAmount(@Param(value = "companyId") Integer companyId);


    /**
     * 授信开通数
     * @param companyId
     * @return
     */
    int selectTotalOpenCreditCount(@Param(value = "companyId") Integer companyId);

    /**
     * 累计授信额（万元)
     * @param companyId
     * @return
     */
    BigDecimal selectTotalCreditAmount(@Param(value = "companyId") Integer companyId);
}