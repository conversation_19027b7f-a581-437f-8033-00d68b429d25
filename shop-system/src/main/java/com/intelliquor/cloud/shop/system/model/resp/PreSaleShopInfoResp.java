package com.intelliquor.cloud.shop.system.model.resp;

import com.intelliquor.cloud.shop.common.annotation.Excel;
import com.intelliquor.cloud.shop.common.annotation.ExcelTitle;
import com.intelliquor.cloud.shop.common.annotation.LevelExcelTitle;
import com.intelliquor.cloud.shop.common.annotation.Sheet;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2020.10.28
 */
@Excel(fileName = "预售卡终端额度统计列表.xls")
@Sheet(sheetNames = {"预售卡终端额度统计列表"}, numPerSheet = 50000, numOfSheet = {}, groupNumber = 1)
@LevelExcelTitle(titleNames = {"预售卡终端额度统计列表"}, titleLevel =1, colSpans = {6}, groupNumber = 1)
@Data
public class PreSaleShopInfoResp  {

    /**
     * 终端编号
     */
    @ExcelTitle(titleName = "终端编号", index = 1, groupNumber = 1, width = 5000)
    private  Integer  shopId;

    /**
     * 终端名称
     */
    @ExcelTitle(titleName = "终端名称", index = 2, groupNumber = 1, width = 5000)
    private  String shopName;

    /**
     * 手机号
     */
    @ExcelTitle(titleName = "手机号", index = 3, groupNumber = 1, width = 5000)
    private String phone;

    /**
     * 预售卡金额
     */
    @ExcelTitle(titleName = "剩余金额", index =4, groupNumber = 1, width = 5000)
    private BigDecimal preSaleAmount;

    /**
     * 充值金额
     */
    @ExcelTitle(titleName = "充值金额", index =5, groupNumber = 1, width = 5000)
    private BigDecimal rechargeAmount;

    /**
     * 累计使用金额
     */
    @ExcelTitle(titleName = "累计使用金额", index =6, groupNumber = 1, width = 5000)
    private BigDecimal useAmount;



}
