package com.intelliquor.cloud.shop.system.model.req;

import com.intelliquor.cloud.shop.system.model.OrderDownloadCenterModel;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2020.08.19
 * 消费者扫码记录-导出的入参
 */
@Data
public class ConsumerScanCodeRecordExportReq {

    /**
     * 公司Id
     */
    private Integer companyId;

    /**
     * 开始时间
     */
    private String startTime;

    /**
     * 结束时间
     */
    private String  endTime;
    /**
     * 产品编码
     */
    private String goodsCode;
    /**
     * 产品名称
     */
    private String goodsName;


    /**
     * 下载中心实体
     */
    private OrderDownloadCenterModel downloadCenterModel;

    /**
     * 开始出库时间
     */
    private String startOutTime;

    /**
     * 结束出库时间
     */
    private String endOutTime;


    public Integer getCompanyId() {
        return companyId;
    }

    public void setCompanyId(Integer companyId) {
        this.companyId = companyId;
    }

    public String getStartTime() {
        return startTime;
    }

    public void setStartTime(String startTime) {
        this.startTime = startTime;
    }

    public String getEndTime() {
        return endTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }

    public String getGoodsCode() {
        return goodsCode;
    }

    public void setGoodsCode(String goodsCode) {
        this.goodsCode = goodsCode;
    }

    public String getGoodsName() {
        return goodsName;
    }

    public void setGoodsName(String goodsName) {
        this.goodsName = goodsName;
    }

    public OrderDownloadCenterModel getDownloadCenterModel() {
        return downloadCenterModel;
    }

    public void setDownloadCenterModel(OrderDownloadCenterModel downloadCenterModel) {
        this.downloadCenterModel = downloadCenterModel;
    }

    public String getStartOutTime() {
        return startOutTime;
    }

    public void setStartOutTime(String startOutTime) {
        this.startOutTime = startOutTime;
    }

    public String getEndOutTime() {
        return endOutTime;
    }

    public void setEndOutTime(String endOutTime) {
        this.endOutTime = endOutTime;
    }
}
