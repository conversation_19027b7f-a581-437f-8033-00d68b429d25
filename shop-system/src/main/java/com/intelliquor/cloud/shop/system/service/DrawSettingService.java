package com.intelliquor.cloud.shop.system.service;

import com.intelliquor.cloud.shop.system.dao.DrawSettingDao;
import com.intelliquor.cloud.shop.system.model.DrawSettingModel;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 描述：提现设置 服务实现层
 *
 * <AUTHOR>
 * @date 2019-07-24
 */
@Service
public class DrawSettingService {

    @Autowired
    private DrawSettingDao drawSettingDao;

    /**
     * 新增数据
     *
     * @param model
     */
    public void insert(DrawSettingModel model) {
        model.setCreateTime(new Date());
        drawSettingDao.insert(model);
    }

    /**
     * 更新数据
     *
     * @param model
     */
    public void update(DrawSettingModel model) {
        model.setUpdateTime(new Date());
        drawSettingDao.update(model);
    }

    /**
     * 获取提现设置信息
     *
     * @return
     */
    public DrawSettingModel getSetting() {
        return drawSettingDao.getSetting();
    }
}