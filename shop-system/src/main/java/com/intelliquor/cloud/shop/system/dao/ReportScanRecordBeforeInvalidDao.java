package com.intelliquor.cloud.shop.system.dao;
import com.intelliquor.cloud.shop.system.model.ReportScanRecordBeforeInvalidModel;
import java.util.Map;
import java.util.List;
/**
* 描述：产品有效扫码前无效扫码统计表 Dao接口
* <AUTHOR>
* @date 2020-08-28
*/
public interface ReportScanRecordBeforeInvalidDao {


    /**
    * 查询数据信息
    *
    * @param searchMap
    * @return
    */
    List<ReportScanRecordBeforeInvalidModel> selectList(Map<String, Object> searchMap);

    /**
    * 新增
    *
    * @param model
    * @return
    */
    Integer insert(ReportScanRecordBeforeInvalidModel model);

    /**
    * 更新
    *
    * @param model
    * @return
    */
    Integer update(ReportScanRecordBeforeInvalidModel model);

    /**
    * 删除
    *
    * @param id
    * @return
    */
    Integer delete(Integer id);

    /**
     * 删除
     *
     * @param startTime
     * @param endTime
     * @param companyId
     * @return
     */
    Integer deleteByTime(String startTime, String endTime, Integer companyId);

}