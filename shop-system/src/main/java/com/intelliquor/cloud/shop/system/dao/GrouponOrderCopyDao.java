package com.intelliquor.cloud.shop.system.dao;

import com.intelliquor.cloud.shop.system.model.GrouponOrder;
import com.intelliquor.cloud.shop.system.model.GrouponOrderQuery;
import com.intelliquor.cloud.shop.system.model.resp.GrouponOrderPageResp;
import com.intelliquor.cloud.shop.system.model.resp.GrouponOrderSimpleResp;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.Date;
import java.util.List;
import java.util.Map;

public interface GrouponOrderCopyDao {
    int countByExample(GrouponOrderQuery example);

    int deleteByExample(GrouponOrderQuery example);

    int deleteByPrimaryKey(Long id);

    int insert(GrouponOrder record);

    int insertSelective(GrouponOrder record);

    List<GrouponOrder> selectByExample(GrouponOrderQuery example);

    List<GrouponOrderSimpleResp> selectSimpleOneByExample(GrouponOrderQuery example);

    List<GrouponOrderPageResp> mySelectByExample(GrouponOrderQuery example);

    List<GrouponOrderPageResp> selectOrderListByPage(Map<String, Object> searchMap);

    //已结算订单数量
    int selectHaveCompleteOrderCount(@Param("shopId") Integer shopId, @Param("companyId") Integer companyId);

    //待结算订单数量
    int selectWaitAccountOrderCount(@Param("shopId") Integer shopId, @Param("companyId") Integer companyId);
    //成团数
    int selectCompleteGrouponCount(@Param("shopId") Integer shopId, @Param("companyId") Integer companyId);
    //参团用户数
    int selectJoinGrouponCustomerCount(@Param("shopId") Integer shopId, @Param("companyId") Integer companyId);

    //已结算订单列表
    List<GrouponOrderPageResp> selectHaveCompleteOrderList(@Param("shopId") Integer shopId, @Param("companyId") Integer companyId);

    //待结算订单列表
    List<GrouponOrderPageResp> selectWaitAccountOrderList(@Param("shopId") Integer shopId, @Param("companyId") Integer companyId);

    GrouponOrder selectByPrimaryKey(Long id);

    @Select("SELECT * FROM t_groupon_order WHERE order_status = 3 AND date_add( send_time, INTERVAL 7 DAY ) < NOW()")
    List<GrouponOrder> findUnReceivedOrders();

    List<GrouponOrder> selectByOrderCode(String orderCode);

    GrouponOrder selectByOrderCodeAndStatus(String orderCode);

    @Select("SELECT order_code from t_groupon_order where activity_id = #{activityId} and order_status not in (0, 5, 6, 7)")
    List<String> findOrderCodes(@Param("activityId") Integer activityId);

    int updateByExampleSelective(@Param("record") GrouponOrder record, @Param("example") GrouponOrderQuery example);

    int updateByExample(@Param("record") GrouponOrder record, @Param("example") GrouponOrderQuery example);

    int updateByPrimaryKeySelective(GrouponOrder record);

    /**
     * 批量发货
     *
     * @param list
     * @return
     */
    int batchUpdateForDeliver(@Param("list") List<GrouponOrder> list);

    int updateForDeliver(@Param("orderStatus") Integer orderStatus, @Param("sendTime") Date sendTime, @Param("orderCode") String orderCode, @Param("expressCode") String expressCode, @Param("expressCompany") String expressCompany);

    int updateByPrimaryKey(GrouponOrder record);

    @Select("select count(1) from t_groupon_order where groupon_code=#{grouponCode}")
    Integer countByGrouponCode(@Param("grouponCode") String grouponCode);

    /**
     * 查询消费者某个活动的购买商品数量
     *
     * @param searchMap
     * @return
     */
    Long getOrderGoodsCountByOpenId(Map<String, Object> searchMap);

    /**
     * 根据收货方式和订单状态查询订单信息
     *
     * @param searchMap
     * @return
     */
    List<GrouponOrder> selectOrderListByOrderStatus(Map<String, Object> searchMap);
}