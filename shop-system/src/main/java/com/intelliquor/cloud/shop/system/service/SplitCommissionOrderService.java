package com.intelliquor.cloud.shop.system.service;

import com.intelliquor.cloud.shop.system.model.SplitCommissionReq;
import com.intelliquor.cloud.shop.system.model.StorageScanDataStatisticsDetailModel;
import com.intelliquor.cloud.shop.system.model.StorageScanDataStatisticsModel;

import java.util.List;

/**
 * Description: 云仓分佣订单
 *
 * <AUTHOR>
 * @create 2020/5/12 14:45
 */
public interface SplitCommissionOrderService {

    /**
     * 返利对外接口
     * @param req
     */
    String dealSplitAccount(SplitCommissionReq req);

    /**
     * 处理云仓订单返利
     * @param req
     */
    void splitCommissionAccount(SplitCommissionReq req);

    List<StorageScanDataStatisticsDetailModel> dealDataStatisticsForShopId(Integer shopId);

    StorageScanDataStatisticsModel dataStatisticsForShopId(Integer shopId);

}
