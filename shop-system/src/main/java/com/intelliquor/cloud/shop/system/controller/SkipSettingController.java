package com.intelliquor.cloud.shop.system.controller;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.intelliquor.cloud.shop.common.entity.PageResponse;
import com.intelliquor.cloud.shop.common.entity.Response;
import com.intelliquor.cloud.shop.common.exception.BusinessException;
import com.intelliquor.cloud.shop.common.utils.SearchUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.intelliquor.cloud.shop.system.model.SkipSettingModel;
import com.intelliquor.cloud.shop.system.service.SkipSettingService;

import java.util.List;


/**
* 描述：公司用户跳转表控制层
* <AUTHOR>
* @date 2020-09-09
*/
@Api(tags = {"公司用户跳转表操作接口"}, description = "公司用户跳转表操作接口")
@RestController
@RequestMapping("/skipSetting")
public class SkipSettingController {

    @Autowired
    private SkipSettingService skipSettingService;


    @ApiOperation(value = "查询分页信息", notes = "查询分页信息",httpMethod = "GET")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "页码", required = true, paramType = "query", dataType = "int"),
            @ApiImplicitParam(name = "limit", value = "每页条数", required = true, paramType = "query", dataType = "int")
    })
    @RequestMapping(value = "/getListByPage")
    public PageResponse<List<SkipSettingModel>> getListByPage(@RequestParam(value = "page", defaultValue = "1") int page,
                                                             @RequestParam(value = "limit", defaultValue = "10") int limit,
                                                             SkipSettingModel model) {

            PageHelper.startPage(page, limit);
            List<SkipSettingModel> list = skipSettingService.selectList(SearchUtil.getSearch(model));
            PageInfo<SkipSettingModel> pageInfo = new PageInfo<>(list);
            return PageResponse.ok(pageInfo);
    }

    @ApiOperation(value = "查询信息列表", notes = "查询信息列表",httpMethod = "GET")
    @RequestMapping(value = "/getList")
    public Response<List<SkipSettingModel>> getList(SkipSettingModel model) {
            List<SkipSettingModel> list = skipSettingService.selectList(SearchUtil.getSearch(model));
            return Response.ok(list);
    }

    @ApiOperation(value = "查询信息", notes = "查询信息",httpMethod = "GET")
    @RequestMapping(value = "/getSkipSettingInfo")
    public Response<SkipSettingModel> getSkipSetting(SkipSettingModel model) {
        if(model==null||model.getCompanyId()==null) {
            throw new BusinessException("确少companyId");
        }
        SkipSettingModel settingModel= skipSettingService.getByCompanyId(model.getCompanyId());
        return Response.ok(settingModel);
    }

    @ApiOperation(value = "保存信息", notes = "保存信息",httpMethod = "POST")
    @RequestMapping(value = "/save")
    public Response<String> save(SkipSettingModel model) {
            skipSettingService.insert(model);
            return Response.ok("保存成功");
    }

    @ApiOperation(value = "更新信息", notes = "更新信息",httpMethod = "POST")
    @RequestMapping(value = "/update")
    public Response<String> update(SkipSettingModel model) {
            skipSettingService.update(model);
            return Response.ok("更新成功");
    }

    @ApiOperation(value = "删除信息", notes = "删除信息",httpMethod = "GET")
    @RequestMapping(value = "/delete")
    public Response<String> delete(@RequestParam(value = "id") Integer id) {
            skipSettingService.delete(id);
            return Response.ok("删除成功");
    }

    @ApiOperation(value = "根据ID查询信息", notes = "根据ID查询信息",httpMethod = "GET")
    @RequestMapping(value = "/getById")
    public Response<SkipSettingModel> getById(@RequestParam(value = "id") Integer id) {
            SkipSettingModel model = skipSettingService.getById(id);
            return Response.ok(model);
    }
}