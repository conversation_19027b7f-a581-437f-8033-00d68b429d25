package com.intelliquor.cloud.shop.system.controller;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.intelliquor.cloud.shop.common.entity.PageResponse;
import com.intelliquor.cloud.shop.common.entity.Response;
import com.intelliquor.cloud.shop.common.utils.ObjectUtil;
import com.intelliquor.cloud.shop.common.utils.SearchUtil;
import com.intelliquor.cloud.shop.system.model.dto.SaleGoodsInfoModelDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import com.intelliquor.cloud.shop.system.model.SaleGoodsInfoModel;
import com.intelliquor.cloud.shop.system.service.SaleGoodsInfoService;

import java.util.List;


/**
* 描述：出货商品详情控制层
* <AUTHOR>
* @date 2019-10-23
*/
@Api(tags = {"出货商品详情操作接口"}, description = "出货商品详情操作接口")
@RestController
@RequestMapping("/saleGoodsInfo")
public class SaleGoodsInfoController {

    @Autowired
    private SaleGoodsInfoService saleGoodsInfoService;


    @ApiOperation(value = "查询分页信息", notes = "查询分页信息",httpMethod = "GET")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "页码", required = true, paramType = "query", dataType = "int"),
            @ApiImplicitParam(name = "limit", value = "每页条数", required = true, paramType = "query", dataType = "int")
    })
    @RequestMapping(value = "/getListByPage")
    public PageResponse<List<SaleGoodsInfoModel>> getListByPage(@RequestParam(value = "page", defaultValue = "1") int page,
                                                             @RequestParam(value = "limit", defaultValue = "10") int limit,
                                                             SaleGoodsInfoModel model) {
            if(ObjectUtil.isEmpty(model) && ObjectUtil.isEmpty(model.getCompanyId())){
                return PageResponse.pageFail("参数错误");
            }
            PageHelper.startPage(page, limit);
            List<SaleGoodsInfoModel> list = saleGoodsInfoService.selectList(SearchUtil.getSearch(model));
            PageInfo<SaleGoodsInfoModel> pageInfo = new PageInfo<>(list);
            return PageResponse.ok(pageInfo);
    }

    @ApiOperation(value = "查询信息列表", notes = "查询信息列表",httpMethod = "GET")
    @RequestMapping(value = "/getList")
    public Response<List<SaleGoodsInfoModel>> getList(SaleGoodsInfoModel model) {
            List<SaleGoodsInfoModel> list = saleGoodsInfoService.selectList(SearchUtil.getSearch(model));
            return Response.ok(list);
    }

    @ApiOperation(value = "保存信息", notes = "保存信息",httpMethod = "POST")
    @RequestMapping(value = "/save")
    public Response<String> save(@RequestBody SaleGoodsInfoModelDto model) {
            return saleGoodsInfoService.insert(model);
    }

    @ApiOperation(value = "更新信息", notes = "更新信息",httpMethod = "POST")
    @RequestMapping(value = "/update")
    public Response<String> update(@RequestBody SaleGoodsInfoModelDto model) {
            return saleGoodsInfoService.update(model);
    }

    @ApiOperation(value = "删除信息", notes = "删除信息",httpMethod = "GET")
    @RequestMapping(value = "/delete")
    public Response<String> delete(@RequestParam(value = "id") Integer id,@RequestParam(value = "companyId") Integer companyId) {
            saleGoodsInfoService.delete(id,companyId);
            return Response.ok("删除成功");
    }

    @ApiOperation(value = "根据ID查询信息", notes = "根据ID查询信息",httpMethod = "GET")
    @RequestMapping(value = "/getById")
    public Response getById(@RequestParam(value = "id") Integer id,@RequestParam(value = "companyId") Integer companyId) {
        return saleGoodsInfoService.getById(id,companyId);
    }

    @ApiOperation(value = "根据终端和商品查询是否有奖励政策", notes = "根据终端和商品查询是否有奖励政策",httpMethod = "GET")
    @RequestMapping(value = "/queryByShopAndGoodsCode" +
            "")
    public Response queryByShopAndGoodsCode(Integer shopId, String goodsCode, Integer companyId) {
        return saleGoodsInfoService.queryByShopAndGoodsCode( shopId,  goodsCode,  companyId);
    }
}