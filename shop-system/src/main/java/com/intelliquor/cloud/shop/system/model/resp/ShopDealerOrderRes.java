package com.intelliquor.cloud.shop.system.model.resp;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
* 描述：订货单管理实体类
* <AUTHOR>
* @date 2021-03-04
*/
@Data
public class ShopDealerOrderRes {

    @ApiModelProperty(value = "")
    private Integer id;

    @ApiModelProperty(value = "合同编号")
    private String contractCode;

    @ApiModelProperty(value = "经销商编码")
    private String channelCode;

    @ApiModelProperty(value = "分销商编码")
    private String distributionCode;

    /**
     * 分销商子编码
     */
    @ApiModelProperty(value = "分销商子编码")
    private String deputyCode;

    @ApiModelProperty(value = "分销商名称")
    private String distributionApellation;

    @ApiModelProperty(value = "分销商姓名")
    private String distributionName;

    @ApiModelProperty(value = "分销商类型：1公司，2个人")
    private String type;

    /**
     * 品牌类型 1-品牌酒 2-酱酒
     */
    private Integer branchType;

    @ApiModelProperty(value = "分销商电话")
    private String telephone;

    @ApiModelProperty(value = "分销商地址")
    private String address;

    @ApiModelProperty(value = "分销商合同任务量")
    private String taskNum;

    @ApiModelProperty(value = "合同开始日期")
    private String contractBegindate;

    @ApiModelProperty(value = "合同结束日期")
    private String contractEnddate;

    @ApiModelProperty(value = "分销商合同url，多个逗号分隔")
    private String contract;

    @ApiModelProperty(value = "打款凭证,多个逗号分隔")
    private String payVoucher;

    @ApiModelProperty(value = "其他附件,多个逗号分隔")
    private String attachment;

    @ApiModelProperty(value = "分销商所在区域")
    private String localArea;

    @ApiModelProperty(value = "上级类型1-经销商 2-分销商")
    private String higherType;

    @ApiModelProperty(value = "上级编码")
    private String higherCode;

    @ApiModelProperty(value = "团购类型 1-非团购 2-团购")
    private String teamType;

    @ApiModelProperty(value = "联系人")
    private String connectName;

    @ApiModelProperty(value = "创建人")
    private String createOp;

    @ApiModelProperty(value = "")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @ApiModelProperty(value = "修改人")
    private String updateOp;

    @ApiModelProperty(value = "修改时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    @ApiModelProperty(value = "状态 1、有效，2、无效，3、审批中 4、草稿")
    private Integer status;

    @ApiModelProperty(value = "审批时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date auditTime;

    @ApiModelProperty(value = "商户Id")
    private Integer companyId;

    @ApiModelProperty(value = "分销商所在区域省")
    private String province;

    @ApiModelProperty(value = "分销商所在区域-市")
    private String city;

    @ApiModelProperty(value = "分销商所在区域-区县")
    private String district;

    @ApiModelProperty(value = "分销商所在区域街道")
    private String township;

    @ApiModelProperty(value = "门头照照片url,多个逗号分隔")
    private String storesImg;

    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 分销区域，省市区，.分隔
     */
    private String region;
    /**
     * 分销街道
     */
    private String street;
    private String regionStreet;

    /**
     * 营业执照，多个逗号分隔
     */
    private String licenseImg;

    /**
     * 社会信用代码或个人身份证号
     */
    private String legalCode;

    /**
     * 营业执照编码
     */
    private String licenseCode;


    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date showTime;

    /**
     * 区域编码
     */
    private String areaCode;

    /**
     * 合同箱数
     */
    private Integer contractBox;

    /**
     * 首单金额
     */
    private Double firstOrderAmount;

    /**
     * 是否删除
     */
    @ApiModelProperty(value = "0未删除1已删除")
    private Integer isDelete = 0;

    /**
     * 变更前的id
     */
    private Integer beforeChangeId;

    /**
     * 提报经销商名称
     */
    private String dealerName;

    /**
     * 提报经销商手机号
     */
    private String dealerPhone;

}