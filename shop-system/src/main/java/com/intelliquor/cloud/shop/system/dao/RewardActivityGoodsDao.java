package com.intelliquor.cloud.shop.system.dao;
import com.intelliquor.cloud.shop.system.model.RewardActivityGoodsModel;
import org.apache.ibatis.annotations.Param;

import java.util.Map;
import java.util.List;
/**
* 描述：扫码奖励活动-商品 Dao接口
* <AUTHOR>
* @date 2019-07-08
*/
public interface RewardActivityGoodsDao {


    /**
    * 查询数据信息
    *
    * @param searchMap
    * @return
    */
    List<RewardActivityGoodsModel> selectList(Map<String, Object> searchMap);

    List<Integer> selectIdsByActivityId(@Param("activityId") Integer activityId);

    Integer batchDelete(@Param("list") List<Integer> list);

    Integer batchUpdate(@Param("list") List<RewardActivityGoodsModel> list);
    /**
    * 新增
    *
    * @param model
    * @return
    */
    Integer insert(RewardActivityGoodsModel model);

    Integer batchInsert(List<RewardActivityGoodsModel> list);
    /**
    * 更新
    *
    * @param model
    * @return
    */
    Integer update(RewardActivityGoodsModel model);

    /**
    * 删除
    *
    * @param id
    * @return
    */
    Integer delete(Integer id);

    /**
    * 根据ID查询
    *
    * @param id
    * @return
    */
    RewardActivityGoodsModel getById(Integer id);

    List<RewardActivityGoodsModel> getByActivityId(@Param("activityId")Integer activityId);

    String getShortNameByActivityIdAndGoodsName(@Param("activityId")Integer activityId,@Param("goodsCode")String goodsName);

}