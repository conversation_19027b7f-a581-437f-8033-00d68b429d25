package com.intelliquor.cloud.shop.system.controller;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.intelliquor.cloud.shop.common.entity.PageResponse;
import com.intelliquor.cloud.shop.common.entity.Response;
import com.intelliquor.cloud.shop.common.model.UserContext;
import com.intelliquor.cloud.shop.common.utils.SearchUtil;
import com.intelliquor.cloud.shop.system.model.resp.IncomeBreakdownResp;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import com.intelliquor.cloud.shop.system.model.RewardCycleBalanceModel;
import com.intelliquor.cloud.shop.system.service.RewardCycleBalanceService;

import java.util.List;


/**
* 描述：终端周期奖励balance表控制层
* <AUTHOR>
* @date 2019-08-02
*/
@Api(tags = {"终端周期奖励balance表操作接口"}, description = "终端周期奖励balance表操作接口")
@RestController
@RequestMapping("/rewardCycleBalance")
public class RewardCycleBalanceController {

    @Autowired
    private RewardCycleBalanceService rewardCycleBalanceService;

    @Autowired
    private UserContext userContext;

    /**
     * 预期收益列表
     * @param page
     * @param limit
     * @param model
     * @return
     */
    @ApiOperation(value = "查询分页信息", notes = "查询分页信息",httpMethod = "GET")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "页码", required = true, paramType = "query", dataType = "int"),
            @ApiImplicitParam(name = "limit", value = "每页条数", required = true, paramType = "query", dataType = "int")
    })
    @RequestMapping(value = "/getListByPage")
    public PageResponse<List<RewardCycleBalanceModel>> getListByPage(@RequestParam(value = "page", defaultValue = "1") int page,
                                                             @RequestParam(value = "limit", defaultValue = "10") int limit,
                                                             RewardCycleBalanceModel model) {
            if(model == null) {
                model = new RewardCycleBalanceModel();
            }
            if(model.getStatus()==null) {
                model.setStatus(0);
            }
            PageHelper.startPage(page, limit);
            List<RewardCycleBalanceModel> list = rewardCycleBalanceService.selectList(SearchUtil.getSearch(model));
            PageInfo<RewardCycleBalanceModel> pageInfo = new PageInfo<>(list);
            return PageResponse.ok(pageInfo);
    }

    @ApiOperation(value = "查询信息列表", notes = "查询信息列表",httpMethod = "GET")
    @RequestMapping(value = "/getList")
    public Response<List<RewardCycleBalanceModel>> getList(RewardCycleBalanceModel model) {
            List<RewardCycleBalanceModel> list = rewardCycleBalanceService.selectList(SearchUtil.getSearch(model));
            return Response.ok(list);
    }

    @ApiOperation(value = "保存信息", notes = "保存信息",httpMethod = "POST")
    @RequestMapping(value = "/save")
    public Response<String> save(RewardCycleBalanceModel model) {
            rewardCycleBalanceService.insert(model);
            return Response.ok("保存成功");
    }

    @ApiOperation(value = "更新信息", notes = "更新信息",httpMethod = "POST")
    @RequestMapping(value = "/update")
    public Response<String> update(RewardCycleBalanceModel model) {
            rewardCycleBalanceService.update(model);
            return Response.ok("更新成功");
    }

    @ApiOperation(value = "删除信息", notes = "删除信息",httpMethod = "GET")
    @RequestMapping(value = "/delete")
    public Response<String> delete(@RequestParam(value = "id") Integer id) {
            rewardCycleBalanceService.delete(id);
            return Response.ok("删除成功");
    }

    /**
     * 根据ID查询信息
     * @param id
     * @return
     */
    @ApiOperation(value = "根据ID查询信息", notes = "根据ID查询信息",httpMethod = "GET")
    @RequestMapping(value = "/getById")
    public Response<RewardCycleBalanceModel> getById(@RequestParam(value = "id") Integer id) {
            RewardCycleBalanceModel model = rewardCycleBalanceService.getById(id);
            return Response.ok(model);
    }

    /**
     * 获取扫码详情
     * @param id
     * @param companyId
     * @return
     */
    @GetMapping("/getCycleBalanceList")
    public Response<List<IncomeBreakdownResp>> getCycleBalanceList(@RequestParam(value = "id") Integer id,Integer companyId){
            List<IncomeBreakdownResp> list = rewardCycleBalanceService.getCycleBalanceList(id,companyId);
            return Response.ok(list);
        }
}