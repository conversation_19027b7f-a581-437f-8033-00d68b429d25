package com.intelliquor.cloud.shop.system.dao;

import com.intelliquor.cloud.shop.system.model.PaymentCodeModel;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 描述：优市订单扫码 Dao接口
 *
 * <AUTHOR>
 * @date 2020-03-17
 */
public interface PaymentCodeDao {


    /**
     * 查询数据信息
     *
     * @param searchMap
     * @return
     */
    List<PaymentCodeModel> selectList(Map<String, Object> searchMap);

    /**
     * 新增
     *
     * @param model
     * @return
     */
    Integer insert(PaymentCodeModel model);

    /**
     * 更新
     *
     * @param model
     * @return
     */
    Integer update(PaymentCodeModel model);

    /**
     * 删除
     *
     * @param id
     * @return
     */
    Integer delete(Integer id);

    /**
     * 根据ID查询
     *
     * @param id
     * @return
     */
    PaymentCodeModel getById(Integer id);

    /**
     * 根据瓶码查询数据列表
     *
     * @param codeIn
     * @return
     */
    List<PaymentCodeModel> selectListByCodeIn(@Param("codeIn") String codeIn);

    /**
     * 根据箱码查询数据列表
     *
     * @param code
     * @return
     */
    List<PaymentCodeModel> selectListByCode(@Param("code") String code);


    /**
     * 查询数组内的码是否已存在
     *
     * @param codes
     * @return
     */
    List<PaymentCodeModel> selectListByCodes(@Param(value = "codes") String[] codes);

    /**
     * 根据订单编号和商品编号查询应收数量和已扫码数(如果返回空则表明该商品不在入库单内)
     *
     * @param recordId
     * @param goodsCode
     * @return
     */
    Map<String, Object> selectOrderScanCount(@Param(value = "recordId") Integer recordId, @Param(value = "goodsCode") String goodsCode);

    /**
     * 根据订单编号查询扫码情况
     *
     * @param recordId
     * @return
     */
    List<Map<String, Object>> selectScanCountByOrderCode(@Param(value = "recordId") Integer recordId);

    /**
     * 根据订单编号查询扫码情况
     *
     * @param orderId
     * @return
     */
    List<Map<String, Object>> selectScanCountByOrderId(@Param(value = "orderId") Integer orderId);

    /**
     * 根据订单ID删除扫码信息
     *
     * @param orderId
     * @return
     */
    Integer deleteByOrderId(@Param(value = "orderId") Integer orderId);
}