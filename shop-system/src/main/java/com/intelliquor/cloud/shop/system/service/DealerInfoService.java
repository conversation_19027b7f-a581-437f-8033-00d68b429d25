package com.intelliquor.cloud.shop.system.service;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.intelliquor.cloud.shop.common.constant.CommonConstant;
import com.intelliquor.cloud.shop.common.dao.*;
import com.intelliquor.cloud.shop.common.enums.AccountTypeEnum;
import com.intelliquor.cloud.shop.common.enums.TerminalRewardRecordSourceEnum;
import com.intelliquor.cloud.shop.common.model.*;
import com.intelliquor.cloud.shop.common.model.vo.*;
import com.intelliquor.cloud.shop.common.utils.CodeConstant;
import com.intelliquor.cloud.shop.system.dao.DealerAreaDao;
import com.intelliquor.cloud.shop.system.dao.DealerInfoDao;
import com.intelliquor.cloud.shop.system.dao.ShopDealerOrderDao;
import com.intelliquor.cloud.shop.system.model.DealerAreaModel;
import com.intelliquor.cloud.shop.system.model.DealerInfoModel;
import com.intelliquor.cloud.shop.system.model.TerminalShopLevelModel;
import com.intelliquor.cloud.shop.system.service.gt.ShopDealerOrderService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 描述：经销商信息 服务实现层
 *
 * <AUTHOR>
 * @date 2019-11-21
 */
@Service
@Slf4j
public class DealerInfoService {

    @Autowired
    private DealerInfoDao dealerInfoDao;

    @Autowired
    private DealerAreaDao dealerAreaDao;
    @Autowired
    ShopDao shopDao;
    @Autowired
    private TerminalScanDetailCommonDao terminalScanDetailCommonDao;
    @Autowired
    private ShopDealerOrderDao shopDealerOrderDao;
    @Resource
    private ShopDealerOrderService shopDealerOrderService;
    @Autowired
    private TerminalShopCommonDao terminalShopCommonDao;
    @Autowired
    private ITerminalShopLevelService terminalShopLevelService;
    @Autowired
    private TerminalRewardRecordNewDao terminalRewardRecordNewDao;
    @Autowired
    private TerminalShopContractCommonDao terminalShopContractCommonDao;
    @Autowired
    private DealerInfoCommonDao dealerInfoCommonDao;

    @Autowired
    private TerminalShopMergeDao terminalShopMergeDao;

    /**
     * 查询数据
     *
     * @return
     */
    public List<DealerInfoModel> selectList(Map<String, Object> searchMap) {
        return dealerInfoDao.selectList(searchMap);
    }


    /**
     * 新增数据
     *
     * @param model
     */
    @Transactional(rollbackFor = Exception.class)
    public void insert(DealerInfoModel model) {
        model.setIsDelete(0);
        dealerInfoDao.insert(model);
        List<DealerAreaModel> list = JSON.parseArray(model.getAreaJson(), DealerAreaModel.class);
        for (DealerAreaModel area : list) {
            area.setDealerId(model.getId());
            dealerAreaDao.insert(area);
        }
    }

    /**
     * 添加经销商
     *
     * @param model
     */
    @Transactional(rollbackFor = Exception.class)
    public void insertDealer(DealerInfoModel model) {
        model.setIsDelete(0);
        dealerInfoDao.insert(model);
    }

    /**
     * 添加经销商区域
     *
     * @param areaJson
     */
    @Transactional(rollbackFor = Exception.class)
    public void insertDealerArea(String dealerCode, String areaJson) {
        List<DealerAreaModel> list = JSON.parseArray(areaJson, DealerAreaModel.class);
        for (DealerAreaModel area : list) {
            area.setDealerCode(dealerCode);
            dealerAreaDao.insert(area);
        }
    }


    /**
     * 更新数据
     *
     * @param model
     */
    @Transactional(rollbackFor = Exception.class)
    public void update(DealerInfoModel model) {
        dealerInfoDao.update(model);
        //删除旧的区域信息
        dealerAreaDao.deleteByDealerId(model.getId());
        //保存新的区域信息
        List<DealerAreaModel> list = JSON.parseArray(model.getAreaJson(), DealerAreaModel.class);
        for (DealerAreaModel area : list) {
            area.setDealerId(model.getId());
            area.setDealerCode(model.getDealerCode());
            dealerAreaDao.insert(area);
        }
    }

    /**
     * 删除数据
     *
     * @param id
     */
    public void delete(Integer id) {
        dealerInfoDao.delete(id);
    }

    /**
     * 根据ID查询数据
     *
     * @param id
     */
    public DealerInfoModel getById(Integer id) {
        return dealerInfoDao.getById(id);
    }

    /**
     * 根据区域和经销商编码查询
     *
     * @param searchMap
     * @return
     */
    public DealerAreaModel selectByParam(Map<String, Object> searchMap) {
        return dealerInfoDao.selectByParam(searchMap);
    }

    /**
     * 根据经销商编码查询
     *
     * @param dealerCode
     * @return
     */
    public List<DealerAreaModel> getByDealerCode(String dealerCode) {
        return dealerAreaDao.getByDealerCode(dealerCode);
    }

    /**
     * 分销商获取下级终端列表
     *
     * @param searchMap
     * @return
     */
    public List<ShopModel> getTerminalForDistributor(Map<String, Object> searchMap) {
        Integer accountType = (Integer) searchMap.get("accountType");
        List<ShopModel> terminalForDistributor = new ArrayList<>();
        if(accountType == AccountTypeEnum.account_type_three.getType()){
            terminalForDistributor = shopDao.getTerminalForDistributorNew(searchMap);
        }else if(accountType == AccountTypeEnum.account_type_four.getType()){
            terminalForDistributor = shopDao.getTerminalForDistributor(searchMap);
        }

        if(terminalForDistributor.isEmpty()){
            return terminalForDistributor;
        }
        List<Integer> shopIds = terminalForDistributor.stream().map(item->item.getId()).collect(Collectors.toList());
        List<Integer> mergeIds = terminalForDistributor.stream().map(item->item.getMergeId()).collect(Collectors.toList());
        // 获取进货总数
        List<ShopStatisticsVo> scanCount = shopDao.getScanCount(shopIds);
        Map<Integer, Integer> scanCountMap = scanCount.stream().collect(Collectors.toMap(ShopStatisticsVo::getShopId,ShopStatisticsVo::getScanCount));
        // 获取开瓶数
        List<ShopStatisticsVo> newScanTime = shopDao.getNewScanTime(shopIds);
        Map<Integer, Date> newScanTimeMap = newScanTime.stream().collect(Collectors.toMap(ShopStatisticsVo::getShopId,ShopStatisticsVo::getNewScanTime));
        // 获取最后进货时间
        List<ShopStatisticsVo> openCount = shopDao.getOpenCount(shopIds);
        Map<Integer, Integer> openCountMap = openCount.stream().collect(Collectors.toMap(ShopStatisticsVo::getShopId,ShopStatisticsVo::getOpenCount));
        // 获取终端等级
        List<TerminalShopLevelModel> shopLevelModelList = terminalShopLevelService.list();
        Map<Long, String> shopLevelMap = shopLevelModelList.stream().collect(Collectors.toMap(TerminalShopLevelModel::getId,TerminalShopLevelModel::getLevelName));

        // 获取主店信息
        QueryWrapper<TerminalShopCommonModel> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("id", mergeIds);
        List<TerminalShopCommonModel> terminalShopCommonModels = terminalShopCommonDao.selectList(queryWrapper);
        Map<Integer, String> terminalShopMap = terminalShopCommonModels.stream().collect(Collectors.toMap(TerminalShopCommonModel::getId, TerminalShopCommonModel::getShopName));
        // 设置每个终端的进货总数、最后进货时就、开瓶数
        terminalForDistributor.forEach(item ->{
            if(scanCountMap.containsKey(item.getId())){
                item.setScanCount(scanCountMap.get(item.getId()));
            }
            if(newScanTimeMap.containsKey(item.getId())){
                item.setNewScanTime(newScanTimeMap.get(item.getId()));
            }
            if(openCountMap.containsKey(item.getId())){
                item.setOpenCount(openCountMap.get(item.getId()));
            }
            if(StrUtil.isNotBlank(item.getLevelCode()) && shopLevelMap.containsKey(Long.valueOf(item.getLevelCode()))){
                item.setLevelName(shopLevelMap.get(Long.valueOf(item.getLevelCode())));
            }
            if(item.getMergeId()!=null && terminalShopMap.containsKey(item.getMergeId())){
                item.setMergeShopName(terminalShopMap.get(item.getMergeId()));
            }
        });

        return terminalForDistributor;
    }

    /**
     * 终端用户获取自己的终端列表
     * @param searchMap
     * @return
     */
    public List<ShopModel> getTerminalForUser(Map<String, Object> searchMap) {
        List<ShopModel> terminalForDistributor = shopDao.getTerminalForUser(searchMap);
        if(terminalForDistributor.isEmpty()){
            return terminalForDistributor;
        }
        List<Integer> shopIds = terminalForDistributor.stream().map(item->item.getId()).collect(Collectors.toList());
        List<Integer> mergeIds = terminalForDistributor.stream().map(item->item.getMergeId()).collect(Collectors.toList());
        // 获取进货总数
        List<ShopStatisticsVo> scanCount = shopDao.getScanCount(shopIds);
        Map<Integer, Integer> scanCountMap = scanCount.stream().collect(Collectors.toMap(ShopStatisticsVo::getShopId,ShopStatisticsVo::getScanCount));
        // 获取开瓶数
        List<ShopStatisticsVo> newScanTime = shopDao.getNewScanTime(shopIds);
        Map<Integer, Date> newScanTimeMap = newScanTime.stream().collect(Collectors.toMap(ShopStatisticsVo::getShopId,ShopStatisticsVo::getNewScanTime));
        // 获取最后进货时间
        List<ShopStatisticsVo> openCount = shopDao.getOpenCount(shopIds);
        Map<Integer, Integer> openCountMap = openCount.stream().collect(Collectors.toMap(ShopStatisticsVo::getShopId,ShopStatisticsVo::getOpenCount));
        // 获取终端等级
        List<TerminalShopLevelModel> shopLevelModelList = terminalShopLevelService.list();
        Map<Long, String> shopLevelMap = shopLevelModelList.stream().collect(Collectors.toMap(TerminalShopLevelModel::getId,TerminalShopLevelModel::getLevelName));
        // 获取主店信息
        QueryWrapper<TerminalShopCommonModel> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("id", mergeIds);
        List<TerminalShopCommonModel> terminalShopCommonModels = terminalShopCommonDao.selectList(queryWrapper);
        Map<Integer, String> terminalShopMap = terminalShopCommonModels.stream().collect(Collectors.toMap(TerminalShopCommonModel::getId, TerminalShopCommonModel::getShopName));
        // 设置每个终端的进货总数、最后进货时就、开瓶数
        terminalForDistributor.forEach(item ->{
            if(scanCountMap.containsKey(item.getId())){
                item.setScanCount(scanCountMap.get(item.getId()));
            }
            if(newScanTimeMap.containsKey(item.getId())){
                item.setNewScanTime(newScanTimeMap.get(item.getId()));
            }
            if(openCountMap.containsKey(item.getId())){
                item.setOpenCount(openCountMap.get(item.getId()));
            }
            if(StrUtil.isNotBlank(item.getLevelCode()) && shopLevelMap.containsKey(Long.valueOf(item.getLevelCode()))){
                item.setLevelName(shopLevelMap.get(Long.valueOf(item.getLevelCode())));
            }
            if(item.getMergeId()!=null && terminalShopMap.containsKey(item.getMergeId())){
                item.setMergeShopName(terminalShopMap.get(item.getMergeId()));
            }
        });

        return terminalForDistributor;
    }

    /**
     * 分销商获取下级终端列表
     *
     * @param commpanyId
     * @param shopId
     * @return
     */
    public ShopModel getTerminalInfo(Integer commpanyId, Integer shopId) {
        ShopModel shop = shopDao.getTerminalInfo(shopId);
        // 根据订单统计进货次数和进货瓶数
        ShopModel purchase = shopDao.getPurchase(commpanyId, shopId);
        shop.setScanCodeBatchNum(purchase.getScanCodeBatchNum());
        shop.setScanCodeBottleNum(purchase.getScanCodeBottleNum());

        // 最近一次进货时间，根据扫码进货记录确定
        Map<String, Object> lastSearchMap = new HashMap<>();
        lastSearchMap.put("shopId", shopId);
        TerminalScanDetailModel scanDetailModel = terminalScanDetailCommonDao.getLastScanDetail(lastSearchMap);
        if (scanDetailModel != null) {
            shop.setNewScanTime(scanDetailModel.getCreateTime());
        }

        // 进货信息列表
        List<ShopModel.Goods> goodsList = shopDao.getPurchaseGoods(commpanyId, shopId);
        shop.setGoodsList(goodsList);
        return shop;
    }



    public TerminalShopVo getTerminalInfoVo(Integer shopId) {
        TerminalShopVo terminalShopVo = new TerminalShopVo();
        LambdaQueryWrapper<TerminalShopCommonModel> lqw = Wrappers.lambdaQuery();
        lqw.eq(TerminalShopCommonModel::getMemberShopId, shopId);
        lqw.last(" LIMIT 1");
        TerminalShopCommonModel terminalShopCommonModel = terminalShopCommonDao.selectOne(lqw);
        BeanUtils.copyProperties(terminalShopCommonModel, terminalShopVo);
        terminalShopVo.setTerminalShopId(terminalShopCommonModel.getId());
        terminalShopVo.setHasMerge(false);
        Integer mergeType = terminalShopCommonModel.getMergeType();
        if(mergeType == CommonConstant.TERMINAL_MERGE_TYPE){//子店
            terminalShopVo.setHasMerge(true);
            LambdaQueryWrapper<TerminalShopMergeModel> mergeLqw = Wrappers.lambdaQuery();
            mergeLqw.eq(TerminalShopMergeModel::getFromId, terminalShopCommonModel.getId());
            mergeLqw.orderByDesc(TerminalShopMergeModel::getCreateTime);
            mergeLqw.last(" LIMIT 1");
            TerminalShopMergeModel terminalShopMergeModel = terminalShopMergeDao.selectOne(mergeLqw);
            if(Objects.nonNull(terminalShopMergeModel)){
                terminalShopVo.setMergeDate(terminalShopMergeModel.getCreateTime());
            }

            LambdaQueryWrapper<TerminalRewardRecordModel> recordLqw = Wrappers.lambdaQuery();
            recordLqw.eq(TerminalRewardRecordModel::getSource, TerminalRewardRecordSourceEnum.PERSONALIZED_QUARTERLY_REWARD.getCode());
            recordLqw.eq(TerminalRewardRecordModel::getType, 2);
            recordLqw.eq(TerminalRewardRecordModel::getIsDelete, 0);
            recordLqw.orderByDesc(TerminalRewardRecordModel::getCreateTime);
            recordLqw.last(" LIMIT 1");
            TerminalRewardRecordModel recordModel = terminalRewardRecordNewDao.selectOne(recordLqw);
            if(Objects.nonNull(recordModel)){
                terminalShopVo.setMergeScore(recordModel.getAmount().abs());
            }else{
                terminalShopVo.setMergeScore(BigDecimal.ZERO);
            }
            TerminalShopCommonModel mergeTerminalShop = terminalShopCommonDao.selectById(terminalShopCommonModel.getMergeId());
            if(Objects.nonNull(mergeTerminalShop)){
                String shopName = mergeTerminalShop.getShopName();
                terminalShopVo.setMergeShopName(shopName);
            }
            String mergeDesc = terminalShopVo.getShopName();
            if(terminalShopVo.getMergeScore().compareTo(BigDecimal.ZERO) >0){
                mergeDesc = mergeDesc.concat("将剩余积分[").concat(terminalShopVo.getMergeScore()+"]");
            }
            mergeDesc = mergeDesc.concat("合并入").concat(terminalShopVo.getMergeShopName());

            terminalShopVo.setMergeDesc(mergeDesc);
        }

        //合同信息
        LambdaQueryWrapper<TerminalShopContractCommonModel> lqwc = Wrappers.lambdaQuery();
        lqwc.eq(TerminalShopContractCommonModel::getTerminalShopId, terminalShopCommonModel.getId());
        lqwc.last(" LIMIT 1");
        TerminalShopContractCommonModel contractCommonModel = terminalShopContractCommonDao.selectOne(lqwc);
        if(Objects.nonNull(contractCommonModel)){
            Integer contractType = contractCommonModel.getContractType();
            if(Objects.nonNull(contractType)){
                String name = CodeConstant.CONTRACT_TYPE_MAP.get(contractType);
                terminalShopVo.setContractTypeName(name);
                terminalShopVo.setContractType(contractType);
            }
            terminalShopVo.setContractCode(contractCommonModel.getContractCode());
            //经销商信息
            String dealerCode = contractCommonModel.getDealerCode();
            CloudDealerInfoModel dealerInfo = dealerInfoCommonDao.getDealerInfoByDealerCode(dealerCode);
            if(Objects.nonNull(dealerInfo)){
                terminalShopVo.setDealerName(dealerInfo.getDealerName());
            }
        }

        //协议信息
        TerminalShopVo terminalProtocol = terminalShopCommonDao.getTerminalProtocol(contractCommonModel.getTerminalShopId());
        if(Objects.nonNull(terminalProtocol)){
            terminalShopVo.setProtocolProperty(terminalProtocol.getProtocolProperty());
            terminalShopVo.setLevelCode(terminalProtocol.getLevelCode());
            terminalShopVo.setLevelName(terminalProtocol.getLevelName());
            terminalShopVo.setProductType(terminalProtocol.getProductType());
            terminalShopVo.setEffectiveTime(terminalProtocol.getEffectiveTime());
            terminalShopVo.setFailureTime(terminalProtocol.getFailureTime());
            terminalShopVo.setProtocolImage(terminalProtocol.getProtocolImage());
            List<String> protocolImageList = new ArrayList<>();
            if(StringUtils.isNotEmpty(terminalProtocol.getProtocolImage())){
                String[] split = terminalProtocol.getProtocolImage().split(",");
                Collections.addAll(protocolImageList, split);
            }
            terminalShopVo.setProtocolImageList(protocolImageList);
            //协议产品
            if(Objects.nonNull(terminalProtocol.getProductProtocolConfigId()) && terminalProtocol.getProductProtocolConfigId() != 0){
                TerminalProtocolProductVo protocolProductVo = terminalShopCommonDao.getProtocolProduct(terminalProtocol.getProductProtocolConfigId());
                terminalShopVo.setProtocolProductVo(protocolProductVo);
            }
        }


        // 收货明细
        List<ReceivedGoodsDetailVo> qtyNumDetailList = shopDao.getQtyNumDetailList(shopId);
        List<ReceivedGoodsDetailVo> receivedNumDetailList = shopDao.getReceivedNumDetailList(shopId);
        // 订单数据转成map方便后面的处理
        Map<String, ReceivedGoodsDetailVo> qtyMap = qtyNumDetailList.stream().collect(Collectors.toMap(ReceivedGoodsDetailVo::getGoodsCode, Function.identity()));

        // 收货数据和订单数据合并
        for (ReceivedGoodsDetailVo goodsDetailVo : receivedNumDetailList) {
            String goodsCode = goodsDetailVo.getGoodsCode();
            if(Objects.isNull(goodsCode)){
                continue;
            }
            if (qtyMap.containsKey(goodsCode)){
                ReceivedGoodsDetailVo vo = qtyMap.get(goodsCode);
                // 设置已收数量
                vo.setReceivedNum(goodsDetailVo.getReceivedNum());
                // 设置待收数量
                vo.setStayNum(vo.getQty() - vo.getReceivedNum());
            }else {
                ReceivedGoodsDetailVo vo = new ReceivedGoodsDetailVo();
                vo.setGoodsCode(goodsCode);
                vo.setGoodsName(goodsDetailVo.getGoodsName());
                // 设置订单数量
                vo.setQty(0);
                // 设置已收数量
                vo.setReceivedNum(goodsDetailVo.getReceivedNum());
                // 设置待收数量
                vo.setStayNum(vo.getQty() - vo.getReceivedNum());
                qtyMap.put(goodsCode, vo);
            }
        }

        // 处理数据
        List<ReceivedGoodsDetailVo> receivedGoodsDetailList = qtyMap.values().stream().collect(Collectors.toList());
        terminalShopVo.setReceivedGoodsDetailList(receivedGoodsDetailList);

        //开瓶明细
        List<OpenBottleDetailVo> openBottleDetailList = shopDao.getOpenBottleDetailList(shopId);
        for (OpenBottleDetailVo openBottleDetailVo : openBottleDetailList) {
            String goodsCode = openBottleDetailVo.getGoodsCode();
            Integer bottleNum = openBottleDetailVo.getBottleNum();
            ReceivedGoodsDetailVo receivedGoodsDetailVo = receivedGoodsDetailList.stream().filter(e -> goodsCode.equals(e.getGoodsCode())).findFirst().orElse(null);
            if(Objects.nonNull(receivedGoodsDetailVo)){
                Integer receivedNum = receivedGoodsDetailVo.getReceivedNum();
                log.info("{},开瓶数:{},收货数:{}", goodsCode,bottleNum,receivedNum);
                if(receivedNum == 0){
                    openBottleDetailVo.setBottleRate("100%");
                }else{
                    Double val1 = Double.valueOf(bottleNum);
                    Double val2 = Double.valueOf(receivedNum);
                    Double rate = val1/val2;
                    openBottleDetailVo.setBottleRate(String.format("%.2f",rate*100)+"%");
                }
            }else{
                openBottleDetailVo.setBottleRate("100%");
            }
        }
        terminalShopVo.setOpenBottleDetailList(openBottleDetailList);

        //积分
        ShopModel shop = shopDao.getTerminalInfo(shopId);
        // 剩余积分
        terminalShopVo.setRemainderScore(shop.getVirtualAmount());
        terminalShopVo.setCreateTime(shop.getCreateTime());
        // 对应member_shop的记录,处理终端状态
        terminalShopVo.setStatus(shop.getStatus());
        // 获得积分
        BigDecimal gainScore = terminalRewardRecordNewDao.getGainScore(shopId);
        terminalShopVo.setGainScore(gainScore);
        if(Objects.isNull(gainScore)){
            gainScore = BigDecimal.ZERO;
        }
        // 已使用积分
        BigDecimal useScore = terminalRewardRecordNewDao.getUseScore(shopId);
        if(Objects.isNull(useScore)){
            useScore = BigDecimal.ZERO;
        }
        terminalShopVo.setUseScore(useScore);
        return terminalShopVo;
    }

    public List<ShopDealerOrderModel> selectShopDealerOrderList(Map<String, Object> searchMap) {
        return shopDealerOrderDao.selectShopDealerOrderList(searchMap);
    }


    /**
     * 查询7天后的订单，如果未发货，标记为自动取消数据
     */
    @Transactional(rollbackFor = Exception.class)
    public void autoCancel() {
        log.info("开始标记超时未发货订单..");
         //查询7天后的未发货订单
        LocalDateTime localDateTime = LocalDateTime.now();
        //计算7天后的过期时间
        Date expireDate = Date.from(localDateTime.minusDays(7).atZone(ZoneId.systemDefault()).toInstant());
        //查询过期时间内所有的未收货订单
        List<ShopDealerOrderModel> list = shopDealerOrderDao.selectExpireUnreceivedOrder(expireDate);
        if(CollectionUtils.isEmpty(list)){
            return;
        }
        List<Long> idList = list.stream().map(ShopDealerOrderModel::getId).collect(Collectors.toList());
        //批量更新订单自动取消标记
        shopDealerOrderDao.batchAutoCancel(idList,1);
        log.info("标记超时未发货订单已完成");
    }

    /**
     * 自动取消订单
     */
    public void autoCancelDeal() {
        log.info("开始自动取消订单逻辑处理,请求溯源..");
        //每次处理10份订单
        List<ShopDealerOrderModel> list = shopDealerOrderDao.selectAutoCancelOrder();
        if(CollectionUtils.isEmpty(list)){
            return;
        }
        for (ShopDealerOrderModel item : list) {
            try {
                shopDealerOrderService.cancel(item.getId(),null);
            }catch (Exception e){
                shopDealerOrderDao.updateAutoCancel(item.getId(),2);
                log.info("自动取消订单失败,订单id:{}",item.getId());
            }
        }


        log.info("结束自动取消订单逻辑处理,请求溯源..");
    }
}
