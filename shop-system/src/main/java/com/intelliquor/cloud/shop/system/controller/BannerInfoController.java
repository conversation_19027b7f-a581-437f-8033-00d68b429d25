package com.intelliquor.cloud.shop.system.controller;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.intelliquor.cloud.shop.common.annotation.SLog;
import com.intelliquor.cloud.shop.common.aop.Log;
import com.intelliquor.cloud.shop.common.entity.PageResponse;
import com.intelliquor.cloud.shop.common.entity.Response;
import com.intelliquor.cloud.shop.common.exception.BusinessException;
import com.intelliquor.cloud.shop.common.utils.ObjectUtil;
import com.intelliquor.cloud.shop.common.utils.SearchUtil;
import com.intelliquor.cloud.shop.system.model.BannerInfoModel;
import com.intelliquor.cloud.shop.system.service.BannerInfoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;


import java.util.List;
import java.util.Map;


/**
* 描述：广告管理控制层
* <AUTHOR>
* @date 2019-07-05
*/
@Api(tags = {"广告管理操作接口"}, description = "广告管理操作接口")
@RestController
@RequestMapping("/system/bannerInfo")
public class BannerInfoController {

    private static final Logger log = LoggerFactory.getLogger(BannerInfoController.class);


    @Autowired
    private BannerInfoService bannerInfoService;


    @SLog("查询广告")
    @ApiOperation(value = "查询分页信息", notes = "查询分页信息",httpMethod = "GET")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "页码", required = true, paramType = "query", dataType = "int"),
            @ApiImplicitParam(name = "limit", value = "每页条数", required = true, paramType = "query", dataType = "int")
    })
    @Log
    @RequestMapping(value = "/getListByPage")
    public PageResponse<List<BannerInfoModel>> getListByPage(@RequestParam(value = "page", defaultValue = "1") int page,
                                                             @RequestParam(value = "limit", defaultValue = "10") int limit,
                                                             BannerInfoModel model) {

            PageHelper.startPage(page, limit);
            List<BannerInfoModel> list = bannerInfoService.selectList(SearchUtil.getSearch(model));
            PageInfo<BannerInfoModel> pageInfo = new PageInfo<>(list);
            return PageResponse.ok(pageInfo);
    }
    @Log
    @ApiOperation(value = "查询信息列表", notes = "查询信息列表",httpMethod = "GET")
    @RequestMapping(value = "/getList")
    public Response<List<BannerInfoModel>> getList(BannerInfoModel model) {
            List<BannerInfoModel> list = bannerInfoService.selectList(SearchUtil.getSearch(model));
            return Response.ok(list);
    }

    /**
     * 小程序消息中心列表
     * @param shopId
     * @param companyId
     * @return
     */
    @RequestMapping(value = "/findMsgList")
    public PageResponse<List<BannerInfoModel>> findList(@RequestParam(value = "page", defaultValue = "1") int page,
                                                    @RequestParam(value = "limit", defaultValue = "10") int limit,
                                                    Integer shopId, Integer companyId) {
        PageHelper.startPage(page, limit);
        List<BannerInfoModel> list = bannerInfoService.findMsgList(shopId, companyId);
        PageInfo<BannerInfoModel> pageInfo = new PageInfo<>(list);
        return PageResponse.ok(pageInfo);
    }

    /**
     * 清空消息
     * @param shopId
     * @param companyId
     * @return
     */
    @PutMapping(value = "/deleteList")
    public Response<String> deleteList(@RequestParam(value = "shopId") Integer shopId, @RequestParam(value = "companyId") Integer companyId) {
        bannerInfoService.deleteList(shopId, companyId);
        return Response.ok("清空成功");
    }

    @Log
    @SLog("添加广告")
    @ApiOperation(value = "保存信息", notes = "保存信息",httpMethod = "POST")
    @RequestMapping(value = "/save")
    public Response<String> save(@RequestBody BannerInfoModel model) {
        if(ObjectUtil.hasEmpty(model,model.getTitle(),model.getStartTime(),model.getEndTime())){
            return Response.fail("参数错误");
        }
        if(model.getStartTime().after(model.getEndTime())){
            return Response.fail("结束时间不能在开始时间之前");
        }
        Response<String> response = new Response<>();
        try {
            bannerInfoService.insert(model);
            response.setResult("保存成功");
        } catch (Exception e) {
            log.error("保存banner广告信息异常！原因：{}", e.getStackTrace());
            e.printStackTrace();
            response.setError(e.getMessage());
        }
        return response;
    }
    @Log
    @SLog("修改广告")
    @ApiOperation(value = "更新信息", notes = "更新信息",httpMethod = "POST")
    @RequestMapping(value = "/update")
    public Response<String> update(@RequestBody BannerInfoModel model) {
        if(ObjectUtil.hasEmpty(model,model.getId())){
            return Response.fail("参数错误");
        }
        if(!ObjectUtil.hasEmpty(model.getStartTime(),model.getEndTime()) && model.getStartTime().after(model.getEndTime())){
            return Response.fail("结束时间不能在开始时间之前");
        }
        Response<String> response = new Response<>();
        try {
            bannerInfoService.update(model);
            response.setResult("更新成功");
        } catch (Exception e) {
            log.error("更新banner广告信息异常！原因：{}", e.getStackTrace());
            e.printStackTrace();
            response.setError(e.getMessage());
        }
        return response;
    }
    @Log
    @SLog("删除广告")
    @ApiOperation(value = "删除信息", notes = "删除信息",httpMethod = "GET")
    @RequestMapping(value = "/delete")
    public Response<String> delete(@RequestParam(value = "id") Integer id) {
            bannerInfoService.delete(id);
            return Response.ok("删除成功");
    }
    @Log
    @ApiOperation(value = "根据ID查询信息", notes = "根据ID查询信息",httpMethod = "GET")
    @RequestMapping(value = "/getById")
    public Response<Map> getById(@RequestParam(value = "id") Integer id) {
            return bannerInfoService.getById(id);
    }


    @RequestMapping(value = "/sendMessage")
    public Response<String> sendMessage(@RequestParam(value="id") Integer id){
        if(id==null){
            throw new BusinessException("缺少参数");
        }
        bannerInfoService.updateSendStatus(id);
        return  Response.ok("正在执行");
    }

}