package com.intelliquor.cloud.shop.system.controller;

import cn.binarywang.wx.miniapp.bean.WxMaJscode2SessionResult;
import cn.binarywang.wx.miniapp.bean.WxMaUserInfo;
import com.alibaba.fastjson.JSONObject;
import com.intelliquor.cloud.shop.common.config.WxOpenServiceConfig;
import com.intelliquor.cloud.shop.common.entity.Response;
import com.intelliquor.cloud.shop.common.exception.BusinessException;
import com.intelliquor.cloud.shop.common.model.MemberShopBindLogModel;
import com.intelliquor.cloud.shop.common.model.req.MemberShopBindReqModel;
import com.intelliquor.cloud.shop.common.service.MemberShopBindService;
import com.intelliquor.cloud.shop.common.utils.ObjectUtil;
import com.intelliquor.cloud.shop.system.service.MessageService;
import com.intelliquor.cloud.shop.system.util.RedisUtil;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.open.api.WxOpenMaService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Map;


/**
 * <p>
 * openid绑定记录 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-01
 */
@Slf4j
@RestController
@RequestMapping("/memberShopBind")
public class MemberShopBindController {
    @Autowired
    private MemberShopBindService memberShopBindService;


    @Autowired
    protected WxOpenServiceConfig wxOpenService;

    @Autowired
    private MessageService messageService;

    @Autowired
    private RedisUtil redisUtil;



    /**
     * 终端绑定商户openid
     * wechatCode 小程序端获取的code
     * appId 小程序wxAppId
     * shopIds 绑定的商户id，多个用逗号隔开
     * verificationCode 验证码
     */
    @PostMapping("/shopBind")
    public Response<String> shopBind(@RequestBody MemberShopBindReqModel memberShopBindModel) {
        //1.检查数据完整性
        if (StringUtils.isBlank(memberShopBindModel.getShopIds())){
            return Response.fail("请填写绑定商户");
        }
        if (StringUtils.isBlank(memberShopBindModel.getWechatCode()) || StringUtils.isBlank(memberShopBindModel.getAppId())){
            return Response.fail("未获得小程序appid");
        }
        //判断是否需要验证手机验证码
        if (StringUtils.isEmpty(memberShopBindModel.getVerificationCode())) {
            return Response.fail("验证码不能为空");
        } else {
            //检验验证码
            Object redisCode = redisUtil.get("CLOUD_DEALER_LOGIN_VER_CODE_" + memberShopBindModel.getPhone());
            if(ObjectUtil.isEmpty(redisCode)){
                return Response.fail("验证码失效");
            }
            if (!redisCode.toString().equals(memberShopBindModel.getVerificationCode())) {
                return Response.fail("验证码错误");
            }

        }

        //获取用户openid
        WxMaJscode2SessionResult result = null;
        String nickName = null;
        try {
            WxOpenMaService wxOpenMaService = wxOpenService.getWxOpenComponentService().getWxMaServiceByAppid(memberShopBindModel.getAppId());
            result = wxOpenMaService.jsCode2SessionInfo(memberShopBindModel.getWechatCode());
//            String sessionKey = result.getSessionKey();
//            WxMaUserInfo userInfo = wxOpenMaService.getUserService().getUserInfo(sessionKey, memberShopBindModel.getEncryptedData(), memberShopBindModel.getIv());
//            nickName = userInfo.getNickName();
            log.info("获取OpenId结果：{}", JSONObject.toJSONString(result));
        }catch (Exception e){
            log.error("获取openid失败，原因",e);
            return Response.fail("获取openid失败");
        }
        String openId = result.getOpenid();
        String unionid = result.getUnionid();
        if (StringUtils.isEmpty(openId)){
            return Response.fail("获取openid失败");
        }
        memberShopBindModel.setOpenid(openId);
        memberShopBindModel.setUnionid(unionid);
        memberShopBindModel.setType(1);
        MemberShopBindLogModel bindModel = new MemberShopBindLogModel();
        BeanUtils.copyProperties(memberShopBindModel,bindModel);
        //bindModel.setNickname(nickName);
        //进行绑定
        memberShopBindService.bindMemberOpenId(bindModel);
        redisUtil.delete("CLOUD_DEALER_LOGIN_VER_CODE_" + memberShopBindModel.getPhone());
        return Response.ok("绑定成功");
    }


    /**
     * 解除绑定
     * shopId 绑定的商户id
     */
    @PostMapping("/unShopBind")
    public Response<String> unShopBind(@RequestBody MemberShopBindReqModel memberShopBindModel) {
        //1.检查数据完整性
        if (memberShopBindModel.getShopId()== null || memberShopBindModel.getShopId()== 0){
            return Response.fail("请填写绑定商户");
        }

        //判断是否需要验证手机验证码
        if (StringUtils.isEmpty(memberShopBindModel.getVerificationCode())) {
            return Response.fail("验证码不能为空");
        } else {
            //检验验证码
            Object redisCode = redisUtil.get("CLOUD_DEALER_LOGIN_VER_CODE_" + memberShopBindModel.getPhone());
            if(ObjectUtil.isEmpty(redisCode)){
                return Response.fail("验证码失效");
            }
            if (!redisCode.toString().equals(memberShopBindModel.getVerificationCode())) {
                return Response.fail("验证码错误");
            }
        }

        MemberShopBindLogModel bindModel = new MemberShopBindLogModel();
        BeanUtils.copyProperties(memberShopBindModel,bindModel);
        memberShopBindService.unBindMember(bindModel);
        redisUtil.delete("CLOUD_DEALER_LOGIN_VER_CODE_" + memberShopBindModel.getPhone());
        return Response.ok("解绑成功");
    }

}
