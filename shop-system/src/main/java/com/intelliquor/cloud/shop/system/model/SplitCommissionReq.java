package com.intelliquor.cloud.shop.system.model;

import lombok.Data;

import java.math.BigDecimal;

/**
 * Description: 云仓分佣
 *
 * <AUTHOR>
 * @create 2020/5/12 13:52
 */

@Data
public class SplitCommissionReq {

    /**
     *   订单编号
     * @mbg.generated
     */
    private String orderCode;

    /**
     *   订单金额
     * @mbg.generated
     */
    private BigDecimal orderAmount;

    /**
     *   终端店id
     * @mbg.generated
     */
    private Integer shopId;

    /**
     * 消费者openid
     */
    private String openid;

    /**
     * 消费者名
     */
    private String consumerName;

    /**
     * 订单返利设置 0无 1比例 2固定
     */
    private Integer orderRebateType = 0;

    /**
     * 订单返利固定金额
     */

    private BigDecimal orderRebateAmounts;

    /**
     * 订单返利比例
     */
    private BigDecimal orderRebateProportion;

    /**
     * 扫码返利固定金额
     */
    private BigDecimal scancodeRebateAmounts;

    /**
     * 券码
     */
    private String code;

    /**
     * 商品编码
     */
    private String goodsCode;

    /**
     * 消费者unionid
     */
    private String unionid;

    private Integer companyId;

    /**
     * 收益类型：0：扫盖码，1：下订单
     */
    private Integer incomeType;

}
