package com.intelliquor.cloud.shop.system.model;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Builder;
import lombok.Data;

import java.math.BigDecimal;

/**
 * Description: 订单中心明细
 *
 * <AUTHOR>
 * @create 2020/2/20 13:30
 */

@Builder
@Data
public class OrderCenterDetailModel {

    /**
     *   主键id
     * @mbg.generated
     */
    @JSONField(serialize = false,deserialize = false)
    private Long id;

    /**
     *   订单id：data_order表id
     * @mbg.generated
     */
    @JSONField(name = "order_id")
    private String orderId;

    /**
     *   订单编号-冗余字段
     * @mbg.generated
     */
    @JSONField(name = "order_no")
    private String orderNo;

    /**
     *   订单来源：云团-ZY_GROUP；云店-CLOUD_SHOP；云众-ZY_MEMBER
     * @mbg.generated
     */
    @JSONField(name = "order_source_system")
    private String orderSourceSystem;

    /**
     *   第三方订单id，云众、云团、云店订单id
     * @mbg.generated
     */
    @JSONField(name = "order_source_id")
    private String orderSourceId;


    /**
     *   商品id
     * @mbg.generated
     */
    @JSONField(name = "goods_id")
    private String goodsId;

    /**
     *   商品名称
     * @mbg.generated
     */
    @JSONField(name = "goods_name")
    private String goodsName;

    /**
     *   券id （商品类型为券类型）
     * @mbg.generated
     */
    @JSONField(name = "coupon_id")
    private String couponId;

    /**
     *   商品图片
     * @mbg.generated
     */
    @JSONField(name = "goods_image")
    private String goodsImage;

    /**
     *   商品类型：1 实物；2 红包；3 券 ;4 积分抽奖 5 第三方
     * @mbg.generated
     */
    @JSONField(name = "goods_type")
    private Integer goodsType;

    /**
     *   商品来源，一般是第三方使用
     * @mbg.generated
     */
    @JSONField(name = "goods_source")
    private String goodsSource;

    /**
     *   商品原价
     * @mbg.generated
     */
    @JSONField(name = "goods_original_price")
    private BigDecimal goodsOriginalPrice;

    /**
     *   实际积分
     * @mbg.generated
     */
    @JSONField(name = "goods_integral")
    private BigDecimal goodsIntegral;

    /**
     *   商品价格
     * @mbg.generated
     */
    @JSONField(name = "goods_price")
    private BigDecimal goodsPrice;

    /**
     *   商品数量
     * @mbg.generated
     */
    @JSONField(name = "quantity")
    private Integer quantity;

    /**
     * 是否赠品 1-是 0-否
     * @mbg.generated
     */
    @JSONField(name = "gift")
    private Integer gift;

    /**
     * 赠品是否为待解锁状态 1-是 0-否
     */
    @JSONField(name = "gift_unlocked")
    private Integer giftUnlocked;

}
