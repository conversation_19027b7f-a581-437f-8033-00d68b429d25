package com.intelliquor.cloud.shop.system.service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.intelliquor.cloud.shop.common.dao.ShopDao;
import com.intelliquor.cloud.shop.common.dao.ShopUserDao;
import com.intelliquor.cloud.shop.common.entity.FileItem;
import com.intelliquor.cloud.shop.common.entity.PageResponse;
import com.intelliquor.cloud.shop.common.entity.Response;
import com.intelliquor.cloud.shop.common.enums.OrderTypeEnum;
import com.intelliquor.cloud.shop.common.model.ShopModel;
import com.intelliquor.cloud.shop.common.model.ShopUserModel;
import com.intelliquor.cloud.shop.common.model.UserContext;
import com.intelliquor.cloud.shop.common.model.UserModel;
import com.intelliquor.cloud.shop.common.utils.*;
import com.intelliquor.cloud.shop.system.dao.*;
import com.intelliquor.cloud.shop.system.model.*;
import com.intelliquor.cloud.shop.system.model.excel.*;
import com.intelliquor.cloud.shop.system.model.req.RewardScanCodeDetailExportReq;
import com.intelliquor.cloud.shop.system.model.resp.PurchaseRecordResp;
import com.intelliquor.cloud.shop.system.model.resp.RewardScanCodeExportResp;
import io.searchbox.client.JestClient;
import io.searchbox.core.Search;
import io.searchbox.core.SearchResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.lucene.search.join.ScoreMode;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.NestedQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.*;

/**
 * 描述：扫码奖励扫码明细 服务实现层
 *
 * <AUTHOR>
 * @date 2019-07-12
 */
@Slf4j
@Service
public class RewardScanCodeDetailService {

    @Autowired
    private RewardScanCodeDetailDao rewardScanCodeDetailDao;

    @Autowired
    private MiddleRewardScanCodeDetailRelationDao middleRewardScanCodeDetailRelationDao;

    @Autowired
    private ShopDao shopDao;

    @Autowired
    private OrderDownloadCenterService downloadCenterService;

    @Autowired
    private RewardScanBalanceDao rewardScanBalanceDao;

    @Autowired
    private ShopUserDao shopUserDao;

    @Autowired
    private BusinessAreaDao businessAreaDao;

    @Autowired
    private DealerService dealerService;

    @Autowired
    private YoushiPaymentRecordService youshiPaymentRecordService;

    @Autowired
    private UserContext userContext;

    @Autowired
    private JestClient jestClient;

    @Autowired
    private ModelInfoService modelInfoService;

    @Value("${GBC-company.id}")
    private Integer gbcCompanyId;

    @Autowired
    private RewardGoodsTypeScoreDao rewardGoodsTypeScoreDao;

    /**
     * 查询数据
     *
     * @return
     */
    public PageInfo<RewardScanCodeDetailModel> selectList(Map<String, Object> searchMap, Integer pageNo, Integer pageSize) {
        PageHelper.startPage(pageNo, pageSize);
        List<RewardScanCodeDetailModel> list = rewardScanCodeDetailDao.selectList(searchMap);
        for (RewardScanCodeDetailModel detail : list) {
            detail.setArea(detail.getProvince() + detail.getCity() + detail.getDistrict());
        }
        PageInfo<RewardScanCodeDetailModel> page = new PageInfo(list);
        return page;
    }
    /**
     * 查询数据
     *
     * @return
     */
    public PageInfo<RewardScanCodeDetailModel> selectListPermission(Map<String, Object> searchMap, Integer pageNo, Integer pageSize) {
        PageHelper.startPage(pageNo, pageSize);
        List<RewardScanCodeDetailModel> list = rewardScanCodeDetailDao.selectListPermission(searchMap);
        for (RewardScanCodeDetailModel detail : list) {
            detail.setArea(detail.getProvince() + detail.getCity() + detail.getDistrict());
        }
        PageInfo<RewardScanCodeDetailModel> page = new PageInfo(list);
        return page;
    }

    /**
     * 查询终端店铺扫码明细
     *
     * @return
     */
    public PageInfo<RewardScanCodeDetailModel> selectListShopGbc(Map<String, Object> searchMap, Integer pageNo, Integer pageSize) {
        PageHelper.startPage(pageNo, pageSize);
        List<RewardScanCodeDetailModel> list = rewardScanCodeDetailDao.selectListShopGbc(searchMap);
        if (null != list && list.size() > 0) {
            // 公司ID
            Integer companyId = list.get(0).getComId();
            // 业务区域
            Set<String> areaCodeSet = new HashSet<>();
            // 经销商
            Set<String> dealerCodeSet = new HashSet<>();
            // 扫码人
            Set<String> openIdSet = new HashSet<>();
            for (RewardScanCodeDetailModel detail : list) {
                if (null != detail.getOrgCode()) {
                    areaCodeSet.add(detail.getOrgCode());
                }
                if (null != detail.getCompanyCode()) {
                    dealerCodeSet.add(detail.getCompanyCode());
                }
                if (null != detail.getOpenId()) {
                    openIdSet.add(detail.getOpenId());
                }
                // 盒码加上VFT
                detail.setPingCode("VFT" + detail.getPingCode());

                // 政策金额
                if (null != detail.getAmount() && null != detail.getQuantity() && 0 != detail.getQuantity()) {
                    detail.setUnit(detail.getAmount().divide(new BigDecimal(detail.getQuantity()), 2, BigDecimal.ROUND_HALF_UP));
                } else {
                    detail.setUnit(new BigDecimal(0));
                }

                // 是否有效扫码均为有效，无效原因为空
                detail.setRecordType(5);
                detail.setIsValidStr("是");
                detail.setInvalidReason("");
            }
            // 业务区域
            Map<String, String> areaMap = new HashMap<>();
            if (areaCodeSet.size() > 0) {
                List<BusinessAreaModel> areaList = businessAreaDao.getByAreaCodes(companyId, areaCodeSet);
                for (BusinessAreaModel area : areaList) {
                    areaMap.put(area.getAreaCode(), area.getAreaName());
                }
            }
            // 经销商
            Map<String, DealerModel> dealerMap = new HashMap<>();
            if (dealerCodeSet.size() > 0) {
                List<DealerModel> dealerList = dealerService.selectListByDealerCodes(companyId, dealerCodeSet);
                for (DealerModel dealer : dealerList) {
                    dealerMap.put(dealer.getDealerCode(), dealer);
                }
            }
            // 扫码人
            Map<String, String> userMap = new HashMap<>();
            if (openIdSet.size() > 0) {
                List<UserModel> userList = shopUserDao.getUserListByOpenIds(openIdSet);
                for (UserModel user : userList) {
                    userMap.put(user.getWxOpenId(), user.getName());
                }
            }
            for (RewardScanCodeDetailModel detail : list) {
                if (null != detail.getOrgCode() && null != areaMap.get(detail.getOrgCode())) {
                    detail.setOrgName(areaMap.get(detail.getOrgCode()));
                }
                // 经销商信息
                if (null != detail.getCompanyCode() && null != dealerMap.get(detail.getCompanyCode())) {
                    DealerModel dealer = dealerMap.get(detail.getCompanyCode());
                    if (ObjectUtil.isNotEmpty(dealer)) {
                        detail.setOtherNumber(dealer.getOtherNumber());
                        detail.setCompanyName(dealer.getDealerName());
                    }
                }
                if (null != detail.getOpenId() && null != userMap.get(detail.getOpenId())) {
                    detail.setUserName(userMap.get(detail.getOpenId()));
                }
            }
        }

        PageInfo<RewardScanCodeDetailModel> page = new PageInfo(list);
        return page;
    }

    public PageInfo<ShopDataStatisticsModel> selectDealerShopDetailList(String dealerCode, String shopName, Integer companyId, Integer pageNo, Integer pageSize) {
        PageHelper.startPage(pageNo, pageSize);
        List<ShopDataStatisticsModel> list = rewardScanCodeDetailDao.selectDealerShopDetailList(dealerCode, shopName, companyId);
        PageInfo<ShopDataStatisticsModel> page = new PageInfo(list);
        return page;
    }

    public ShopDataStatisticsModel selectDealerShopTopData(String dealerCode, String shopName, Integer companyId) {
        ShopDataStatisticsModel model;
        model = rewardScanCodeDetailDao.selectDealerShopTopData(dealerCode, shopName, companyId);
        if (null == model) {
            model = new ShopDataStatisticsModel();
        }
        return model;
    }

    /**
     * 获取扫码统计
     *
     * @return
     */
    public Map getTotalByTime(Integer companyId, Integer shopId, Integer type, String startTime, String endTime) {
        return rewardScanCodeDetailDao.getTotalByTime(companyId, shopId, type, startTime, endTime);
    }

    public List<RewardScanCodeDetailShop> selectExcelShopList(Map<String, Object> searchMap) {
        List<RewardScanCodeDetailShop> list = rewardScanCodeDetailDao.selectExcelShopList(searchMap);
        return list;
    }

    public List<RewardScanCodeDetailShopGbc> selectExcelShopGbcList(Map<String, Object> searchMap) {
        List<RewardScanCodeDetailShopGbc> list = rewardScanCodeDetailDao.selectExcelShopGbcList(searchMap);
        return list;
    }


    public List<RewardScanCodeDetailConsumer> selectExcelConsumerList(Map<String, Object> searchMap) {
        List<RewardScanCodeDetailConsumer> list = rewardScanCodeDetailDao.selectExcelConsumerList(searchMap);
        return list;
    }

    /**
     * 新增数据
     *
     * @param model
     */
    public void insert(RewardScanCodeDetailModel model) {
        rewardScanCodeDetailDao.insert(model);
    }

    /**
     * 更新数据
     *
     * @param model
     */
    public void update(RewardScanCodeDetailModel model) {
        rewardScanCodeDetailDao.update(model);
    }

    /**
     * 删除数据
     *
     * @param id
     */
    public void delete(Integer id) {
        rewardScanCodeDetailDao.delete(id);
    }

    /**
     * 根据ID查询数据
     *
     * @param id
     */
    public RewardScanCodeDetailModel getById(Integer id) {
        return rewardScanCodeDetailDao.getById(id);
    }

    /**
     * 最近4条奖励信息
     *
     * @param shopId
     * @return
     */
    public Response<TreeSet<RewardScanCodeDetailModel>> getRewardMsg(Integer shopId) {
        ShopModel shopModel = shopDao.getById(shopId);
        List<RewardScanCodeDetailModel> cash = rewardScanCodeDetailDao.getByShopIdAndRewardType(shopId, 1, 2, shopModel.getCompanyId());
        List<RewardScanCodeDetailModel> good = rewardScanCodeDetailDao.getByShopIdAndRewardType(shopId, 3, 2, shopModel.getCompanyId());
        TreeSet<RewardScanCodeDetailModel> tree = new TreeSet<>();
        for (RewardScanCodeDetailModel model : cash) {
            tree.add(model);
        }
        for (RewardScanCodeDetailModel model : good) {
            tree.add(model);
        }
        return Response.ok(tree);
    }


    public Response<TreeSet<RewardScanCodeDetailModel>> getMsg(Integer shopId) {
        ShopModel shopModel = shopDao.getById(shopId);
        List<RewardScanCodeDetailModel> cash = rewardScanCodeDetailDao.getByShopIdAndRewardType(shopId, 1, 2, shopModel.getCompanyId());
        List<RewardScanCodeDetailModel> good = rewardScanCodeDetailDao.getByShopIdAndRewardType(shopId, 3, 2, shopModel.getCompanyId());
        TreeSet<RewardScanCodeDetailModel> tree = new TreeSet<>();
        for (RewardScanCodeDetailModel model : cash) {
            tree.add(model);
        }
        for (RewardScanCodeDetailModel model : good) {
            tree.add(model);
        }
        return Response.ok(tree);
    }

    /**
     * 产品扫码统计
     *
     * @param page
     * @param limit
     * @param startTime
     * @param endTime
     * @return
     */
    public PageResponse<List<GoodsScanExcel>> goodsScan(Integer page, Integer limit, String startTime, String endTime, Integer comId) {
        PageHelper.startPage(page, limit);
        List<GoodsScanExcel> list = rewardScanCodeDetailDao.goodsScan(startTime, endTime, comId);
        PageInfo<GoodsScanExcel> pageInfo = new PageInfo<>(list);
        return PageResponse.ok(pageInfo);
    }

    public List<GoodsScanExcel> excelGoodsScan(String startTime, String endTime, Integer comId) {
        List<GoodsScanExcel> list = rewardScanCodeDetailDao.goodsScan(startTime, endTime, comId);
        return list;
    }


    /**
     * 终端扫码统计
     *
     * @param page
     * @param limit
     * @return
     */
    public PageResponse<List<ShopScanExcel>> shopScan(Integer page, Integer limit, Map<String, Object> param) {
        log.info("终端扫码统计开始=={}", param);
        // 统计条件
        PageHelper.startPage(page, limit);
        List<ShopScanExcel> list = rewardScanCodeDetailDao.shopScanDistinct(param);
        PageInfo<ShopScanExcel> pageInfo = new PageInfo<>(list);
        // 最终统计
        List<ShopScanExcel> statList = rewardScanCodeDetailDao.shopScanStat((Integer) param.get("comId"), list);
        pageInfo.setList(statList);
        log.info("终端扫码统计结束=={}", statList);
        return PageResponse.ok(pageInfo);
    }

    public List<ShopScanExcel> excelShopScan(Map<String, Object> param) {
        List<ShopScanExcel> list = rewardScanCodeDetailDao.shopScan(param);
        return list;
    }

    /**
     * 添加下载中心数据
     *
     * @param model
     * @return
     */
    public OrderDownloadCenterModel addOrderDownloadCenter(ShopScanExcel model) {
        OrderDownloadCenterModel downloadCenterModel = model.getDownloadCenterModel();

        downloadCenterModel.setType(OrderTypeEnum.TERMINALSCANTOTAL.getValue().shortValue());

        downloadCenterService.addOrderDownloadCenter(downloadCenterModel);
        return downloadCenterModel;
    }

    /**
     * 创建文件并上传至阿里云OSS
     *
     * @param model
     * @return
     * @throws IOException
     */
    public String exportOrderFile(ShopScanExcel model) throws IOException {
        Map<String, Object> param = new HashMap<>();
        param.put("shopName", model.getShopName());
        param.put("orgName", model.getOrgName());
        param.put("linkphone", model.getLinkphone());
        param.put("channelId", model.getChannelId());
        param.put("goodsCode", model.getGoodsCode());
        param.put("goodsName", model.getGoodsName());
        param.put("startTime", model.getStartTimeStr());
        param.put("endTime", model.getEndTimeStr());
        param.put("comId", model.getCompanyId());
        List<ShopScanExcel> list = this.excelShopScan(param);
        if (!AirUtils.hv(list)) {
            ShopScanExcel entity = new ShopScanExcel();
            list.add(entity);
        }

        FileItem fileItem = ExcelTools.exportByFile(list, 1);
        String fileName = (new StringBuffer()).append("ZDSMTJ").append(TimeUtilis.getCurrentFormatTime("yyyyMMddHHmmss")).append(".xls").toString();
        return downloadCenterService.getFileUrl(fileName, fileItem.getContent());
    }

    /**
     * 终端扫码统计导出
     *
     * @param model
     * @return
     * @throws IOException
     */
    @Async
    public void shopScanExportTask(ShopScanExcel model) throws IOException {
        System.out.println("开始执行");

        // 1、添加下载中心数据
        OrderDownloadCenterModel downloadCenterModel = this.addOrderDownloadCenter(model);

        // 2、上传文件
        String fileUrl = this.exportOrderFile(model);

        // 3、修改下载中心状态
        downloadCenterModel.setFilePath(fileUrl);
        downloadCenterService.updOrderDownloadCenter(downloadCenterModel);

        System.out.println("结束执行");
    }

    /**
     * 添加下载中心数据
     *
     * @param conditionExcel
     * @return
     */
    public OrderDownloadCenterModel addOrderDownloadCenter2(ExportConditionExcel conditionExcel) {
        OrderDownloadCenterModel model = conditionExcel.getDownloadCenterModel();

        model.setType(OrderTypeEnum.PRODUCTSCANTOTAL.getValue().shortValue());

        downloadCenterService.addOrderDownloadCenter(model);
        return model;
    }

    /**
     * 创建文件并上传至阿里云OSS
     *
     * @param
     * @return
     * @throws IOException
     */
    public String exportOrderFile2(ExportConditionExcel conditionExcel) throws IOException {
        List<GoodsScanExcel> list = this.excelGoodsScan(conditionExcel.getStartTime(), conditionExcel.getEndTime(), conditionExcel.getCompanyId());
        if (!AirUtils.hv(list)) {
            GoodsScanExcel entity = new GoodsScanExcel();
            list.add(entity);
        }

        FileItem fileItem = ExcelTools.exportByFile(list, 1);
        String fileName = (new StringBuffer()).append("CPSMHZ").append(TimeUtilis.getCurrentFormatTime("yyyyMMddHHmmss")).append(".xls").toString();
        return downloadCenterService.getFileUrl(fileName, fileItem.getContent());
    }

    /**
     * 产品扫码汇总导出
     *
     * @param conditionExcel
     * @return
     * @throws IOException
     */
    @Async
    public void goodsScanExportTask(ExportConditionExcel conditionExcel) throws IOException {
        System.out.println("开始执行");

        // 1、添加下载中心数据
        OrderDownloadCenterModel downloadCenterModel = this.addOrderDownloadCenter2(conditionExcel);

        // 2、上传文件
        String fileUrl = this.exportOrderFile2(conditionExcel);

        // 3、修改下载中心状态
        downloadCenterModel.setFilePath(fileUrl);
        downloadCenterService.updOrderDownloadCenter(downloadCenterModel);

        System.out.println("结束执行");
    }


    /**
     * 添加下载中心数据
     *
     * @param model
     * @return
     */
    public OrderDownloadCenterModel addOrderDownloadCenter3(RewardScanCodeDetailShop model) {
        OrderDownloadCenterModel downloadCenterModel = model.getDownloadCenterModel();

        downloadCenterModel.setType(OrderTypeEnum.TERMINALSCANDETAILS.getValue().shortValue());

        downloadCenterService.addOrderDownloadCenter(downloadCenterModel);
        return downloadCenterModel;
    }

    /**
     * 创建文件并上传至阿里云OSS
     *
     * @param model
     * @return
     * @throws IOException
     */
    public String exportOrderFile3(RewardScanCodeDetailShop model) throws IOException {
        Map<String, Object> searchMap = new HashMap<>();
        if (ObjectUtil.isNotEmpty(model.getRewardTypeArray())) {
            String[] is = model.getRewardTypeArray().split("[|]");
            if (ObjectUtil.isNotEmpty(is)) {
                searchMap.put("rewardTypeArray", is);
            }
        }
        searchMap.put("type", model.getType());
        searchMap.put("goodsName", model.getGoodsName());
        searchMap.put("goodsCode", model.getGoodsCode());
        searchMap.put("qrcode", model.getQrcode());
        searchMap.put("companyName", model.getCompanyName());
        searchMap.put("shopName", model.getShopName());
        searchMap.put("shopPhone", model.getShopPhone());
        searchMap.put("comId", model.getCompanyId());
        if (ObjectUtil.isNotEmpty(model.getStartTime())) {
            searchMap.put("startTime", model.getStartTime());
        }
        if (ObjectUtil.isNotEmpty(model.getEndTime())) {
            searchMap.put("endTime", model.getEndTime());
        }
        searchMap.put("province", model.getProvince());
        searchMap.put("city", model.getCity());
        searchMap.put("district", model.getDistrict());
        searchMap.put("balanceId", model.getBalanceId());
        searchMap.put("outTime", model.getOutTime());
        searchMap.put("quantity", model.getQuantity());
        List<RewardScanCodeDetailShop> list = this.selectExcelShopList(searchMap);
        if (!AirUtils.hv(list)) {
            RewardScanCodeDetailShop detailShop = new RewardScanCodeDetailShop();
            list.add(detailShop);
        }

        String fileName = (new StringBuffer()).append("ZDSMMX").append(TimeUtilis.getCurrentFormatTime("yyyyMMddHHmmss")).append(".xls").toString();
        FileItem fileItem = ExcelTools.exportByFile(list, 1);

        return downloadCenterService.getFileUrl(fileName, fileItem.getContent());
    }

    /**
     * 终端扫码明细导出
     *
     * @param rewardScanCodeDetailShop
     * @return
     * @throws IOException
     */
    @Async
    public void shopScanDetailExportTask(RewardScanCodeDetailShop rewardScanCodeDetailShop) throws IOException {
        System.out.println("开始执行");

        // 1、添加下载中心数据
        OrderDownloadCenterModel downloadCenterModel = this.addOrderDownloadCenter3(rewardScanCodeDetailShop);

        // 2、上传文件
        String fileUrl = this.exportOrderFile3(rewardScanCodeDetailShop);

        // 3、修改下载中心状态
        downloadCenterModel.setFilePath(fileUrl);
        downloadCenterService.updOrderDownloadCenter(downloadCenterModel);

        System.out.println("结束执行");
    }

    /**
     * 古贝春-终端店铺扫码记录-添加下载中心数据
     *
     * @param model
     * @return
     */
    public OrderDownloadCenterModel addOrderDownloadCenter5(RewardScanCodeDetailShopGbc model) {
        OrderDownloadCenterModel downloadCenterModel = model.getDownloadCenterModel();

        downloadCenterModel.setType(OrderTypeEnum.SHOPSCANRECORD.getValue().shortValue());

        downloadCenterService.addOrderDownloadCenter(downloadCenterModel);
        return downloadCenterModel;
    }

    /**
     * 古贝春-终端店铺扫码记录-创建文件并上传至阿里云OSS
     *
     * @param param
     * @return
     * @throws IOException
     */
    public String exportOrderFile5(RewardScanCodeDetailShopGbc param) throws IOException {
        Map<String, Object> searchMap = SearchUtil.getSearch(param);
        // 查询全部数据
        List<RewardScanCodeDetailShopGbc> listSelect = this.selectExcelShopGbcList(searchMap);
        // 导出的数据List
        List<RewardScanCodeDetailShopGbc> listResult = new ArrayList<>();
        if (null != listSelect && listSelect.size() > 0) {
            // 每1000条数据分组处理
            List<List<RewardScanCodeDetailShopGbc>> listList = rewardScanCodeDetailShopGbcSplitList(listSelect, 1000);
            for (List<RewardScanCodeDetailShopGbc> listItem : listList) {
                // 公司ID
                Integer companyId = listItem.get(0).getComId();
                // 业务区域
                Set<String> areaCodeSet = new HashSet<>();
                // 经销商
                Set<String> dealerCodeSet = new HashSet<>();
                // 扫码人
                Set<String> openIdSet = new HashSet<>();
                for (RewardScanCodeDetailShopGbc detail : listItem) {
                    if (null != detail.getOrgCode()) {
                        areaCodeSet.add(detail.getOrgCode());
                    }
                    if (null != detail.getCompanyCode()) {
                        dealerCodeSet.add(detail.getCompanyCode());
                    }
                    if (null != detail.getOpenId()) {
                        openIdSet.add(detail.getOpenId());
                    }
                    // 政策金额
                    if (null != detail.getAmount() && null != detail.getQuantity() && 0 != detail.getQuantity()) {
                        detail.setUnit(detail.getAmount().divide(new BigDecimal(detail.getQuantity()), 2, BigDecimal.ROUND_HALF_UP));
                    } else {
                        detail.setUnit(new BigDecimal(0));
                    }

                    // 是否有效扫码均为有效，无效原因为空
                    detail.setRecordType(5);
                    detail.setIsValidStr("是");
                    detail.setInvalidReason("");
                }
                // 业务区域
                Map<String, String> areaMap = new HashMap<>();
                if (areaCodeSet.size() > 0) {
                    List<BusinessAreaModel> areaList = businessAreaDao.getByAreaCodes(companyId, areaCodeSet);
                    for (BusinessAreaModel area : areaList) {
                        areaMap.put(area.getAreaCode(), area.getAreaName());
                    }
                }
                // 经销商
                Map<String, DealerModel> dealerMap = new HashMap<>();
                if (dealerCodeSet.size() > 0) {
                    List<DealerModel> dealerList = dealerService.selectListByDealerCodes(companyId, dealerCodeSet);
                    for (DealerModel dealer : dealerList) {
                        dealerMap.put(dealer.getDealerCode(), dealer);
                    }
                }
                // 扫码人
                Map<String, String> userMap = new HashMap<>();
                if (openIdSet.size() > 0) {
                    List<UserModel> userList = shopUserDao.getUserListByOpenIds(openIdSet);
                    for (UserModel user : userList) {
                        userMap.put(user.getWxOpenId(), user.getName());
                    }
                }
                for (RewardScanCodeDetailShopGbc detail : listItem) {
                    if (null != detail.getOrgCode() && null != areaMap.get(detail.getOrgCode())) {
                        detail.setOrgName(areaMap.get(detail.getOrgCode()));
                    }
                    // 经销商信息
                    if (null != detail.getCompanyCode() && null != dealerMap.get(detail.getCompanyCode())) {
                        DealerModel dealer = dealerMap.get(detail.getCompanyCode());
                        if (ObjectUtil.isNotEmpty(dealer)) {
                            detail.setOtherNumber(dealer.getOtherNumber());
                            detail.setCompanyName(dealer.getDealerName());
                        }
                    }
                    if (null != detail.getOpenId() && null != userMap.get(detail.getOpenId())) {
                        detail.setUserName(userMap.get(detail.getOpenId()));
                    }
                }
                // 添加到结果List
                listResult.addAll(listItem);
            }
        }

        if (!AirUtils.hv(listResult)) {
            RewardScanCodeDetailShopGbc detail = new RewardScanCodeDetailShopGbc();
            listResult.add(detail);
        }

        String fileName = (new StringBuffer()).append("终端店铺扫码记录").append(TimeUtilis.getCurrentFormatTime("yyyyMMddHHmmss")).append(".xls").toString();
        FileItem fileItem = ExcelTools.exportByFile(listResult, 1);

        return downloadCenterService.getFileUrl(fileName, fileItem.getContent());
    }

    private List<List<RewardScanCodeDetailShopGbc>> rewardScanCodeDetailShopGbcSplitList(List<RewardScanCodeDetailShopGbc> list, int groupSize) {
        int length = list.size();
        // 计算可以分成多少组
        int num = (length + groupSize - 1) / groupSize;
        List<List<RewardScanCodeDetailShopGbc>> newList = new ArrayList<>(num);
        for (int i = 0; i < num; i++) {
            // 开始位置
            int fromIndex = i * groupSize;
            // 结束位置
            int toIndex = (i + 1) * groupSize < length ? (i + 1) * groupSize : length;
            newList.add(list.subList(fromIndex, toIndex));
        }
        return newList;
    }


    /**
     * 古贝春-终端店铺扫码记录导出任务
     *
     * @param rewardScanCodeDetailShopGbc
     * @return
     * @throws IOException
     */
    @Async
    public void shopScanCodeDetailGbcExportTask(RewardScanCodeDetailShopGbc rewardScanCodeDetailShopGbc) throws IOException {
        System.out.println("开始执行");

        // 1、添加下载中心数据
        OrderDownloadCenterModel downloadCenterModel = this.addOrderDownloadCenter5(rewardScanCodeDetailShopGbc);

        // 2、上传文件
        String fileUrl = this.exportOrderFile5(rewardScanCodeDetailShopGbc);

        // 3、修改下载中心状态
        downloadCenterModel.setFilePath(fileUrl);
        downloadCenterService.updOrderDownloadCenter(downloadCenterModel);

        System.out.println("结束执行");
    }


    @Transactional(rollbackFor = Exception.class)
    public Response<String> deleteScanCode(String[] ids, int checked) {
        StringBuffer codeStr = new StringBuffer();
        StringBuffer idStr = new StringBuffer();
        for (int i = 0; i < ids.length; i++) {
            //奖励金额
            BigDecimal detailAmount = BigDecimal.ZERO;
            Integer scoreTypeId = null;
            RewardGoodsTypeScoreModel scoreModel = null;
            RewardScanCodeDetailModel detailModel = rewardScanCodeDetailDao.getById(Integer.valueOf(ids[i]));
            if (null != detailModel) {
                RewardScanBalanceModel balanceModel = rewardScanBalanceDao.getById(detailModel.getBalanceId());
                if(balanceModel.getEarningType() == 1){
                    //红包
                    detailAmount = detailModel.getAmount();
                }else if(balanceModel.getEarningType() == 5){
                    //积分
                    detailAmount = new BigDecimal(detailModel.getScore());
                    scoreTypeId = balanceModel.getScoreTypeId();
                }
                if (0 < detailAmount.doubleValue()) {
                    //奖励金额大于零的时候，要减去收支明细和余额的钱
                    BigDecimal userAmount = BigDecimal.ZERO;
                    ShopUserModel userModel = shopUserDao.getPrimaryAccountByShopId(balanceModel.getShopId());
                    if(balanceModel.getEarningType() == 1){
                        //账户余额
                        userAmount = userModel.getAmount();
                    }else if(balanceModel.getEarningType() == 5){
                        //账户积分
                        userAmount = new BigDecimal(userModel.getScore());
                        if(scoreTypeId != null){
                            //获取对应的币
                            scoreModel = rewardGoodsTypeScoreDao.getByShopIdAndScoreTypeId(balanceModel.getShopId(),scoreTypeId);
                        }
                        if(scoreModel != null && userAmount.compareTo(new BigDecimal(scoreModel.getScore()))>0){
                            userAmount = new BigDecimal(scoreModel.getScore());
                        }
                    }
                    if (null == userModel || detailAmount.compareTo(userAmount) > 0) {
                        //余额的钱不足，需要提示用户
                        if (codeStr.length() <= 0) {
                            codeStr.append(detailModel.getQrcode());
                            idStr.append(ids[i]);
                        } else {
                            codeStr.append("," + detailModel.getQrcode());
                            idStr.append("," + ids[i]);
                        }
                    } else {
                        if(scoreModel != null){
                            RewardGoodsTypeScoreModel score = new RewardGoodsTypeScoreModel();
                            score.setScoreTypeId(scoreTypeId);
                            score.setCompanyId(balanceModel.getCompanyId());
                            score.setShopId(balanceModel.getShopId());
                            score.setShopUserId(userModel.getId());
                            score.setScore(detailModel.getScore());
                            int count = rewardGoodsTypeScoreDao.reduceScore(score);
                            log.info("扫码清除删除终端{}的{}币{}个，更新结果{}",balanceModel.getShopId(),scoreTypeId,detailModel.getScore(),count);
                        }
                        rewardScanCodeDetailDao.setDeleteById(detailModel.getId());
                        balanceModel.setAmount(balanceModel.getAmount().subtract(detailModel.getAmount()));
                        balanceModel.setScore(balanceModel.getScore()-detailModel.getScore());
                        balanceModel.setCodeCount(balanceModel.getCodeCount() - 1);
                        rewardScanBalanceDao.update(balanceModel);
                        shopUserDao.addAmount(userModel.getId(), (detailModel.getAmount()).multiply(BigDecimal.valueOf(-1)));
                        shopUserDao.addScore(userModel.getId(),(-1*detailModel.getScore()));
                        //删除优市金服相关信息
                        youshiPaymentRecordService.deleteDetail(detailModel.getId());
                    }

                } else {
                    //奖励金额等于零的时候，直接删除码信息
                    balanceModel.setCodeCount(balanceModel.getCodeCount() - 1);
                    rewardScanBalanceDao.update(balanceModel);
                    rewardScanCodeDetailDao.setDeleteById(detailModel.getId());
                    //删除优市金服相关信息
                    youshiPaymentRecordService.deleteDetail(detailModel.getId());
                }
            }
        }
        if (idStr.length() > 0) {
            Response response = new Response();
            response.setMsg(codeStr + ",以上码对应用户奖励余额不足，无法扫码清除");
            response.setSuccess(Boolean.FALSE);
            response.setResult(idStr);
            response.setCode(456);
            return response;
        }
        return Response.ok("删除成功");

    }

    /**
     * 通过openid获取客户受众信息
     *
     * @param companyId
     * @param modelId
     * @param openid
     * @return
     */
    public JSONObject getCustomerByOpenid(Integer companyId, Integer modelId, String openid) {
        String indexName = "customer_" + companyId + "_" + modelId;
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        BoolQueryBuilder baseBoolQueryBuilder = QueryBuilders.boolQuery();
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        boolQueryBuilder.must(QueryBuilders.termQuery("identitys.type.keyword", "wechat"));
        boolQueryBuilder.must(QueryBuilders.termQuery("identitys.value.keyword", openid));
        NestedQueryBuilder nestedQueryBuilder = QueryBuilders.nestedQuery("identitys", boolQueryBuilder, ScoreMode.None);
        baseBoolQueryBuilder.must(QueryBuilders.termQuery("is_delete", 0));
        baseBoolQueryBuilder.must(nestedQueryBuilder);
        searchSourceBuilder.query(baseBoolQueryBuilder);

        Search search = new Search.Builder(searchSourceBuilder.toString())
                .addType("_doc")
                .addIndex(indexName)
                .build();

        SearchResult rs = null;
        try {
            rs = jestClient.execute(search);
        } catch (IOException e) {
            e.printStackTrace();
        }
        if (rs != null) {
            if (rs.isSucceeded()) {
                String sourceAsString = StringUtils.join("[", rs.getSourceAsString(), "]");
                JSONArray jsonArray = com.alibaba.fastjson.JSONObject.parseArray(sourceAsString);
                if (jsonArray != null && jsonArray.size() > 0) {
                    JSONObject jsonObject = (JSONObject) jsonArray.get(0);
                    return jsonObject;
                }
            }
        }
        return null;
    }

    /**
     * 查询数据
     *
     * @return
     */
    public PageInfo<RewardScanCodeExportResp> getProductFlowList(Map<String, Object> searchMap, Integer pageNo, Integer pageSize) {
        PageHelper.startPage(pageNo, pageSize);
        List<RewardScanCodeExportResp> list = rewardScanCodeDetailDao.getDealerProductFlowList(searchMap);
        PageInfo<RewardScanCodeExportResp> page = new PageInfo(list);
        return page;
    }


    /**
     * 终端导出
     *
     * @param searchMap
     * @param shopModel
     * @throws IOException
     */
    @Async
    public void terminalExportTask(Map<String, Object> searchMap, RewardScanCodeDetailExportReq shopModel) throws IOException {
        System.out.println("开始执行");

        // 1、添加下载中心数据
        OrderDownloadCenterModel downloadCenterModel = this.addTerminalDownloadCenter(shopModel);

        // 2、上传文件
        String fileUrl = this.exportOrder(searchMap);

        // 3、修改下载中心状态
        downloadCenterModel.setFilePath(fileUrl);
        downloadCenterService.updOrderDownloadCenter(downloadCenterModel);

        System.out.println("结束执行");
    }

    /**
     * 添加下载中心数据
     *
     * @return
     */
    public OrderDownloadCenterModel addTerminalDownloadCenter(RewardScanCodeDetailExportReq request) {
        OrderDownloadCenterModel downloadCenterModel = request.getDownloadCenterModel();

        downloadCenterModel.setType(OrderTypeEnum.DEALERPRODUCTFLOW.getValue().shortValue());

        downloadCenterService.addOrderDownloadCenter(downloadCenterModel);
        return downloadCenterModel;
    }

    /**
     * 创建文件并上传至阿里云OSS
     *
     * @param searchMap
     * @return
     * @throws IOException
     */
    public String exportOrder(Map<String, Object> searchMap) throws IOException {
        List<RewardScanCodeExportResp> exportRespList = rewardScanCodeDetailDao.getDealerProductFlowList(searchMap);

        FileItem fileItem = ExcelTools.exportByFile(exportRespList, 1);
        //String fileName = "经销商串货产品流向明细表.xls";
         String fileName = (new StringBuffer()).append("经销商串货产品流向明细表").append(TimeUtilis.getCurrentFormatTime("yyyyMMddHHmmss")).append(".xls").toString();
        return downloadCenterService.getFileUrl(fileName, fileItem.getContent());
    }

    /**
     * 拆分终端店铺扫码转为盒码转存到中间表
     */
    public void splitShopScanCode() throws Exception {
        // 最后拆分的店铺扫码记录ID
        Integer lastDetailId = middleRewardScanCodeDetailRelationDao.getMaxDetailId(gbcCompanyId);
        if (null == lastDetailId) {
            lastDetailId = 0;
        }
        // 需拆分处理的终端扫码记录
        Map<String, Object> searchMap = new HashMap<>();
        searchMap.put("rewardTypeArray", new String[]{"1", "2", "3"});
        searchMap.put("type", 1);
        searchMap.put("lastDetailId", lastDetailId);
        searchMap.put("comId", gbcCompanyId);
        List<RewardScanCodeDetailModel> details = rewardScanCodeDetailDao.selectCodeIn(searchMap);
        for (RewardScanCodeDetailModel detail : details) {
            String[] codeArray = detail.getCodeIn().split(",");
            Set<String> pingCodeSet = new HashSet<>();
            // code_in里的码数量=1，则只有盒码
            if (codeArray.length == 1) {
                pingCodeSet.add(codeArray[0]);
            } else {
                // code_in里的码数量>1，就去掉与qrcode字段相等的码，其余的都是盒码
                for (String code : codeArray) {
                    if (!code.equals(detail.getQrcode())) {
                        pingCodeSet.add(code);
                    }
                }
            }
            for (String pingCode : pingCodeSet) {
                MiddleRewardScanCodeDetailRelationModel addData = new MiddleRewardScanCodeDetailRelationModel();
                addData.setDetailId(detail.getId());
                addData.setPingCode(pingCode);
                if (pingCode != detail.getQrcode()) {
                    addData.setBoxCode(detail.getQrcode());
                }
                addData.setCompanyId(detail.getComId());
                middleRewardScanCodeDetailRelationDao.insert(addData);
            }
        }
    }

    public List<RewardScanCodeDetailModel> selectReportList(Map<String, Object> searchMap) {

        return rewardScanCodeDetailDao.selectReportList(searchMap);
    }


    /**
     * 按月和店进行去统计数据
     */
    public List<PurchaseRecordResp>  getPurchaseRecord(Map<String,Object> map) {


        return rewardScanCodeDetailDao.getPurchaseRecord(map);

    }
}
