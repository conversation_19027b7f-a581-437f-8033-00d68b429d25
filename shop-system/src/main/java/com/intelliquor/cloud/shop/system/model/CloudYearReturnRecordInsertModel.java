package com.intelliquor.cloud.shop.system.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 年返奖励配置表实体对象
 */
@Data
public class CloudYearReturnRecordInsertModel implements Serializable {

    public CloudYearReturnRecordInsertModel() {
        this.goodsModelList = new ArrayList<>();
    }

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private Integer id;

    /**
     * 活动名称
     */
    @ApiModelProperty(value = "活动名称",required = true)
    private String activityName;

    /**
     * 公司id
     */
    private Integer companyId;

    /**
     * 活动时间
     */
    @ApiModelProperty(value = "活动时间",required = true)
    private String activityTime;

    /**
     * 云店类型，3：分销商, 4:合伙人，5:终端 用,号隔开
     */
    @ApiModelProperty(value = "云店类型",required = true)
    private String cloudShopType;

    /**
     * 年度奖励：年度进货金额
     */
    @ApiModelProperty(value = "年度奖励：年度进货金额比例",required = true)
    private BigDecimal yearAward;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 删除状态 0-已删除 1-未删除
     */
    private Integer isDelete;

    /**
     * 启用状态，0：未启用，1：启用
     */
    private Integer enableStatus;

    /**
     * 年返奖励-商品关系
     */
    @ApiModelProperty(value = "年返奖励-商品关系",required = true)
    List<CloudYearReturnRecordGoodsModel> goodsModelList;

}
