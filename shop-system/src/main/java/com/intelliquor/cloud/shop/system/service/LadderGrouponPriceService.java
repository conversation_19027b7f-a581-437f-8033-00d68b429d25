package com.intelliquor.cloud.shop.system.service;

import com.intelliquor.cloud.shop.system.dao.LadderGrouponPriceDao;
import com.intelliquor.cloud.shop.system.model.LadderGrouponPriceModel;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
* 描述：阶梯拼团价格 服务实现层
* <AUTHOR>
* @date 2020-08-27
*/
@Service
public class LadderGrouponPriceService{

    @Autowired
    private LadderGrouponPriceDao ladderGrouponPriceDao;

    /**
    * 查询数据
    *
    * @return
    */
    public List<LadderGrouponPriceModel> selectList(Map<String, Object> searchMap) {
        return ladderGrouponPriceDao.selectList(searchMap);
    }


    /**
    * 新增数据
    *
    * @param model
    */
    public void insert(LadderGrouponPriceModel model) {
        ladderGrouponPriceDao.insert(model);
    }

    /**
    * 更新数据
    *
    * @param model
    */
    public void update(LadderGrouponPriceModel model) {
        ladderGrouponPriceDao.update(model);
    }

    /**
    * 删除数据
    *
    * @param id
    */
    public void delete(Integer id) {
        ladderGrouponPriceDao.delete(id);
    }

    /**
    * 根据ID查询数据
    *
    * @param id
    */
    public LadderGrouponPriceModel getById(Integer id) {
        return ladderGrouponPriceDao.getById(id);
    }
}