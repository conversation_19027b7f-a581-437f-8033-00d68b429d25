package com.intelliquor.cloud.shop.system.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 描述：任务中心-活动配置终端范围范围实体类
 *
 * <AUTHOR>
 * @date 2019-11-04
 */
@Data
public class ActivityTerminalRangeModel {

    @ApiModelProperty(value = "1按终端 2按行政区域")
    private Integer type;

    @ApiModelProperty(value = "")
    private String province;

    @ApiModelProperty(value = "")
    private String city;

    @ApiModelProperty(value = "")
    private String district;

    @ApiModelProperty(value = "终端id")
    private String shopId;

    @ApiModelProperty(value = "终端名称")
    private String name;

}
