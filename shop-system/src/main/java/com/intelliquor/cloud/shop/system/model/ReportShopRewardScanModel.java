package com.intelliquor.cloud.shop.system.model;

import com.intelliquor.cloud.shop.common.annotation.Excel;
import com.intelliquor.cloud.shop.common.annotation.ExcelTitle;
import com.intelliquor.cloud.shop.common.annotation.LevelExcelTitle;
import com.intelliquor.cloud.shop.common.annotation.Sheet;
import io.swagger.annotations.ApiModelProperty;

        import com.fasterxml.jackson.annotation.JsonFormat;

import java.math.BigDecimal;
import java.util.Date;

import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

/**
* 描述：终端销售统计实体类
* <AUTHOR>
* @date 2020-08-21
*/
@Excel(fileName = "终端销售统计.xls")
@Sheet(sheetNames = {"终端销售统计"}, numPerSheet = 50000, numOfSheet = {}, groupNumber = 1)
@LevelExcelTitle(titleNames = {"终端销售统计"}, titleLevel = 1, colSpans = {8}, groupNumber = 1)
@Data
public class ReportShopRewardScanModel {


    @ApiModelProperty(value = "")
    private Integer id;

    @ApiModelProperty(value = "区域编码")
    @ExcelTitle(titleName = "区域编码", index = 1, groupNumber = 1, width = 5000)
    private String orgCode;

    @ApiModelProperty(value = "区域名称")
    @ExcelTitle(titleName = "区域名称", index = 2, groupNumber = 1, width = 5000)
    private String orgName;

    @ApiModelProperty(value = "终端ID")
    private Integer shopId;

    @ApiModelProperty(value = "终端编码")
    @ExcelTitle(titleName = "终端编码", index = 3, groupNumber = 1, width = 5000)
    private String diyCode;

    @ApiModelProperty(value = "终端名称")
    @ExcelTitle(titleName = "终端名称", index = 4, groupNumber = 1, width = 5000)
    private String shopName;

    @ApiModelProperty(value = "产品编码")
    @ExcelTitle(titleName = "产品编码", index = 5, groupNumber = 1, width = 5000)
    private String goodsCode;

    @ApiModelProperty(value = "产品名称")
    @ExcelTitle(titleName = "产品名称", index = 6, groupNumber = 1, width = 5000)
    private String goodsName;

    @ApiModelProperty(value = "扫码数量")
    @ExcelTitle(titleName = "有效或待确定扫码数量", index = 7, groupNumber = 1, width = 5000)
    private Integer quantity;

    @ApiModelProperty(value = "扫码时间")
    @ExcelTitle(titleName = "扫码时间", index = 8, groupNumber = 1, width = 5000)
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date scanTime;

    @ApiModelProperty(value = "政策金额")
    @ExcelTitle(titleName = "政策金额", index = 9, groupNumber = 1, width = 5000)
    private BigDecimal amount;

    @ApiModelProperty(value = "公司id")
    private Integer companyId;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
    * 排序字段默认为id
    */
    private String sortCode = "id";

    /**
    * 排序规则默认为降序排列(DESC/ASC)
    */
    private String sortRole = "DESC";


    public Integer getId() {
    return id;
    }

    public void setId(Integer id) {
    this.id = id;
    }

    public Integer getShopId() {
    return shopId;
    }

    public void setShopId(Integer shopId) {
    this.shopId = shopId;
    }

    public String getGoodsCode() {
    return goodsCode;
    }

    public void setGoodsCode(String goodsCode) {
    this.goodsCode = goodsCode;
    }

    public String getGoodsName() {
    return goodsName;
    }

    public void setGoodsName(String goodsName) {
    this.goodsName = goodsName;
    }

    public Integer getQuantity() {
    return quantity;
    }

    public void setQuantity(Integer quantity) {
    this.quantity = quantity;
    }

    public Date getScanTime() {
    return scanTime;
    }

    public void setScanTime(Date scanTime) {
    this.scanTime = scanTime;
    }


    public Integer getCompanyId() {
    return companyId;
    }

    public void setCompanyId(Integer companyId) {
    this.companyId = companyId;
    }

    public Date getCreateTime() {
    return createTime;
    }

    public void setCreateTime(Date createTime) {
    this.createTime = createTime;
    }
    public String getSortRole() {
    return sortRole;
    }

    public void setSortRole(String sortRole) {
    this.sortRole = sortRole;
    }

    public String getSortCode() {
    return sortCode;
    }

    public void setSortCode(String sortCode) {
    this.sortCode = sortCode;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public String getOrgCode() {
        return orgCode;
    }

    public void setOrgCode(String orgCode) {
        this.orgCode = orgCode;
    }

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

    public String getDiyCode() {
        return diyCode;
    }

    public void setDiyCode(String diyCode) {
        this.diyCode = diyCode;
    }

    public String getShopName() {
        return shopName;
    }

    public void setShopName(String shopName) {
        this.shopName = shopName;
    }
}