package com.intelliquor.cloud.shop.system.service;

import cn.hutool.core.codec.Base64;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Maps;
import com.intelliquor.cloud.shop.common.dao.ShopDao;
import com.intelliquor.cloud.shop.common.dao.ShopUserDao;
import com.intelliquor.cloud.shop.common.entity.*;
import com.intelliquor.cloud.shop.common.enums.MsgTypeEnum;
import com.intelliquor.cloud.shop.common.enums.OrderTypeEnum;
import com.intelliquor.cloud.shop.common.exception.BusinessException;
import com.intelliquor.cloud.shop.common.model.ShopModel;
import com.intelliquor.cloud.shop.common.model.ShopUserModel;
import com.intelliquor.cloud.shop.common.utils.*;
import com.intelliquor.cloud.shop.system.dao.*;
import com.intelliquor.cloud.shop.system.model.*;
import com.intelliquor.cloud.shop.system.model.req.GrouponOrderPageReq;
import com.intelliquor.cloud.shop.system.model.req.LadderGrouponOrderPageReq;
import com.intelliquor.cloud.shop.system.model.req.LadderGrouponOrderSendReq;
import com.intelliquor.cloud.shop.system.model.req.TopStatisticsData;
import com.intelliquor.cloud.shop.system.model.resp.LadderGrouponOrderDetailResp;
import com.intelliquor.cloud.shop.system.model.resp.LadderGrouponOrderPageResp;
import com.intelliquor.cloud.shop.system.util.enums.LadderGroupOrderEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.aop.framework.AopContext;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.*;

/**
* 描述：消费者扫码明细表 服务实现层
* <AUTHOR>
* @date 2020-08-27
*/
@Service
@Slf4j
public class LadderGrouponOrderService{

    @Autowired
    private LadderGrouponOrderDao ladderGrouponOrderDao;

    @Autowired
    private OrderDownloadCenterService downloadCenterService;

    @Autowired
    private LadderGrouponOrderDetailDao ladderGrouponOrderDetailDao;

    @Autowired
    OrderCenterService orderCenterService;


    @Autowired
    private RewardScanBalanceDao rewardScanBalanceDao;

    @Autowired
    private ShopUserDao shopUserDao;

    @Autowired
    RestTemplate restTemplate;

    @Value("${groupBuy.sendTemplateMessageSubscribeUrL}")
    private String sendTemplateMessageSubscribeUrL;

    @Value("${shopMemebr.ladderGroupOrderRefundUrl}")
    private String ladderGroupOrderRefundUrl;

    @Autowired
    private LadderGrouponActivityDao ladderGrouponActivityDao;

    @Autowired
    private ShopDao shopDao;

    @Autowired
    private RewardScanBalanceService rewardScanBalanceService;

    @Autowired
    private MessageTemplateService messageTemplateService;

    @Autowired
    private MemberMsgService memberMsgService;

    @Value("${cloud-shop.ladder-order.autoConfirmReceivedFlag}")
    private String autoConfirmReceivedFlag;

    @Autowired
    private DealerDao dealerDao;


    @Autowired
    private LadderGrouponDataDao grouponDataDao;
    /**
    * 查询数据
    *
    * @return
    */
    public List<LadderGrouponOrderModel> selectList(Map<String, Object> searchMap) {
        return ladderGrouponOrderDao.selectList(searchMap);
    }

    /**
     * 查询后台订单数据
     *
     * @return
     */
    public List<LadderGrouponOrderModel> selectListByPage(Map<String, Object> searchMap) {
        return ladderGrouponOrderDao.selectByPage(searchMap);
    }
    /**
     * 查询后台订单数据
     *
     * @return
     */
    public List<LadderGrouponOrderModel> pageList(Map<String, Object> searchMap) {
        List<LadderGrouponOrderModel>  list= ladderGrouponOrderDao.selectByPage(searchMap);
        if(!CollectionUtils.isEmpty(list)){

            for(LadderGrouponOrderModel orderModel:list) {

                if (orderModel.getReceivedType() == 1) {
                    //自提
                    ShopModel shopModel = shopDao.getById(orderModel.getShopId());
                    if (shopModel != null) {
                        orderModel.setSellerName(shopModel.getName());
                    } else {
                        orderModel.setSellerName("");
                    }
                } else {
                    if (orderModel.getSendType()==null||Integer.valueOf(0).equals(orderModel.getSendType())||Integer.valueOf(1).equals(orderModel.getSendType())) {
                        //经销商未发货
                        orderModel.setSellerName("厂家");
                    }  else {
                        DealerModel dealer = dealerDao.getByCode(orderModel.getDealerCode());
                        //经销商发货
                        orderModel.setSellerName(dealer.getDealerName());
                    }
                }

            }


        }

        return list;
    }

    /**
     * 阶梯拼团小程序端订单列表
     * @param req
     * @return
     */
    public PageInfo<LadderGrouponOrderPageResp> getOrderList(GrouponOrderPageReq req) {

        PageHelper.startPage(req.getPageNum(), req.getPageSize());
        List<LadderGrouponOrderPageResp> grouponOrderList = ladderGrouponOrderDao.selectOrderListByPage(SearchUtil.getSearch(req));
        if (grouponOrderList != null && grouponOrderList.size() > 0) {
            for (LadderGrouponOrderPageResp grouponOrderPageResp : grouponOrderList) {
                //查询订单商品数据
                List<LadderGrouponOrderDetailModel> orderDetailList=new ArrayList<>();
                LadderGrouponOrderDetailModel ladderGrouponOrderDetailModel= ladderGrouponOrderDetailDao.selectByOrderCode(grouponOrderPageResp.getOrderCode());
                if (ladderGrouponOrderDetailModel!=null) {
                    orderDetailList.add(ladderGrouponOrderDetailModel);
                }
                BigDecimal totolMoney = BigDecimal.ZERO;
                if (orderDetailList != null && orderDetailList.size() > 0) {
                    grouponOrderPageResp.setGrouponOrderDetailList(orderDetailList);
                    for (LadderGrouponOrderDetailModel grouponOrderDetail : orderDetailList) {
                        BigDecimal goodsTotalPrice = BigDecimal.ZERO;
                        if (grouponOrderPageResp.getOrderType() == 1) {
                            //拼团 ->拼团价格*数量
                            goodsTotalPrice = grouponOrderDetail.getGoodsGrouponPrice().multiply(new BigDecimal(grouponOrderDetail.getQty()));
                        } else {
                            //单人 ->原价*数量
                            goodsTotalPrice = grouponOrderDetail.getGoodsPrice().multiply(new BigDecimal(grouponOrderDetail.getQty()));
                        }
                        // 实付金额
                        totolMoney = totolMoney.add(goodsTotalPrice);
                        grouponOrderDetail.setGoodsTotalPrice(goodsTotalPrice);
                    }
                }
                //通过activityId查询出活动详情
                GroupActivityModel groupActivityModel = new GroupActivityModel();
                groupActivityModel.setId(grouponOrderPageResp.getActivityId());
                LadderGrouponActivityModel groupActivityOne =  ladderGrouponActivityDao.getById(grouponOrderPageResp.getActivityId());
                //获取团长返利比例
                Integer grouperRebateType=0;
                BigDecimal grouperRebate = new BigDecimal(0);
                if (null != groupActivityOne) {
                    grouperRebate = groupActivityOne.getGrouperRebate();
                    grouperRebateType=groupActivityOne.getGrouperRebateType();
                }


                //计算待结算金额（订单金额*返利比例）
                if (grouperRebateType.equals(0)) {

                    BigDecimal baifenbi = new BigDecimal(0.01);
                    BigDecimal temp = grouponOrderPageResp.getOrderAmount().multiply(grouperRebate).setScale(2, BigDecimal.ROUND_HALF_UP);
                    BigDecimal waitAccountAmount = temp.multiply(baifenbi).setScale(2, BigDecimal.ROUND_HALF_UP);
                    //设置待结算金额
                    grouponOrderPageResp.setWaitAccountAmount(waitAccountAmount);
                } else {

                    grouponOrderPageResp.setWaitAccountAmount(grouperRebate);
                }


                //计算预计到账时间
                Map<String,Object> groupMap=new HashMap();
                groupMap.put("grouponCode",grouponOrderPageResp.getGrouponCode());
                groupMap.put("companyId",grouponOrderPageResp.getCompanyId());
                List<LadderGrouponDataModel> selectByGrouponCode = grouponDataDao.selectListByGroupCode(groupMap);
                if (null != selectByGrouponCode && selectByGrouponCode.size() > 0) {
                    if (selectByGrouponCode.get(0).getCompleteTime() != null) {
                        Calendar calendar = Calendar.getInstance();
                        calendar.setTime(selectByGrouponCode.get(0).getCompleteTime());
                        calendar.add(Calendar.DATE, 7);//增加7天
                        grouponOrderPageResp.setExpectedAccountTime(calendar.getTime());
                    }
                }

                if (grouponOrderPageResp.getOrderStatus() == 4) {
                    grouponOrderPageResp.setExpectedAccountTime(grouponOrderPageResp.getReceivedTime());
                }
                //总金额
                grouponOrderPageResp.setTotalMoney(totolMoney.add(grouponOrderPageResp.getFreight()));
                //处理数据
                if (grouponOrderPageResp.getReceivedType() == 1) {
                    //自提
                    ShopModel shopModel = shopDao.getById(grouponOrderPageResp.getShopId());
                    if (shopModel != null) {
                        grouponOrderPageResp.setSellerName(shopModel.getName());
                    } else {
                        grouponOrderPageResp.setSellerName("");
                    }
                } else {
                    if (Integer.valueOf(0).equals(grouponOrderPageResp.getSendType())) {
                        //经销商未发货
                        grouponOrderPageResp.setSellerName("厂家");
                    } else if (Integer.valueOf(1).equals(grouponOrderPageResp.getSendType())) {
                        //厂家发货
                        grouponOrderPageResp.setSellerName("厂家");
                    } else {
                        DealerModel dealer = dealerDao.getByCode(grouponOrderPageResp.getDealerCode());
                        //经销商发货
                        grouponOrderPageResp.setSellerName(dealer.getDealerName());
                        grouponOrderPageResp.setDealerName(dealer.getDealerName());
                        grouponOrderPageResp.setDealerMan(dealer.getLinkman());
                        grouponOrderPageResp.setDealerPhone(dealer.getPhone());
                    }
                    //7天自动收货
                   /* if (grouponOrderPageResp.getOrderStatus() == 3 &&
                            TimeUtilis.datemindateSecond(new Date(),grouponOrderPageResp.getSendTime()) > 7 * 24 * 60 * 60) {
                        GrouponOrder grouponOrder = new GrouponOrder();
                        grouponOrder.setId(grouponOrderPageResp.getId());
                        grouponOrder.setOrderStatus(4);
                        grouponOrderDao.updateByPrimaryKeySelective(grouponOrder);
                    }*/

                }
                //判断是否有来自云众的退款申请
             /*   GrouponApplyBackModel grouponApplyBackModel = grouponApplyBackDao.load(grouponOrderPageResp.getOrderCode());
                if (null != grouponApplyBackModel) {//说明这个订单有来自云众的退款申请
                    grouponOrderPageResp.setIsBackApplyFromYunzhong(1);
                } else {//说明这个订单没有来自云众的退款申请
                    grouponOrderPageResp.setIsBackApplyFromYunzhong(0);
                }*/
            }
        }
        PageInfo<LadderGrouponOrderPageResp> pageInfo = new PageInfo<>(grouponOrderList);
        return pageInfo;
    }

    /**
     * 查询订单详情
     *
     * @param orderId
     * @return
     */
    public LadderGrouponOrderDetailResp findOne(Integer orderId){
        log.info("查询订单详情的id为orderId=====" + orderId);
        LadderGrouponOrderModel orderModel = ladderGrouponOrderDao.getById(orderId);
        if (orderModel == null) {

            throw new BusinessException("该订单不存在");
        }
        LadderGrouponOrderDetailResp grouponOrderDetailResp = new LadderGrouponOrderDetailResp();
        BeanUtils.copyProperties(orderModel, grouponOrderDetailResp);
        // 获取订单详情
        Map<String, Object> searchMap = new HashMap<>();
        searchMap.put("orderCode", orderModel.getOrderCode());
        searchMap.put("sortCode", "id");
        searchMap.put("sortRole", "desc");
        List<LadderGrouponOrderDetailModel> orderDetailModelList = ladderGrouponOrderDetailDao.selectList(searchMap);
        if (!CollectionUtils.isEmpty(orderDetailModelList)) {

            for (LadderGrouponOrderDetailModel orderDetailModel : orderDetailModelList) {
                BigDecimal totalPrice=BigDecimal.ZERO;
                totalPrice=orderDetailModel.getGoodsGrouponPrice().multiply(new BigDecimal(orderDetailModel.getQty()));
                orderDetailModel.setGoodsTotalPrice(totalPrice);
            }
            grouponOrderDetailResp.setGrouponOrderDetailList(orderDetailModelList);

        }

        if (grouponOrderDetailResp.getReceivedType() == 1) {
            //自提
            ShopModel shopModel = shopDao.getById(grouponOrderDetailResp.getShopId());
            if (shopModel == null) {
                throw new BusinessException("该订单门店数据违法");
            }
            //门店信息
            grouponOrderDetailResp.setSellerName(shopModel.getName());
            grouponOrderDetailResp.setSellerMobile(shopModel.getLinkphone());
            grouponOrderDetailResp.setSellerAddress(shopModel.getAddress());


        } else {

            if (Integer.valueOf(0).equals(orderModel.getSendType())||Integer.valueOf(1).equals(orderModel.getSendType())) {
                //经销商未发货
                grouponOrderDetailResp.setSellerName("厂家");
            } else {
                DealerModel dealer = dealerDao.getByCode(orderModel.getDealerCode());
                if(dealer!=null){
                    //经销商发货
                    grouponOrderDetailResp.setSellerName(dealer.getDealerName());
                    grouponOrderDetailResp.setDealerName(dealer.getDealerName());
                    grouponOrderDetailResp.setDealerMan(dealer.getLinkman());
                    grouponOrderDetailResp.setDealerPhone(dealer.getPhone());
                }

            }

        }

        // 查询团数据
        Map searchDataMap=new HashMap();
        searchDataMap.put("companyId",orderModel.getCompanyId());
        searchDataMap.put("grouponCode",orderModel.getGrouponCode());
        List<LadderGrouponDataModel>  dataModelList=grouponDataDao.selectListByGroupCode(searchDataMap);
        if(!CollectionUtils.isEmpty(dataModelList)){
            grouponOrderDetailResp.setCompleteTime(dataModelList.get(0).getCompleteTime());
        }

        return grouponOrderDetailResp;
    }

    /**
    * 新增数据
    *
    * @param model
    */
    public void insert(LadderGrouponOrderModel model) {
        ladderGrouponOrderDao.insert(model);
    }

    /**
    * 更新数据
    *
    * @param model
    */
    public void update(LadderGrouponOrderModel model) {
        ladderGrouponOrderDao.update(model);
    }

    /**
    * 删除数据
    *
    * @param id
    */
    public void delete(Integer id) {
        ladderGrouponOrderDao.delete(id);
    }

    /**
    * 根据ID查询数据
    *
    * @param id
    */
    public LadderGrouponOrderModel getById(Integer id) {
        return ladderGrouponOrderDao.getById(id);
    }

    public Long getOrderGoodsCountByOpenId(Integer activityId, String openId) {
        Map<String, Object> searchMap = new HashMap<>();
        searchMap.put("activityId", activityId);
        searchMap.put("openId", openId);
        return ladderGrouponOrderDao.getOrderGoodsCountByOpenId(searchMap);
    }

    /**
     * 应退金额退款任务
     */
     public void handleRefundReturnAmount(){
         // 每次处理300条订单
         PageHelper.startPage(1, 300);
         List<LadderGrouponOrderModel>  orderModelList= ladderGrouponOrderDao.selectRefundData();
         if (CollectionUtils.isEmpty(orderModelList)) {
             return ;
         }
         // 循环需要退金额的订单
         for(LadderGrouponOrderModel orderModel :orderModelList){
             log.info("orderCode======="+orderModel.getOrderCode()+",returnAmount===="+orderModel.getReturnAmount());
             // 先判断退款金额是不是0.如果是0直接更新状态
             if(orderModel.getReturnAmount().compareTo(BigDecimal.ZERO)==0){
                 LadderGrouponOrderModel updateModel=new LadderGrouponOrderModel();
                 updateModel.setId(orderModel.getId());
                 updateModel.setRefundReturnApplyTime(new Date());
                 updateModel.setUpdateTime(new Date());
                 updateModel.setRefundReturnStatus(2);
                 ladderGrouponOrderDao.update(updateModel);
                 return;
             }


             // 首先更新应退金额状态
             LadderGrouponOrderModel updateModel=new LadderGrouponOrderModel();
             updateModel.setId(orderModel.getId());
             updateModel.setRefundReturnApplyTime(new Date());
             updateModel.setUpdateTime(new Date());
             updateModel.setRefundReturnStatus(1);
             ladderGrouponOrderDao.update(updateModel);

             // 以下请求云店进行退款
             ResponseEntity<String> responseEntity = null;
             try {
                 orderModel.setRefundType(LadderGroupOrderEnum.REFUND_TYPE_RETURN.getValue());
                 HttpHeaders headers = this.getRequestHeaders(orderModel.getCompanyId());
                 HttpEntity<LadderGrouponOrderModel> entity = new HttpEntity<>(orderModel, headers);
                 responseEntity = restTemplate.postForEntity(ladderGroupOrderRefundUrl, entity, String.class);
             } catch (Exception e) {
                 log.error("请求云众退款异常",e);
                 continue;
             }
             log.info("退款结果通知：{}" ,JSON.toJSONString(responseEntity));
             if (responseEntity.getStatusCode().equals(HttpStatus.OK)) {
                 String body = responseEntity.getBody();
                 JSONObject resp = JSONObject.parseObject(body);
                 if ("200".equals(resp.getString("code"))) {
                     log.info("退款成功, 订单号：{}" , orderModel.getOrderCode());
                 } else {
                     log.error("退款失败, 订单号：{}",orderModel.getOrderCode());
                     continue;
                 }
             } else {
                 log.error("请求云众退款异常, 订单号：{}", orderModel.getOrderCode());
                 continue;
             }
         }
     }



    /**
     * 阶梯团订单导出
     * @param model
     * @throws IOException
     */
    @Async
    public void export(LadderGrouponOrderPageReq model) throws IOException {
        OrderDownloadCenterModel downloadCenterModel = this.addGrouponOrderDownloadCenter(model);
        String fileUrl = this.exportOrderFile(model);
        downloadCenterModel.setFilePath(fileUrl);
        downloadCenterService.updOrderDownloadCenter(downloadCenterModel);
    }

    /**
     * 添加下载中心数据
     *
     * @param model
     * @return
     */
    public OrderDownloadCenterModel addGrouponOrderDownloadCenter(LadderGrouponOrderPageReq model) {
        OrderDownloadCenterModel downloadCenterModel = model.getDownloadCenterModel();

        downloadCenterModel.setType(OrderTypeEnum.LADDERGROUPONORDER.getValue().shortValue());

        downloadCenterService.addOrderDownloadCenter(downloadCenterModel);
        return downloadCenterModel;
    }

    /**
     * 创建文件并上传至阿里云OSS
     *
     * @param model
     * @return
     * @throws IOException
     */
    public String exportOrderFile(LadderGrouponOrderPageReq model) throws IOException {
        List<LadderGrouponOrderPageResp> list = this.getOrderListNoPage(model);
        FileItem fileItem = ExcelTools.exportByFile(list, 1);
        String fileName = (new StringBuffer()).append("JTTDD").append(TimeUtilis.getCurrentFormatTime("yyyyMMddHHmmss")).append(".xls").toString();
        return downloadCenterService.getFileUrl(fileName, fileItem.getContent());

    }

    public List<LadderGrouponOrderPageResp> getOrderListNoPage(LadderGrouponOrderPageReq req) {
        List<LadderGrouponOrderPageResp> grouponOrderList = ladderGrouponOrderDao.selectOrderList(SearchUtil.getSearch(req));
        return grouponOrderList;
    }

    /**
     * 发货
     * @param req
     */
    @Transactional(rollbackFor = Exception.class)
    public void send(LadderGrouponOrderSendReq req) {
        LadderGrouponOrderModel grouponOrderOld = ladderGrouponOrderDao.getById(Integer.parseInt(req.getId().toString()));
        if (grouponOrderOld == null) {
            throw new BusinessException("当前订单不存在");
        }
        if (grouponOrderOld.getOrderStatus() != 2
                && grouponOrderOld.getOrderStatus() != 21
                && grouponOrderOld.getOrderStatus() != 22
                && grouponOrderOld.getOrderStatus() != 23
                ) {
            throw new BusinessException("当前订单状态错误");
        }
        Map<String, Object> searchMap = Maps.newHashMap();
        searchMap.put("orderCode",grouponOrderOld.getOrderCode());
        searchMap.put("sortCode","id");
        searchMap.put("sortRole","desc");
        List<LadderGrouponOrderDetailModel> orderDetailList = ladderGrouponOrderDetailDao.selectList(searchMap);
        if (CollectionUtils.isEmpty(orderDetailList)) {
            throw new BusinessException("当前订单商品信息错误");
        }
        LadderGrouponOrderModel grouponOrderUp = new LadderGrouponOrderModel();
        grouponOrderUp.setId(req.getId());
        grouponOrderUp.setOrderStatus(3);
        grouponOrderUp.setExpressCode(req.getExpressCode());
        grouponOrderUp.setExpressCompany(req.getExpressCompany());
        grouponOrderUp.setSendTime(new Date());
        ladderGrouponOrderDao.update(grouponOrderUp);
        //查询更新后的订单信息
        LadderGrouponOrderModel grouponOrderNew = ladderGrouponOrderDao.getById(Integer.parseInt(req.getId().toString()));
        // 发送kafka消息
        GrouponOrder grouponOrder = new GrouponOrder();
        BeanUtils.copyProperties(grouponOrderNew, grouponOrder);
        grouponOrder.setOrderType(7);//阶梯团订单类型在订单中心中为7
        GrouponOrderDetail grouponOrderDetail = new GrouponOrderDetail();
        BeanUtils.copyProperties(orderDetailList.get(0), grouponOrderDetail);
        orderCenterService.sendOrderMessage(grouponOrder, grouponOrderDetail, "update");

        //发送订阅消息
        WechatAppletMessage message = setWechatAppletMessage(grouponOrderNew.getAppid(),
                grouponOrderNew.getOpenid(),
                grouponOrderNew.getOrderCode(),
                grouponOrderNew.getExpressCode(),
                grouponOrderNew.getExpressCompany(),
                grouponOrderNew.getSendTime(),
                grouponOrderDetail);
        sendMessage(req.getCompanyId(), message);

    }

    /**
     * 发送微信消息
     * @param companyId
     * @param message
     */
    public void sendMessage(Integer companyId, WechatAppletMessage message) {
        ResponseEntity<String> responseEntity = null;
        try {
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.set("authLocal", AuthLocalUtil.getAuthLocal(companyId));
            headers.set("userId", companyId + "");
            HttpEntity<WechatAppletMessage> entity = new HttpEntity<>(message);
            responseEntity = restTemplate.postForEntity(sendTemplateMessageSubscribeUrL, entity, String.class);
            log.info("云店请求发订阅消息---{}" , JSON.toJSONString(responseEntity));
        } catch (Exception e) {
            log.error("请求微信发送订阅消息异常", e.getMessage());
            return;
        }
        if (responseEntity.getStatusCode().equals(HttpStatus.OK)) {
            String body = responseEntity.getBody();
            JSONObject result = JSONObject.parseObject(body);
            if (result.getInteger("code") == 0) {
                log.info("发送订阅消息成功");
            } else {
                log.error("发送订阅消息失败：{}" ,result.getInteger("code") + "_" + result.getString("error"));
            }
        } else {
            log.error("请求微信发送订阅消息失败");
        }

    }

    /**
     * 批量发货
     * @param companyId
     * @param file
     * @return
     * @throws Exception
     */
    @Transactional(rollbackFor = Exception.class)
    public int importExcel(Integer companyId, MultipartFile file) throws Exception {
        int result = 0;
        if (!file.isEmpty()) {
            String suffix = Objects.requireNonNull(file.getOriginalFilename()).substring(file.getOriginalFilename().lastIndexOf("."));
            if (!".xlsx".equals(suffix)) {
                throw new Exception("导入的文件格式不正确");
            }
            //增加EXCEL表格处理开始
            Response<ExcelInfo> excelInfoResponse = ExcelTools.transferStreamToExcel(file.getInputStream());
            ExcelInfo excelInfo = excelInfoResponse.getResult();
            if (excelInfo == null) {
                throw new Exception(excelInfoResponse.getMsg());
            }
            List<ExcelTitleInfo> titleInfoList = excelInfo.getTitleInfo();
            if (CollectionUtils.isEmpty(titleInfoList) || titleInfoList.size() != 3) {
                throw new Exception("导入的数据格式不正确");
            }
            List<LadderGrouponOrderModel> list = new ArrayList<>();
            List<List<ExcelBodyInfo>> bodyInfoList = excelInfo.getBodyInfo();
            List<WechatAppletMessage> wechatAppletMessageList = new ArrayList<>();
            for (int i = 0; i < bodyInfoList.size(); i++) {
                List<ExcelBodyInfo> excelBodyInfoList = bodyInfoList.get(i);
                LadderGrouponOrderModel grouponOrder = new LadderGrouponOrderModel();
                grouponOrder.setOrderCode(excelBodyInfoList.get(0).getValue() == null?"":excelBodyInfoList.get(0).getValue().trim());
                grouponOrder.setExpressCode(excelBodyInfoList.get(1).getValue());
                grouponOrder.setExpressCompany(excelBodyInfoList.get(2).getValue());
                grouponOrder.setSendTime(new Date());
                list.add(grouponOrder);
            }
            //后台校验文件格式正确性
            if (CollectionUtils.isEmpty(list)) {
                throw new Exception("导入的文件没有数据！");
            }
            for (int i = 0; i < list.size(); i++) {
                LadderGrouponOrderModel grouponOrderInfo = list.get(i);
                String orderCode = grouponOrderInfo.getOrderCode();
                if (StringUtils.isEmpty(orderCode)) {
                    throw new BusinessException("第" + (i + 2) + "行订单号为空");
                }
                if (StringUtils.isEmpty(grouponOrderInfo.getExpressCode())) {
                    throw new BusinessException("第" + (i + 2) + "行物流单号为空");
                }
                if (StringUtils.isEmpty(grouponOrderInfo.getExpressCompany())) {
                    throw new BusinessException("第" + (i + 2) + "行物流公司为空");
                }
                Map<String, Object> searchMap = Maps.newHashMap();
                searchMap.put("orderCode",orderCode);
                searchMap.put("sortCode","id");
                searchMap.put("sortRole","desc");
                List<LadderGrouponOrderModel> ladderGrouponOrderModelsOld = ladderGrouponOrderDao.selectList(searchMap);
                if (CollectionUtils.isEmpty(ladderGrouponOrderModelsOld)) {
                    throw new BusinessException("第" + (i + 2) + "行" + orderCode + "订单号不存在");
                }
                LadderGrouponOrderModel groupOrderOld = ladderGrouponOrderModelsOld.get(0);
                if(!groupOrderOld.getCompanyId().equals(companyId)){
                    throw new BusinessException("第" + (i + 2) + "行" + orderCode + "订单号不属于当前公司");
                }
                if (groupOrderOld.getOrderStatus() != 2
                        && groupOrderOld.getOrderStatus() != 21
                        && groupOrderOld.getOrderStatus() != 22
                        && groupOrderOld.getOrderStatus() != 23
                        ) {
                    throw new BusinessException("第" + (i + 2) + "行订单状态错误");
                }
                List<LadderGrouponOrderDetailModel> orderDetailList = ladderGrouponOrderDetailDao.selectList(searchMap);
                if (CollectionUtils.isEmpty(orderDetailList)) {
                    throw new BusinessException("第" + (i + 2) + "行订单商品信息错误");
                }

                grouponOrderInfo.setId(groupOrderOld.getId());
                grouponOrderInfo.setShopId(groupOrderOld.getShopId());
                grouponOrderInfo.setAppid(groupOrderOld.getAppid());
                grouponOrderInfo.setOpenid(groupOrderOld.getOpenid());
                grouponOrderInfo.setOrderStatus(3);

                int resultCount = ladderGrouponOrderDao.updateForDeliver(
                        grouponOrderInfo.getOrderStatus(),
                        grouponOrderInfo.getSendTime(),
                        grouponOrderInfo.getOrderCode(),
                        grouponOrderInfo.getExpressCode(),
                        grouponOrderInfo.getExpressCompany());
                //查询更新后最新的订单信息
                List<LadderGrouponOrderModel> ladderGrouponOrderModelsNew = ladderGrouponOrderDao.selectList(searchMap);
                GrouponOrder order2 = new GrouponOrder();
                BeanUtils.copyProperties(ladderGrouponOrderModelsNew.get(0),order2);
                order2.setOrderType(7);//阶梯团在订单中心类型为7
                GrouponOrderDetail detail = new GrouponOrderDetail();
                BeanUtils.copyProperties(orderDetailList.get(0), detail);
                // 发送kafka消息
                orderCenterService.sendOrderMessage(order2, detail, "update");
                //状态更新成功，且是第一次更新（SendTime为空）才发送消息
                if (resultCount > 0 && groupOrderOld.getSendTime() == null) {
                    //设置消息体
                    WechatAppletMessage wechatAppletMessage = setWechatAppletMessage(grouponOrderInfo.getAppid(),
                            grouponOrderInfo.getOpenid(),
                            grouponOrderInfo.getOrderCode(),
                            grouponOrderInfo.getExpressCode(),
                            grouponOrderInfo.getExpressCompany(),
                            grouponOrderInfo.getSendTime(),
                            detail);
                    wechatAppletMessageList.add(wechatAppletMessage);
                }
            }
            //增加EXCEL表格处理结束
            result = 1;
            ((LadderGrouponOrderService) AopContext.currentProxy()).sendWechatMessageAsync(companyId, wechatAppletMessageList);
        }
        return result;
    }

    /**
     * 异步批量发送微信消息
     * @param companyId
     * @param wechatAppletMessageList
     */
    @Async
    public void sendWechatMessageAsync(Integer companyId, List<WechatAppletMessage> wechatAppletMessageList) {
        System.out.println("开始发送微信订阅消息");
        wechatAppletMessageList.stream().forEach(d->{
            sendMessage(companyId, d);
        });
        System.out.println("结束发送微信订阅消息");
    }

    /**
     * 设置微信订阅消息
     *
     * @param appid
     * @param openid
     * @param orderCode
     * @param expressCode
     * @param expressCompany
     * @param sendTime
     * @return
     */
    public WechatAppletMessage setWechatAppletMessage(String appid, String openid, String orderCode, String expressCode, String expressCompany, Date sendTime, GrouponOrderDetail grouponOrderDetail) {
        //发送订阅消息
        WechatAppletMessage message = new WechatAppletMessage();
        message.setAppid(appid);
        message.setOpenid(openid);
        message.setTemplate("DELIVERY_NOTICE");
        Map<String, TemplateData> data = new HashMap<>();
        //订单编号
        TemplateData keyword1 = new TemplateData();
        keyword1.setValue(orderCode);
        data.put("character_string7", keyword1);

        //快递公司
        TemplateData keyword2 = new TemplateData();
        keyword2.setValue(expressCompany);
        data.put("thing1", keyword2);

        //快递单号
        TemplateData keyword3 = new TemplateData();
        keyword3.setValue(expressCode);
        data.put("character_string2", keyword3);

        //发货时间
        TemplateData keyword4 = new TemplateData();
        keyword4.setValue(TimeUtilis.getStringDate("yyyy-MM-dd HH:mm:ss", sendTime));
        data.put("time3", keyword4);

        //订单商品
        TemplateData keyword5 = new TemplateData();
        String goodsName = "";
        if (grouponOrderDetail != null ) {
            goodsName = grouponOrderDetail.getGoodsName();
            if (goodsName.length() > 20) {
                goodsName = goodsName.substring(0, 20);
            }
        } else {
            goodsName = "无";
        }

        keyword5.setValue(goodsName);
        data.put("thing5", keyword5);
        message.setData(data);
        System.out.println(JSON.toJSONString(message));
        return message;
    }

    /**
     * 退款
     * @param order
     * @throws Exception
     */
    @Transactional(rollbackFor = Exception.class)
    public void refund(LadderGrouponOrderModel order) throws Exception {
        //退款
        LadderGrouponActivityModel activityModel = new LadderGrouponActivityModel();
        activityModel.setCompanyId(order.getCompanyId());
        List<LadderGrouponOrderModel> orderList = new ArrayList<>();
        orderList.add(order);
        Integer result = groupOrderBackMoney(activityModel, orderList, LadderGroupOrderEnum.REFUND_TYPE_ORDER.getValue());
        if (result <= 0) {
            throw new Exception("退款失败");
        }
        //更改订单状态为 7退款中
        LadderGrouponOrderModel orderModel = new LadderGrouponOrderModel();
        orderModel.setOrderCode(order.getOrderCode());
        orderModel.setOrderStatus(LadderGroupOrderEnum.REFUNDING.getValue());
        orderModel.setRefundingTime(new Date());
        ladderGrouponOrderDao.updateStatusByOrderCode(orderModel);

        Map<String, Object> searchMap = Maps.newHashMap();
        searchMap.put("orderCode",order.getOrderCode());
        searchMap.put("sortCode","id");
        searchMap.put("sortRole","desc");
        List<LadderGrouponOrderModel> ladderGrouponOrderModels = ladderGrouponOrderDao.selectList(searchMap);
        if (CollectionUtils.isEmpty(ladderGrouponOrderModels)) {
            throw new BusinessException(order.getOrderCode()+"订单信息错误");
        }
        GrouponOrder order2 = new GrouponOrder();
        BeanUtils.copyProperties(ladderGrouponOrderModels.get(0),order2);
        order2.setOrderType(7);//阶梯团在订单中心类型为7
        order2.setRefundingTime(orderModel.getRefundingTime());
        order2.setOrderStatus(orderModel.getOrderStatus());

        List<LadderGrouponOrderDetailModel> orderDetailList = ladderGrouponOrderDetailDao.selectList(searchMap);
        if (CollectionUtils.isEmpty(orderDetailList)) {
            throw new BusinessException(order.getOrderCode()+"订单商品信息错误");
        }
        GrouponOrderDetail detail = new GrouponOrderDetail();
        BeanUtils.copyProperties(orderDetailList.get(0), detail);
        // 发送kafka消息
        orderCenterService.sendOrderMessage(order2, detail, "update");

        //如果订单已确认收货，扣除终端返利金额
        if (Integer.valueOf(4).equals(order.getOrderStatus())) {
            Map<String, Object> paramMap = new HashMap<>();
            paramMap.put("shopId", order.getShopId());
            paramMap.put("companyId", order.getCompanyId());
            paramMap.put("sourceOrderCode", order.getOrderCode());
            paramMap.put("earningType", 11);
            paramMap.put("remark", order.getOrderCode() + " 团长奖励");
            List<RewardScanBalanceModel> balanceList = rewardScanBalanceDao.selectList(searchMap);
            if (!org.springframework.util.CollectionUtils.isEmpty(balanceList)) {
                RewardScanBalanceModel balanceModel = balanceList.get(0);
                ShopUserModel shopUser = shopUserDao.getPrimaryAccountByShopId(order.getShopId());
                if (balanceModel.getAmount().compareTo(shopUser.getAmount()) > 0) {
                    //返利金额大于终端店余额则不处理
                } else {
                    //删除收支明细
                    balanceModel.setIsDelete(1);
                    balanceModel.setRemark(balanceModel.getRemark() + " 已退款");
                    rewardScanBalanceDao.update(balanceModel);
                    //删除终端余额
                    shopUserDao.addAmount(shopUser.getId(), balanceModel.getAmount().multiply(BigDecimal.valueOf(-1)));
                }
            }
        }



    }


    /**
     * 判断终端余额是否够扣除奖励
     *
     * @param order
     * @return
     */
    public boolean checkShopAmount(LadderGrouponOrderModel order) {
        Map<String, Object> searchMap = new HashMap<>();
        searchMap.put("shopId", order.getShopId());
        searchMap.put("companyId", order.getCompanyId());
        searchMap.put("sourceOrderCode", order.getOrderCode());
        searchMap.put("earningType", 11);
        List<RewardScanBalanceModel> balanceList = rewardScanBalanceDao.selectList(searchMap);
        if (CollectionUtils.isNotEmpty(balanceList)) {
            RewardScanBalanceModel balanceModel = balanceList.get(0);
            ShopUserModel shopUser = shopUserDao.getPrimaryAccountByShopId(order.getShopId());
            if (balanceModel.getAmount().compareTo(shopUser.getAmount()) > 0) {
                //返利金额大于终端店余额需要用户确认
                return false;
            } else {
                return true;
            }
        } else {
            return true;
        }
    }

    /**
     * 调用退款逻辑
     *
     * @param activityModel
     * @param grouponOrderList
     * @param refundType       1拼团失败 2 退货退款
     */
    public Integer groupOrderBackMoney(LadderGrouponActivityModel activityModel, List<LadderGrouponOrderModel> grouponOrderList, Integer refundType) {
        Integer num = 0;  //退款人数计数
        for (LadderGrouponOrderModel order : grouponOrderList) {
            ResponseEntity<String> responseEntity = null;
            try {
                order.setRefundType(refundType);
                HttpHeaders headers = this.getRequestHeaders(activityModel.getCompanyId());
                HttpEntity<LadderGrouponOrderModel> entity = new HttpEntity<>(order, headers);
                responseEntity = restTemplate.postForEntity(ladderGroupOrderRefundUrl, entity, String.class);
            } catch (Exception e) {
                log.error("请求云众退款异常",e);
                continue;
            }
            log.info("退款结果通知：{}" ,JSON.toJSONString(responseEntity));
            if (responseEntity.getStatusCode().equals(HttpStatus.OK)) {
                String body = responseEntity.getBody();
                JSONObject resp = JSONObject.parseObject(body);
                if ("200".equals(resp.getString("code"))) {
                    log.info("退款成功, 订单号：{}" , order.getOrderCode());
                    num++;
                } else {
                    log.error("退款失败, 订单号：{}",order.getOrderCode());
                    continue;
                }
            } else {
                log.error("请求云众退款异常, 订单号：{}", order.getOrderCode());
                continue;
            }
        }
        return num;
    }

    /**
     * @Description: 获取请求头信息
     */
    private HttpHeaders getRequestHeaders(Integer companyId) {
        HttpHeaders requestHeaders = new HttpHeaders();
        requestHeaders.setContentType(MediaType.APPLICATION_JSON);
        requestHeaders.set("userId", String.valueOf(companyId));
        String base = companyId + "-" + TimeUtilis.getTimeStamp();
        requestHeaders.set("authLocal", Base64.encode(base.getBytes()));
        return requestHeaders;
    }

    /**
     * 确认收货
     * @param id
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public int confirmReceived(Long id) {
        LadderGrouponOrderModel order = ladderGrouponOrderDao.getById(Integer.parseInt(id.toString()));
        if (!AirUtils.hv(order)) {
            throw new BusinessException("该订单不存在");
        }

        if (order.getOrderStatus() != 3) {
            throw new BusinessException("该订单 " + order.getOrderCode() + " 不能收货");
        }
        LadderGrouponOrderModel update = new LadderGrouponOrderModel();
        update.setOrderCode(order.getOrderCode());
        update.setOrderStatus(4);
        update.setReceivedTime(new Date());
        int flag = ladderGrouponOrderDao.updateStatusByOrderCode(update);
        Map<String, Object> searchMap = Maps.newHashMap();
        searchMap.put("orderCode",order.getOrderCode());
        searchMap.put("sortCode","id");
        searchMap.put("sortRole","desc");
        List<LadderGrouponOrderModel> ladderGrouponOrderModels = ladderGrouponOrderDao.selectList(searchMap);
        if (CollectionUtils.isEmpty(ladderGrouponOrderModels)) {
            throw new BusinessException(order.getOrderCode()+"订单信息错误");
        }
        order = ladderGrouponOrderModels.get(0);
        GrouponOrder order2 = new GrouponOrder();
        BeanUtils.copyProperties(order,order2);
        order2.setOrderType(7);//阶梯团在订单中心类型为7

        List<LadderGrouponOrderDetailModel> orderDetailList = ladderGrouponOrderDetailDao.selectList(searchMap);
        if (CollectionUtils.isEmpty(orderDetailList)) {
            throw new BusinessException(order.getOrderCode()+"订单商品信息错误");
        }
        GrouponOrderDetail detail = new GrouponOrderDetail();
        BeanUtils.copyProperties(orderDetailList.get(0), detail);
        // 发送kafka消息
        orderCenterService.sendOrderMessage(order2, detail, "update");

        //分账
        LadderGrouponActivityModel activityModel = ladderGrouponActivityDao.getById(order.getActivityId());
        if (AirUtils.hv(activityModel)) {
            // 重复返利校验
            int num = rewardScanBalanceDao.ishvByOrderCode(order.getOrderCode());
            if (num > 0) {
                throw new BusinessException("请勿重复收货！");
            }
            // 一、自提时开启资金流向
            if (activityModel.getMoneyFlow() == 1 && activityModel.getSendType() == 1) {
                // 团购订单分账
                this.groupOrderSplitAccount(order, orderDetailList);
            }
            // 二、开启团长奖励
            if (activityModel.getIsGrouperRebateStatus() == 1) {
                // 给团长奖励
                this.groupOrderSetAccount(order, orderDetailList,activityModel);
            }
        }
        return flag;
    }

    /**
     * 团购订单分账
     * @param grouponOrder
     * @param orderDetailList
     */
    private void groupOrderSplitAccount(LadderGrouponOrderModel grouponOrder, List<LadderGrouponOrderDetailModel> orderDetailList) {
        // 1、取得进货价、提货卡信息
        ShopModel shopModel = shopDao.getById(grouponOrder.getShopId());
        if (AirUtils.hv(shopModel)) {
            // 查询订单商品数据
            ShopUserModel user = shopUserDao.getPrimaryAccountByShopId(grouponOrder.getShopId());
            for (LadderGrouponOrderDetailModel grouponOrderDetail : orderDetailList) {
                if (grouponOrder.getOrderAmount().compareTo(BigDecimal.ZERO) == 1) {
                    shopUserDao.addAmount(user.getId(), grouponOrder.getOrderAmount());
                } else {
                    continue;
                }
                RewardScanBalanceModel detailModel = new RewardScanBalanceModel();
                detailModel.setShopId(grouponOrder.getShopId());
                detailModel.setEarningType(11);
                detailModel.setTransaction("XR" + System.currentTimeMillis() + (int) ((Math.random() * 9 + 1) * 1000));

                if (grouponOrder.getOrderAmount().compareTo(BigDecimal.ZERO) == 1) {
                    detailModel.setAmount(grouponOrder.getOrderAmount());
                } else {
                    continue;
                }
                detailModel.setCreateTime(new Date());
                detailModel.setStatus(1);
                detailModel.setIsDelete(0);
                detailModel.setRemark(grouponOrder.getOrderCode() + " 销售分润");
                detailModel.setShopUserId(user.getId());
                detailModel.setSourceOrderCode(grouponOrder.getOrderCode());
                detailModel.setUnread(0);
                detailModel.setScore(0);
                detailModel.setCodeCount(grouponOrderDetail.getQty());
                detailModel.setCompanyId(shopModel.getCompanyId());
                rewardScanBalanceService.insert(detailModel);
                Map<String, TemplateData> map = new HashMap<>();
                map.put("character_string1", new TemplateData(grouponOrder.getOrderCode()));
                map.put("amount2", new TemplateData(grouponOrder.getOrderAmount().toString()));
                map.put("amount3", new TemplateData(detailModel.getAmount().toString()));
                map.put("thing4", new TemplateData("到账时间以到账记录为准"));
                messageTemplateService.send("groupOrderRebate", shopModel.getId(), map, shopModel.getCompanyId());
                memberMsgService.insert(MsgTypeEnum.SYDZ, 0,
                        "订单" + grouponOrder.getOrderCode() + "收益到账" + detailModel.getAmount(), shopModel.getId());
            }
        }
    }

    /**
     * 团长收益
     * @param grouponOrder
     * @param activityModel
     */
    private void groupOrderSetAccount(LadderGrouponOrderModel grouponOrder, List<LadderGrouponOrderDetailModel> orderDetailList, LadderGrouponActivityModel activityModel) {
        // 团长收益
        BigDecimal expectMoney = new BigDecimal(0);
        if(activityModel.getGrouperRebateType() == 0){
            //0-实付金额百分比
            expectMoney = grouponOrder.getGroupAmount().multiply(activityModel.getGrouperRebate().multiply(BigDecimal.valueOf(0.01))).setScale(2, BigDecimal.ROUND_HALF_UP);
        }else if(activityModel.getGrouperRebateType() == 1){
            //1-每笔订单X元
            expectMoney = activityModel.getGrouperRebate().setScale(2, BigDecimal.ROUND_HALF_UP);
        }
        // 将团长收益放入门店余额
        ShopModel shopModel = shopDao.getById(grouponOrder.getShopId());
        if (AirUtils.hv(shopModel)) {
            for (LadderGrouponOrderDetailModel grouponOrderDetail : orderDetailList) {
                ShopUserModel user = shopUserDao.getPrimaryAccountByShopId(grouponOrder.getShopId());
                shopUserDao.addAmount(user.getId(), expectMoney);
                RewardScanBalanceModel detailModel = new RewardScanBalanceModel();
                detailModel.setShopId(grouponOrder.getShopId());
                detailModel.setEarningType(11);
                detailModel.setTransaction("XR" + System.currentTimeMillis() + (int) ((Math.random() * 9 + 1) * 1000));
                detailModel.setAmount(expectMoney);
                detailModel.setCreateTime(new Date());
                detailModel.setStatus(1);
                detailModel.setSourceOrderCode(grouponOrder.getOrderCode());
                detailModel.setShopUserId(user.getId());
                detailModel.setIsDelete(0);
                detailModel.setRemark(grouponOrder.getOrderCode() + " 团长奖励");
                detailModel.setUnread(0);
                detailModel.setScore(0);
                detailModel.setCodeCount(grouponOrderDetail.getQty());
                detailModel.setCompanyId(shopModel.getCompanyId());
                rewardScanBalanceService.insert(detailModel);
                Map<String, TemplateData> map = new HashMap<>();
                map.put("character_string1", new TemplateData(grouponOrder.getOrderCode()));
                map.put("amount2", new TemplateData(grouponOrder.getOrderAmount().toString()));
                map.put("amount3", new TemplateData(expectMoney.toString()));
                map.put("thing4", new TemplateData("到账时间以到账记录为准"));
                messageTemplateService.send("groupOrderRebate", grouponOrder.getShopId(), map, shopModel.getCompanyId());
                //发送内部消息
                memberMsgService.insert(MsgTypeEnum.SYDZ, 0, "订单" + grouponOrder.getOrderCode() + "收益到账" + expectMoney, shopModel.getId());
            }
        }
    }

    /**
     * 自动确认收货任务
     */
    public void autoConfirmReceived() {
        if (autoConfirmReceivedFlag.equals("N")) {
            return;
        }

        // 查询未收货且已过7天的单据
        List<LadderGrouponOrderModel> orderList = ladderGrouponOrderDao.findUnReceivedOrders();
        for (LadderGrouponOrderModel order : orderList) {
            log.info("订单号：" + order.getOrderCode());
            // 执行确认收货+返利
            this.confirmReceived(order.getId());
        }
    }

    /**
     * 自动处理订单邮寄状态
     *
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public void handelOrderStatus() {
        Map<String, Object> searchMap = new HashMap<>();
        searchMap.put("orderStatus", new Integer[]{2, 21, 22});
        searchMap.put("receivedType", 2);
        List<LadderGrouponOrderModel> list = ladderGrouponOrderDao.selectOrderListByOrderStatus(searchMap);
        if(CollectionUtils.isNotEmpty(list)){
            list.stream().forEach(d->{
                LadderGrouponOrderModel update = new LadderGrouponOrderModel();
                update.setOrderCode(d.getOrderCode());
                if(22 == d.getOrderStatus()){
                    update.setOrderStatus(23);
                    update.setWarehouseOutTime(new Date());
                } else if (21 == d.getOrderStatus()) {
                    update.setOrderStatus(22);
                    update.setWarehousePackTime(new Date());
                } else if (2 == d.getOrderStatus()) {
                    update.setOrderStatus(21);
                    update.setWarehouseReceiveTime(new Date());
                }else{
                    throw new BusinessException(d.getOrderCode()+"订单状态错误");
                }
                ladderGrouponOrderDao.updateStatusByOrderCode(update);
                searchMap.clear();
                searchMap.put("orderCode",d.getOrderCode());
                searchMap.put("sortCode","id");
                searchMap.put("sortRole","desc");
                List<LadderGrouponOrderModel> ladderGrouponOrderModels = ladderGrouponOrderDao.selectList(searchMap);
                if (CollectionUtils.isEmpty(ladderGrouponOrderModels)) {
                    throw new BusinessException(d.getOrderCode()+"订单信息错误");
                }
                update = ladderGrouponOrderModels.get(0);
                GrouponOrder order2 = new GrouponOrder();
                BeanUtils.copyProperties(update,order2);
                order2.setOrderType(7);//阶梯团在订单中心类型为7

                List<LadderGrouponOrderDetailModel> orderDetailList = ladderGrouponOrderDetailDao.selectList(searchMap);
                if (CollectionUtils.isEmpty(orderDetailList)) {
                    throw new BusinessException(d.getOrderCode()+"订单商品信息错误");
                }
                GrouponOrderDetail detail = new GrouponOrderDetail();
                BeanUtils.copyProperties(orderDetailList.get(0), detail);
                // 发送kafka消息
                orderCenterService.sendOrderMessage(order2, detail, "update");

            });
        }
    }

    /**
     * 小程序订单列表顶部统计数据
     * @param companyId
     * @return
     */
    public TopStatisticsData getTopStatisticsData(Integer shopId, Integer companyId) {
        TopStatisticsData topStatisticsData = new TopStatisticsData();
        topStatisticsData.setHaveCompleteOrderCount(ladderGrouponOrderDao.selectHaveCompleteOrderCount(shopId, companyId));
        topStatisticsData.setWaitAccountOrderCount(ladderGrouponOrderDao.selectWaitAccountOrderCount(shopId, companyId));
        topStatisticsData.setCompleteGrouponCount(ladderGrouponOrderDao.selectCompleteGrouponCount(shopId, companyId));
        topStatisticsData.setJoinGrouponCustomerCount(ladderGrouponOrderDao.selectJoinGrouponCustomerCount(shopId, companyId));
        List<LadderGrouponOrderPageResp> grouponOrderPageResps = ladderGrouponOrderDao.selectHaveCompleteOrderList(shopId, companyId);
        List<LadderGrouponOrderPageResp> waitAccountOrderList = ladderGrouponOrderDao.selectWaitAccountOrderList(shopId, companyId);
        BigDecimal haveCompleteOrderTotalAmount = new BigDecimal("0");
        BigDecimal waitAccountOrderTotalAmount = new BigDecimal("0");
        for (LadderGrouponOrderPageResp grouponOrderPageResp : grouponOrderPageResps) {
            if (grouponOrderPageResp.getOrderAmount() != null && grouponOrderPageResp.getIsGrouperRebateStatus() == 1) {
                BigDecimal haveCompleteOrderAmount = new BigDecimal(0);
                if(grouponOrderPageResp.getGrouperRebateType() == 1){
                    //X元
                    haveCompleteOrderAmount = grouponOrderPageResp.getGrouperRebate();
                }else{
                    //比例
                    haveCompleteOrderAmount = grouponOrderPageResp.getGroupAmount().multiply(grouponOrderPageResp.getGrouperRebate().multiply(BigDecimal.valueOf(0.01))).setScale(2, BigDecimal.ROUND_HALF_UP);
                }
                haveCompleteOrderTotalAmount = haveCompleteOrderTotalAmount.add(haveCompleteOrderAmount);
            }
        }

        for (LadderGrouponOrderPageResp waitOrder : waitAccountOrderList) {
            if (waitOrder.getOrderAmount() != null &&  waitOrder.getIsGrouperRebateStatus() == 1) {
                BigDecimal waitAccountOrderAmount = new BigDecimal(0);
                if(waitOrder.getGrouperRebateType() == 1){
                    //X元
                    waitAccountOrderAmount = waitOrder.getGrouperRebate();
                }else{
                    //比例
                    waitAccountOrderAmount = waitOrder.getGroupAmount().multiply(waitOrder.getGrouperRebate().multiply(BigDecimal.valueOf(0.01))).setScale(2, BigDecimal.ROUND_HALF_UP);
                }
                waitAccountOrderTotalAmount = waitAccountOrderTotalAmount.add(waitAccountOrderAmount);
            }
        }
        topStatisticsData.setHaveCompleteOrderTotalAmount(haveCompleteOrderTotalAmount.setScale(2, BigDecimal.ROUND_HALF_UP).toString());
        topStatisticsData.setWaitAccountOrderTotalAmount(waitAccountOrderTotalAmount.setScale(2, BigDecimal.ROUND_HALF_UP).toString());
        return topStatisticsData;
    }
}