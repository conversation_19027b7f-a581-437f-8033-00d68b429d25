package com.intelliquor.cloud.shop.system.service;

import com.intelliquor.cloud.shop.common.entity.Response;
import com.intelliquor.cloud.shop.system.model.ShopSauceOrderDetailModel;
import com.intelliquor.cloud.shop.system.model.ShopSauceOrderModel;
import com.intelliquor.cloud.shop.system.model.req.ShopSaIntegralOrderResp;

import java.util.List;

/**
 * Created by Administrator on 2020/10/9 0009.
 */
public interface SauceOrderOpenService {

    /**
     * 云店对云众提供同步订单的接口
     * @param orderModel
     * @param detailModel
     * @return
     */
    Response insertOrder(ShopSauceOrderModel orderModel, ShopSauceOrderDetailModel detailModel);

    /**
     * 处理景酱积分订单的推广分润
     * @param list
     * @return
     */
    Response dealBalance(List<ShopSaIntegralOrderResp> list);
}
