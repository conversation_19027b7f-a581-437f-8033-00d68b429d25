package com.intelliquor.cloud.shop.system.dao;

import com.intelliquor.cloud.shop.system.model.ShopRewardModel;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 描述：终端奖励配额信息 Dao接口
 *
 * <AUTHOR>
 * @date 2020-02-25
 */
public interface ShopRewardDao {


    /**
     * 查询数据信息
     *
     * @param searchMap
     * @return
     */
    List<ShopRewardModel> selectList(Map<String, Object> searchMap);

    /**
     * 新增
     *
     * @param model
     * @return
     */
    Integer insert(ShopRewardModel model);

    /**
     * 更新
     *
     * @param model
     * @return
     */
    Integer update(ShopRewardModel model);

    /**
     * 库存递减
     *
     * @return
     */
    Integer decrRewardNum(@Param("shopId") Integer shopId, @Param("rewardType") String rewardType, @Param("num") Integer num);

    /**
     * 配额递减
     * @param id
     * @param num
     * @return
     */
    Integer reduceRewardNum(@Param("id") Integer id, @Param("num") Integer num);

    /**
     * 删除
     *
     * @param id
     * @return
     */
    Integer delete(Integer id);

    /**
     * 根据ID查询
     *
     * @param id
     * @return
     */
    ShopRewardModel getById(Integer id);

    /**
     * 更新数量
     * @param id
     * @param rewardNum
     * @return
     */
    Integer updateRewardNum(Integer id,Integer rewardNum);
}