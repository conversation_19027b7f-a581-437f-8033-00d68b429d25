package com.intelliquor.cloud.shop.system.model;

import lombok.Data;

import java.util.Date;

@Data
public class StorageScanCode {
    /**
     *   主键id
     * @mbg.generated
     */
    private Integer id;

    /**
     *   扫描盒码
     * @mbg.generated
     */
    private String code;

    /**
     *   终端id
     * @mbg.generated
     */
    private Long shopId;

    /**
     *   终端名称
     * @mbg.generated
     */
    private String shopName;

    /**
     *   盒码关联优惠券码数
     * @mbg.generated
     */
    private Integer quantity;

    /**
     *   状态：0 未启用（只扫码） 1 已启用（扫完点击完成）
     * @mbg.generated
     */
    private Integer status;

    /**
     *   商户id
     * @mbg.generated
     */
    private Integer companyId;

    /**
     *   创建时间
     * @mbg.generated
     */
    private Date createTime;

    /**
     *   更新时间
     * @mbg.generated
     */
    private Date updateTime;
}