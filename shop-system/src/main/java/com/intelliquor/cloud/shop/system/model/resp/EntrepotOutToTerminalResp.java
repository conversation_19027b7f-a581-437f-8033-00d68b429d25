package com.intelliquor.cloud.shop.system.model.resp;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.intelliquor.cloud.shop.common.annotation.Excel;
import com.intelliquor.cloud.shop.common.annotation.ExcelTitle;
import com.intelliquor.cloud.shop.common.annotation.LevelExcelTitle;
import com.intelliquor.cloud.shop.common.annotation.Sheet;
import com.intelliquor.cloud.shop.system.model.OrderDownloadCenterModel;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

@Excel(fileName = "出入库记录.xls")
@Sheet(sheetNames = {"出入库记录列表"}, numPerSheet = 50000, numOfSheet = {}, groupNumber = 1)
@LevelExcelTitle(titleNames = {"出入库记录列表"}, titleLevel =1, colSpans = {8}, groupNumber = 1)
@Data
public class EntrepotOutToTerminalResp {

    /**
     * 主键id
     */
    private Integer id;

    /**
     * 库管人员id
     */
    private Integer entrepotManagerId;

    /**
     * 企业id
     */
    private Integer companyId;

    /**
     * 码
     */
    @ExcelTitle(titleName = "码", index = 1, groupNumber = 1, width = 5000)
    private String code;

    /**
     * 码类型
     */
    private String codeType;

    /**
     * 终端店id
     */
    @ExcelTitle(titleName = "终端编码", index = 7, groupNumber = 1, width = 5000)
    private String terminalId;

    /**
     * 终端店名称
     */
    @ExcelTitle(titleName = "入库终端", index = 6, groupNumber = 1, width = 5000)
    private String terminalName;

    /**
     * 创建时间
     */
    @ExcelTitle(titleName = "入库时间", index = 8, groupNumber = 1, width = 5000)
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 产品编码
     */
    @ExcelTitle(titleName = "产品编码", index = 3, groupNumber = 1, width = 5000)
    private String productCode;

    /**
     * 产品名称
     */
    @ExcelTitle(titleName = "产品名称", index = 4, groupNumber = 1, width = 5000)
    private String  productName;

    /**
     * 出库给上游经销商时间
     */
    @ExcelTitle(titleName = "上游出库时间", index = 5, groupNumber = 1, width = 5000)
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date outDealerTime;

    /**
     * 开始时间
     */
    private String startTime;

    /**
     * 结束时间
     */
    private String endTime;


    /**
     * 码类型
     */
    @ExcelTitle(titleName = "码类型", index = 2, groupNumber = 1, width = 5000)
    private String codeTypeStr;

    /**
     * 下载中心实体
     */
    private OrderDownloadCenterModel downloadCenterModel;
}