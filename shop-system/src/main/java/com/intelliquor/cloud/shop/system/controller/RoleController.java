package com.intelliquor.cloud.shop.system.controller;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.intelliquor.cloud.shop.common.entity.PageResponse;
import com.intelliquor.cloud.shop.common.entity.Response;
import com.intelliquor.cloud.shop.common.utils.SearchUtil;
import com.intelliquor.cloud.shop.system.model.RoleModel;
import com.intelliquor.cloud.shop.system.service.RoleService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


/**
 * 描述：角色管理控制层
 *
 * <AUTHOR>
 * @date 2019-06-18
 */
@Api(tags = {"角色管理操作接口"}, description = "角色管理操作接口")
@RestController
@RequestMapping("/system/role")
public class RoleController {

    @Autowired
    private RoleService roleService;


    @ApiOperation(value = "查询分页信息", notes = "查询分页信息", httpMethod = "GET")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "页码", required = true, paramType = "query", dataType = "int"),
            @ApiImplicitParam(name = "limit", value = "每页条数", required = true, paramType = "query", dataType = "int")
    })
    @RequestMapping(value = "/getListByPage")
    public PageResponse<List<RoleModel>> getListByPage(RoleModel model) {

        PageHelper.startPage(model.getPage(), model.getLimit());
        List<RoleModel> list = roleService.selectList(SearchUtil.getSearch(model));
        PageInfo<RoleModel> pageInfo = new PageInfo<>(list);
        return PageResponse.ok(pageInfo);
    }

    @ApiOperation(value = "查询信息列表", notes = "查询信息列表", httpMethod = "GET")
    @RequestMapping(value = "/getList")
    public Response<List<RoleModel>> getList(@RequestBody RoleModel model) {
        List<RoleModel> list = roleService.selectList(SearchUtil.getSearch(model));
        return Response.ok(list);
    }

    @ApiOperation(value = "保存信息", notes = "保存信息", httpMethod = "POST")
    @RequestMapping(value = "/save")
    public Response<String> save(@RequestBody RoleModel model) {
        return roleService.insert(model);
    }

    @ApiOperation(value = "更新信息", notes = "更新信息", httpMethod = "POST")
    @RequestMapping(value = "/update")
    public Response<String> update(@RequestBody RoleModel model) {
        return roleService.update(model);
    }

    @ApiOperation(value = "更新状态信息", notes = "更新状态信息", httpMethod = "POST")
    @RequestMapping(value = "/updateStatus")
    public Response<String> updateStatus(@RequestParam(value = "id") Integer id, @RequestParam(value = "status") String status) {
        roleService.updateStatus(id, status);
        return Response.ok("更新成功");
    }

    @ApiOperation(value = "删除信息", notes = "删除信息", httpMethod = "GET")
    @RequestMapping(value = "/delete")
    public Response<String> delete(@RequestParam(value = "id") Integer id) {
        roleService.delete(id);
        return Response.ok("删除成功");
    }

    @ApiOperation(value = "根据ID查询信息", notes = "根据ID查询信息", httpMethod = "GET")

    @RequestMapping(value = "/getById")
    public Response<RoleModel> getById(@RequestParam(value = "id") Integer id) {
        RoleModel model = roleService.getById(id);
        return Response.ok(model);
    }

    @ApiOperation(value = "根据ID查询权限信息", notes = "根据ID查询权限信息", httpMethod = "GET")
    @RequestMapping(value = "/getPerm")
    public Response<List<Integer>> getPerm(@RequestParam(value = "id") Integer id) {
        List<Integer> perms = roleService.getPermsByRoleId(id);
        return Response.ok(perms);
    }

    @ApiOperation(value = "保存角色权限信息", notes = "保存角色权限信息", httpMethod = "GET")
    @RequestMapping(value = "/savePerm")
    public Response<String> savePerm(@RequestBody RoleModel model) {
        if (null == model.getId() || null == model.getPermIds()) {
            return Response.fail("参数缺失");
        }
        roleService.savePerm(model.getId(), model.getPermIds());
        return Response.ok("保存成功");
    }

    @ApiOperation(value = "查询角色列表", notes = "查询角色列表", httpMethod = "GET")
    @RequestMapping(value = "/getRoleList")
    public Response<List<RoleModel>> getRoleList() {
        RoleModel model = new RoleModel();
        model.setStatus("0");
        List<RoleModel> list = roleService.selectList(SearchUtil.getSearch(model));
        return Response.ok(list);
    }
}