package com.intelliquor.cloud.shop.system.model;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * @Auther: tianms
 * @Date: 2021/04/26 15:24
 * @Description: 云店扫码出库明细表
 */
@Data
public class ShopScanRemoveInventoryDetailModel {

    /**
     * 主键
     */
    private Integer id;

    /**
     * 商户id
     */
    private Integer companyId;

    /**
     * 批次id
     */
    private Integer balanceId;

    /**
     * 瓶数
     */
    private Integer num;

    /**
     * 码信息
     */
    private String code;

    /**
     * 有效码
     */
    private String codeIn;

    /**
     * 码类型,1:箱码，2:盒码
     */
    private Integer codeType;

    /**
     * 商品编号
     */
    private String goodsCode;

    /**
     * 商品名称
     */
    private String goodsName;

    /**
     * 客户编码
     */
    private String customerNo;

    /**
     * 供应商名称
     */
    private String customerName;

    /**
     * 批次信息
     */
    private String orderNo;

    /**
     * 创建时间
     */
    @JSONField(format= "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 出库时间
     */
    @JSONField(format= "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date outTime;

    /**
     * 扫码是否完成，true:完成，false：未完成
     */
    private Boolean scanFinish;

}
