<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.intelliquor.cloud.shop.system.dao.ScanGoodsRewardSetDao">

    <resultMap id="BaseResultMap" type="com.intelliquor.cloud.shop.system.model.ScanGoodsRewardSetModel" >
        <result column="id" property="id" />
        <result column="goods_code" property="goodsCode" />
        <result column="goods_name" property="goodsName" />
        <result column="company_id" property="companyId" />
        <result column="reward_condition" property="rewardCondition" />
        <result column="reward_shop_condition" property="rewardShopCondition" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <sql id="Base_Column_List">
        id,
        goods_code,
        goods_name,
        company_id,
        reward_condition,
        reward_shop_condition,
        create_time,
        update_time
    </sql>

    <insert id="insert" useGeneratedKeys="true" keyColumn="id" keyProperty="id" parameterType="com.intelliquor.cloud.shop.system.model.ScanGoodsRewardSetModel">
        INSERT INTO t_scan_goods_reward_set
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test ='null != goodsCode'>
                goods_code,
            </if>
            <if test ='null != goodsName'>
                goods_name,
            </if>
            <if test ='null != companyId'>
                company_id,
            </if>
            <if test ='null != rewardCondition'>
                reward_condition,
            </if>
            <if test ='null != rewardShopCondition'>
                reward_shop_condition,
            </if>
            <if test ='null != createTime'>
                create_time,
            </if>
            <if test ='null != updateTime'>
                update_time
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test ='null != goodsCode'>
                #{goodsCode},
            </if>
            <if test ='null != goodsName'>
                #{goodsName},
            </if>
            <if test ='null != companyId'>
                #{companyId},
            </if>
            <if test ='null != rewardCondition'>
                #{rewardCondition},
            </if>
            <if test ='null != rewardShopCondition'>
                #{rewardShopCondition},
            </if>
            <if test ='null != createTime'>
                #{createTime},
            </if>
            <if test ='null != updateTime'>
                #{updateTime}
            </if>
        </trim>
    </insert>

    <delete id="delete" >
        DELETE FROM t_scan_goods_reward_set
        WHERE id = #{id}
    </delete>

    <update id="update" parameterType="com.intelliquor.cloud.shop.system.model.ScanGoodsRewardSetModel">
        UPDATE t_scan_goods_reward_set
        <set>
            <if test ='null != goodsCode'>goods_code = #{goodsCode},</if>
            <if test ='null != goodsName'>goods_name = #{goodsName},</if>
            <if test ='null != companyId'>company_id = #{companyId},</if>
            <if test ='null != rewardCondition'>reward_condition = #{rewardCondition},</if>
            <if test ='null != rewardShopCondition'>reward_shop_condition = #{rewardShopCondition},</if>
            <if test ='null != createTime'>create_time = #{createTime},</if>
            <if test ='null != updateTime'>update_time = #{updateTime}</if>
        </set>
        WHERE id = #{id}
    </update>

    <update id="updNoConditStock" >
        UPDATE t_scan_goods_reward_set
        SET reward_condition = JSON_REPLACE(reward_condition, "$[0].rewardStock", #{num})
        WHERE goods_code = #{goodsCode} and company_id = #{companyId}
    </update>

    <select id="load" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM t_scan_goods_reward_set
        WHERE id = #{id}
    </select>

    <select id="findByGoodsCode" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM t_scan_goods_reward_set
        WHERE 1=1
            <if test="null != goodsCode">
               and goods_code = #{goodsCode}
            </if>
            <if test="null != companyId">
               and company_id = #{companyId}
            </if>
    </select>

</mapper>
