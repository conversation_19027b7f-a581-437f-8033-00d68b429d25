<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.intelliquor.cloud.shop.system.dao.DealerAreaDao">

    <sql id="baseColumn">
        id,
        dealer_id,
        dealer_code,
        province,
        city,
        district,
        street
    </sql>

    <resultMap id="baseResultMap" type="com.intelliquor.cloud.shop.system.model.DealerAreaModel">
        <id property="id" column="id"/>
        <result property="dealerId" column="dealer_id"/>
        <result property="dealerCode" column="dealer_code"/>
        <result property="province" column="province"/>
        <result property="city" column="city"/>
        <result property="district" column="district"/>
        <result property="street" column="street"/>
    </resultMap>

    <sql id="selectiveWhere">
        <where>

        </where>
    </sql>


    <select id="selectList" resultMap="baseResultMap">
        SELECT
        <include refid="baseColumn"/>
        FROM t_dealer_area
        <include refid="selectiveWhere"/>
        ORDER BY ${sortCode} ${sortRole}
    </select>

    <insert id="insert" parameterType="com.intelliquor.cloud.shop.system.model.DealerAreaModel">
        INSERT INTO t_dealer_area
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="dealerId != null ">
                dealer_id,
            </if>
            <if test="dealerCode != null ">
                dealer_code,
            </if>
            <if test="province != null ">
                province,
            </if>
            <if test="city != null ">
                city,
            </if>
            <if test="district != null ">
                district,
            </if>
            <if test="street != null ">
                street
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="dealerId != null ">
                #{dealerId},
            </if>
            <if test="dealerCode != null ">
                #{dealerCode},
            </if>
            <if test="province != null ">
                #{province},
            </if>
            <if test="city != null ">
                #{city},
            </if>
            <if test="district != null ">
                #{district},
            </if>
            <if test="street != null ">
                #{street}
            </if>
        </trim>
    </insert>

    <update id="update" parameterType="com.intelliquor.cloud.shop.system.model.DealerAreaModel">
        UPDATE t_dealer_area
        <set>
            <if test="dealerId != null ">
                dealer_id = #{dealerId},
            </if>
            <if test="province != null ">
                province = #{province},
            </if>
            <if test="city != null ">
                city = #{city},
            </if>
            <if test="district != null ">
                district = #{district},
            </if>
            <if test="street != null ">
                street = #{street},
            </if>
        </set>
        WHERE id = #{id}
    </update>

    <delete id="delete" parameterType="integer">
        DELETE FROM t_dealer_area
        WHERE id = #{id}
    </delete>

    <select id="getById" resultMap="baseResultMap">
        SELECT
        <include refid="baseColumn"/>
        FROM t_dealer_area
        WHERE id = #{id}
    </select>

    <select id="getByDealerCode" resultMap="baseResultMap">
        SELECT
        <include refid="baseColumn"/>
        FROM t_dealer_area
        WHERE dealer_code = #{dealerCode}
    </select>

    <delete id="deleteByDealerId" parameterType="integer">
        DELETE FROM t_dealer_area
        WHERE dealer_id = #{dealerId}
    </delete>

    <delete id="deleteByDealerCode" >
        DELETE FROM t_dealer_area
        WHERE dealer_code = #{dealerCode}
    </delete>
</mapper>