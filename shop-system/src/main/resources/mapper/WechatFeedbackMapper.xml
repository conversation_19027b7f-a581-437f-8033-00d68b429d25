<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.intelliquor.cloud.shop.system.dao.WechatFeedbackDao">

    <sql id="baseColumn">
        id,
        problem_type,
        problem_desc,
        problem_screenshot_pic,
        feedback_terminal,
        phone,
        linkman,
        schedule,
        is_delete,
        create_time,
        update_time,
        company_id,
        handling_result,
        handling_time,
        handling_pic
    </sql>

    <resultMap id="baseResultMap" type="com.intelliquor.cloud.shop.system.model.WechatFeedbackModel">
        <id property="id" column="id"/>
        <result property="problemType" column="problem_type"/>
        <result property="problemDesc" column="problem_desc"/>
        <result property="problemScreenshotPic" column="problem_screenshot_pic"/>
        <result property="feedbackTerminal" column="feedback_terminal"/>
        <result property="feedbackTerminalName" column="feedback_terminal_name"/>
        <result property="terminalLinkphone" column="terminal_linkphone"/>
        <result property="terminalLinkman" column="terminal_linkman"/>
        <result property="phone" column="phone"/>
        <result property="linkman" column="linkman"/>
        <result property="schedule" column="schedule"/>
        <result property="isDelete" column="is_delete"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="companyId" column="company_id"/>
        <result property="handlingResult" column="handling_result"/>
        <result property="handlingTime" column="handling_time"/>
        <result property="handlingPic" column="handling_pic"/>
    </resultMap>

    <sql id="selectiveWhere">
        <where>
            f.is_delete = 0
            <if test="companyId != null ">
                and f.company_id = #{companyId}
            </if>
            <if test="feedbackTerminal != null ">
                and f.feedback_terminal = #{feedbackTerminal}
            </if>
            <if test="feedbackTerminalName != null and feedbackTerminalName != ''">
                and s.name LIKE CONCAT('%', #{feedbackTerminalName}, '%')
            </if>
            <if test="linkman != null and linkman != ''">
                and f.linkman LIKE CONCAT('%', #{linkman}, '%')
            </if>
            <if test="phone != null and phone != ''">
                and f.phone LIKE CONCAT('%', #{phone}, '%')
            </if>
            <if test="schedule != null ">
                and f.schedule = #{schedule}
            </if>
            <if test="beginTime != null and beginTime != ''">
                AND f.create_time &gt;= #{beginTime}
            </if>
            <if test="endTime != null and endTime != ''">
                AND f.create_time &lt;= #{endTime}
            </if>
            <if test="handlingBeginTime != null and handlingBeginTime != ''">
                AND f.handling_time &gt;= #{handlingBeginTime}
            </if>
            <if test="handlingEndTime != null and handlingEndTime != ''">
                AND f.handling_time &lt;= #{handlingEndTime}
            </if>
        </where>
    </sql>


    <select id="selectList" resultMap="baseResultMap">
        SELECT
        f.id,
        f.problem_type,
        f.problem_desc,
        f.problem_screenshot_pic,
        f.feedback_terminal,
        s.name as feedback_terminal_name,
        s.linkphone as terminal_linkphone,
        s.linkman as terminal_linkman,
        f.phone,
        f.linkman,
        f.schedule,
        f.is_delete,
        f.create_time,
        f.update_time,
        f.company_id,
        f.handling_result,
        f.handling_time,
        f.handling_pic
        FROM t_wechat_feedback f
        left join t_member_shop s on s.id = f.feedback_terminal
        <include refid="selectiveWhere"/>
        ORDER BY ${sortCode} ${sortRole}
    </select>

    <insert id="insert" parameterType="com.intelliquor.cloud.shop.system.model.WechatFeedbackModel">
        INSERT INTO t_wechat_feedback
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="problemType != null ">
                problem_type,
            </if>
            <if test="problemDesc != null ">
                problem_desc,
            </if>
            <if test="problemScreenshotPic != null ">
                problem_screenshot_pic,
            </if>
            <if test="feedbackTerminal != null ">
                feedback_terminal,
            </if>
            <if test="phone != null ">
                phone,
            </if>
            <if test="linkman != null ">
                linkman,
            </if>
            <if test="schedule != null ">
                schedule,
            </if>
            <if test="companyId != null ">
                company_id,
            </if>
            is_delete,
            create_time
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="problemType != null ">
                #{problemType},
            </if>
            <if test="problemDesc != null ">
                #{problemDesc},
            </if>
            <if test="problemScreenshotPic != null ">
                #{problemScreenshotPic},
            </if>
            <if test="feedbackTerminal != null ">
                #{feedbackTerminal},
            </if>
            <if test="phone != null ">
                #{phone},
            </if>
            <if test="linkman != null ">
                #{linkman},
            </if>
            <if test="schedule != null ">
                #{schedule},
            </if>
            <if test="companyId != null ">
                #{companyId},
            </if>
            0,
            now()
        </trim>
    </insert>

    <update id="update" parameterType="com.intelliquor.cloud.shop.system.model.WechatFeedbackModel">
        UPDATE t_wechat_feedback
        <set>
            <if test="problemType != null ">
                problem_type = #{problemType},
            </if>
            <if test="problemDesc != null ">
                problem_desc = #{problemDesc},
            </if>
            <if test="problemScreenshotPic != null ">
                problem_screenshot_pic = #{problemScreenshotPic},
            </if>
            <if test="feedbackTerminal != null ">
                feedback_terminal = #{feedbackTerminal},
            </if>
            <if test="phone != null ">
                phone = #{phone},
            </if>
            <if test="linkman != null ">
                linkman = #{linkman},
            </if>
            <if test="schedule != null ">
                schedule = #{schedule},
            </if>
            <if test="companyId != null ">
                company_id = #{companyId},
            </if>
            <if test="handlingResult != null ">
                handling_result = #{handlingResult},
            </if>
            <if test="handlingTime != null ">
                handling_time = #{handlingTime},
            </if>
            <if test="handlingPic != null ">
                handling_pic = #{handlingPic},
            </if>
            update_time = now()
        </set>
        WHERE id = #{id}
    </update>

    <update id="replyQuestion" parameterType="com.intelliquor.cloud.shop.system.model.WechatFeedbackModel">
        UPDATE t_wechat_feedback
        <set>
            schedule = 2,
            update_time = now(),
            <if test="companyId != null ">
                company_id = #{companyId},
            </if>
            <if test="handlingResult != null ">
                handling_result = #{handlingResult},
            </if>
            <if test="handlingPic != null ">
                handling_pic = #{handlingPic},
            </if>
            handling_time = now()
        </set>
        WHERE id = #{id}
    </update>

    <delete id="delete" parameterType="integer">
        DELETE FROM t_wechat_feedback
        WHERE id = #{id}
    </delete>

    <update id="logicDelete" parameterType="integer">
    UPDATE t_wechat_feedback
    set is_delete = 1
    WHERE id = #{id}
    </update>

    <select id="getById" resultMap="baseResultMap">
        SELECT
        f.id,
        f.problem_type,
        f.problem_desc,
        f.problem_screenshot_pic,
        f.feedback_terminal,
        s.name as feedback_terminal_name,
        f.phone,
        f.linkman,
        f.schedule,
        f.is_delete,
        f.create_time,
        f.update_time,
        f.company_id,
        f.handling_result,
        f.handling_time,
        f.handling_pic
        FROM t_wechat_feedback f
        left join t_member_shop s on s.id = f.feedback_terminal
        WHERE f.id = #{id}
    </select>

</mapper>