<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.intelliquor.cloud.shop.system.dao.SalesmanDao">

    <sql id="baseColumn">
        id,
        org_code,
        org_name,
        name,
        role_type,
        role_str,
        phone,
        identity_id,
        nickname,
        open_id,
        status,
        created_at
    </sql>

    <resultMap id="baseResultMap" type="com.intelliquor.cloud.shop.system.model.SalesmanModel">
            <id property="id" column="id"/>
            <result property="orgCode" column="org_code"/>
            <result property="orgName" column="org_name"/>
            <result property="name" column="name"/>
            <result property="roleType" column="role_type"/>
            <result property="roleStr" column="role_str"/>
            <result property="phone" column="phone"/>
            <result property="identityId" column="identity_id"/>
            <result property="nickname" column="nickname"/>
            <result property="openId" column="open_id"/>
            <result property="status" column="status"/>
            <result property="createdAt" column="created_at"/>
    </resultMap>

    <sql id="selectiveWhere">
        <where>

        </where>
    </sql>


    <select id="selectList" resultMap="baseResultMap">
        SELECT
        <include refid="baseColumn"/>
        FROM salesman
        <include refid="selectiveWhere"/>
        ORDER BY ${sortCode} ${sortRole}
    </select>

    <insert id="insert" parameterType="com.intelliquor.cloud.shop.system.model.SalesmanModel">
        INSERT INTO salesman
    <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="orgCode != null ">
            org_code,
            </if>
            <if test="orgName != null ">
            org_name,
            </if>
            <if test="name != null ">
            name,
            </if>
            <if test="roleType != null ">
            role_type,
            </if>
            <if test="roleStr != null ">
            role_str,
            </if>
            <if test="phone != null ">
            phone,
            </if>
            <if test="identityId != null ">
            identity_id,
            </if>
            <if test="nickname != null ">
            nickname,
            </if>
            <if test="openId != null ">
            open_id,
            </if>
            <if test="status != null ">
            status,
            </if>
            <if test="createdAt != null ">
            created_at
            </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="orgCode != null ">
            #{orgCode},
            </if>
            <if test="orgName != null ">
            #{orgName},
            </if>
            <if test="name != null ">
            #{name},
            </if>
            <if test="roleType != null ">
            #{roleType},
            </if>
            <if test="roleStr != null ">
            #{roleStr},
            </if>
            <if test="phone != null ">
            #{phone},
            </if>
            <if test="identityId != null ">
            #{identityId},
            </if>
            <if test="nickname != null ">
            #{nickname},
            </if>
            <if test="openId != null ">
            #{openId},
            </if>
            <if test="status != null ">
            #{status},
            </if>
            <if test="createdAt != null ">
            #{createdAt}
            </if>
    </trim>
    </insert>

    <update id="update" parameterType="com.intelliquor.cloud.shop.system.model.SalesmanModel">
        UPDATE salesman
        <set>
            <if test="orgCode != null ">
            org_code = #{orgCode},
            </if>
            <if test="orgName != null ">
            org_name = #{orgName},
            </if>
            <if test="name != null ">
            name = #{name},
            </if>
            <if test="roleType != null ">
            role_type = #{roleType},
            </if>
            <if test="roleStr != null ">
            role_str = #{roleStr},
            </if>
            <if test="phone != null ">
            phone = #{phone},
            </if>
            <if test="identityId != null ">
            identity_id = #{identityId},
            </if>
            <if test="nickname != null ">
            nickname = #{nickname},
            </if>
            <if test="openId != null ">
            open_id = #{openId},
            </if>
            <if test="status != null ">
            status = #{status},
            </if>
            <if test="createdAt != null ">
            created_at = #{createdAt},
            </if>
        </set>
        WHERE id = #{id}
    </update>

    <delete id="delete" parameterType="integer">
        DELETE FROM salesman
        WHERE id = #{id}
    </delete>

    <select id="getById" resultMap="baseResultMap">
        SELECT
        <include refid="baseColumn"/>
        FROM salesman
        WHERE id = #{id}
    </select>

</mapper>