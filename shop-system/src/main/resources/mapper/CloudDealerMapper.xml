<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.intelliquor.cloud.shop.system.dao.CloudDealerDao">

    <sql id="selectiveWhere">
        <where>
            is_delete = 1
            <if test="companyId != null ">
                and company_id = #{companyId}
            </if>
            <if test="dealerCodeEq != null and dealerCodeEq != ''">
                and dealer_code  = #{dealerCodeEq}
            </if>
        </where>
    </sql>


    <select id="selectList" resultType="com.intelliquor.cloud.shop.system.model.CloudDealerInfoModel">
        SELECT
        *
        FROM t_cloud_dealer_info
        <include refid="selectiveWhere"/>
        ORDER BY id desc
    </select>

</mapper>