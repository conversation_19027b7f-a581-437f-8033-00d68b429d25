<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.intelliquor.cloud.shop.system.dao.RewardCycleTransactionDao">

    <sql id="baseColumn">
        id,
        cycle_balance_id,
        qrcode,
        total,
        quantity,
        unit,
        reward_type,
        activity_id,
        create_time,
        update_time,
        code_in,
        goods_code,
        goods_name,
        company_id,
        balance_amount,
        transation_amount,
        remark
    </sql>

    <resultMap id="baseResultMap" type="com.intelliquor.cloud.shop.system.model.RewardCycleTransactionModel">
            <id property="id" column="id"/>
            <result property="cycleBalanceId" column="cycle_balance_id"/>
            <result property="qrcode" column="qrcode"/>
            <result property="total" column="total"/>
            <result property="quantity" column="quantity"/>
            <result property="unit" column="unit"/>
            <result property="rewardType" column="reward_type"/>
            <result property="activityId" column="activity_id"/>
            <result property="createTime" column="create_time"/>
            <result property="updateTime" column="update_time"/>
            <result property="codeIn" column="code_in"/>
            <result property="goodsCode" column="goods_code"/>
            <result property="goodsName" column="goods_name"/>
            <result property="companyId" column="company_id"/>
            <result property="balanceAmount" column="balance_amount"/>
            <result property="transationAmount" column="transation_amount"/>
            <result property="remark" column="remark"/>
    </resultMap>

    <sql id="selectiveWhere">
        <where>

        </where>
    </sql>


    <select id="selectList" resultMap="baseResultMap">
        SELECT
        <include refid="baseColumn"/>
        FROM t_reward_cycle_transaction
        <include refid="selectiveWhere"/>
        ORDER BY ${sortCode} ${sortRole}
    </select>

    <insert id="insert" parameterType="com.intelliquor.cloud.shop.system.model.RewardCycleTransactionModel">
        INSERT INTO t_reward_cycle_transaction
    <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="cycleBalanceId != null ">
            cycle_balance_id,
            </if>
            <if test="qrcode != null ">
            qrcode,
            </if>
            <if test="total != null ">
            total,
            </if>
            <if test="quantity != null ">
            quantity,
            </if>
            <if test="unit != null ">
            unit,
            </if>
            <if test="rewardType != null ">
            reward_type,
            </if>
            <if test="activityId != null ">
            activity_id,
            </if>
            <if test="createTime != null ">
            create_time,
            </if>
            <if test="updateTime != null ">
            update_time,
            </if>
            <if test="codeIn != null ">
            code_in,
            </if>
            <if test="goodsCode != null ">
            goods_code,
            </if>
            <if test="goodsName != null ">
            goods_name,
            </if>
            <if test="companyId != null ">
            company_id,
            </if>
            <if test="balanceAmount != null ">
            balance_amount,
            </if>
            <if test="transationAmount != null ">
            transation_amount,
            </if>
            <if test="remark != null ">
            remark
            </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="cycleBalanceId != null ">
            #{cycleBalanceId},
            </if>
            <if test="qrcode != null ">
            #{qrcode},
            </if>
            <if test="total != null ">
            #{total},
            </if>
            <if test="quantity != null ">
            #{quantity},
            </if>
            <if test="unit != null ">
            #{unit},
            </if>
            <if test="rewardType != null ">
            #{rewardType},
            </if>
            <if test="activityId != null ">
            #{activityId},
            </if>
            <if test="createTime != null ">
            #{createTime},
            </if>
            <if test="updateTime != null ">
            #{updateTime},
            </if>
            <if test="codeIn != null ">
            #{codeIn},
            </if>
            <if test="goodsCode != null ">
            #{goodsCode},
            </if>
            <if test="goodsName != null ">
            #{goodsName},
            </if>
            <if test="companyId != null ">
            #{companyId},
            </if>
            <if test="balanceAmount != null ">
            #{balanceAmount},
            </if>
            <if test="transationAmount != null ">
            #{transationAmount},
            </if>
            <if test="remark != null ">
            #{remark}
            </if>
    </trim>
    </insert>

    <update id="update" parameterType="com.intelliquor.cloud.shop.system.model.RewardCycleTransactionModel">
        UPDATE t_reward_cycle_transaction
        <set>
            <if test="cycleBalanceId != null ">
            cycle_balance_id = #{cycleBalanceId},
            </if>
            <if test="qrcode != null ">
            qrcode = #{qrcode},
            </if>
            <if test="total != null ">
            total = #{total},
            </if>
            <if test="quantity != null ">
            quantity = #{quantity},
            </if>
            <if test="unit != null ">
            unit = #{unit},
            </if>
            <if test="rewardType != null ">
            reward_type = #{rewardType},
            </if>
            <if test="activityId != null ">
            activity_id = #{activityId},
            </if>
            <if test="createTime != null ">
            create_time = #{createTime},
            </if>
            <if test="updateTime != null ">
            update_time = #{updateTime},
            </if>
            <if test="codeIn != null ">
            code_in = #{codeIn},
            </if>
            <if test="goodsCode != null ">
            goods_code = #{goodsCode},
            </if>
            <if test="goodsName != null ">
            goods_name = #{goodsName},
            </if>
            <if test="companyId != null ">
            company_id = #{companyId},
            </if>
            <if test="balanceAmount != null ">
            balance_amount = #{balanceAmount},
            </if>
            <if test="transationAmount != null ">
            transation_amount = #{transationAmount},
            </if>
            <if test="remark != null ">
            remark = #{remark},
            </if>
        </set>
        WHERE id = #{id}
    </update>

    <delete id="delete" parameterType="integer">
        DELETE FROM t_reward_cycle_transaction
        WHERE id = #{id}
    </delete>

    <select id="getById" resultMap="baseResultMap">
        SELECT
        <include refid="baseColumn"/>
        FROM t_reward_cycle_transaction
        WHERE id = #{id}
    </select>

</mapper>