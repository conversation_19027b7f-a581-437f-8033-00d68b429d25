<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.intelliquor.cloud.shop.system.dao.RewardGuideRangeDao">

    <sql id="baseColumn">
        id,
        guide_id,
        type,
        province,
        city,
        district,
        shop_id,
        create_time
    </sql>

    <sql id="unionColumn">
        r.id,
        r.guide_id,
        r.type,
        r.province,
        r.city,
        r.district,
        r.shop_id,
        r.create_time,
        s.name as shop_name
    </sql>

    <resultMap id="baseResultMap" type="com.intelliquor.cloud.shop.system.model.RewardGuideRangeModel">
        <id property="id" column="id"/>
        <result property="guideId" column="guide_id"/>
        <result property="type" column="type"/>
        <result property="province" column="province"/>
        <result property="city" column="city"/>
        <result property="district" column="district"/>
        <result property="shopId" column="shop_id"/>
        <result property="createTime" column="create_time"/>
        <result property="shopName" column="shop_name"/>

    </resultMap>

    <sql id="selectiveWhere">
        <where>

        </where>
    </sql>


    <select id="selectList" resultMap="baseResultMap">
        SELECT
        <include refid="baseColumn"/>
        FROM t_reward_guide_range
        <include refid="selectiveWhere"/>
        ORDER BY ${sortCode} ${sortRole}
    </select>

    <insert id="insert" parameterType="com.intelliquor.cloud.shop.system.model.RewardGuideRangeModel">
        INSERT INTO t_reward_guide_range(
            guide_id,
            type,
            province,
            city,
            district,
            shop_id,
            create_time
        )VALUES(
            #{guideId},
            #{type},
            #{province},
            #{city},
            #{district},
            #{shopId},
            #{createTime}
        )
    </insert>

    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO t_reward_guide_range(
        guide_id,
        type,
        province,
        city,
        district,
        shop_id
        )values
        <foreach collection="list" item="item" index="index" separator=",">
            ( #{item.guideId},
            #{item.type},
            #{item.province},
            #{item.city},
            #{item.district},
            #{item.shopId} )
        </foreach>
    </insert>


    <update id="batchUpdate" parameterType="java.util.List">
        <foreach collection="list" item="item" index="index" open="" close="" separator=";">
            UPDATE t_reward_guide_range
            <set>
                <if test="item.guideId != null and item.guideId !=''  ">
                    guide_id = #{item.guideId},
                </if>
                <if test="item.type != null and item.type !=''  ">
                    type = #{item.type},
                </if>
                <if test="item.province != null and item.province !=''  ">
                    province = #{item.province},
                </if>
                <if test="item.city != null and item.city !=''  ">
                    city = #{item.city},
                </if>
                <if test="item.district != null and item.district !=''  ">
                    district = #{item.district},
                </if>
                <if test="item.shopId != null and item.shopId !=''  ">
                    shop_id = #{item.shopId},
                </if>
            </set>
            WHERE id = #{item.id}
        </foreach>
    </update>

    <delete id="batchDelete">
        delete from
        t_reward_guide_range
        where id in
        <foreach collection="list" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="selectIdsByGuideId" resultType="integer">
        SELECT
        id
        FROM t_reward_guide_range
        where guide_id = #{guideId}
    </select>

    <update id="update" parameterType="com.intelliquor.cloud.shop.system.model.RewardGuideRangeModel">
        UPDATE t_reward_guide_range
        <set>
            <if test="guideId != null and guideId !=''  ">
                guide_id = #{guideId},
            </if>
            <if test="type != null and type !=''  ">
                type = #{type},
            </if>
            <if test="province != null and province !=''  ">
                province = #{province},
            </if>
            <if test="city != null and city !=''  ">
                city = #{city},
            </if>
            <if test="district != null and district !=''  ">
                district = #{district},
            </if>
            <if test="shopId != null and shopId !=''  ">
                shop_id = #{shopId},
            </if>
            <if test="createTime != null and createTime !=''  ">
                create_time = #{createTime},
            </if>
        </set>
        WHERE id = #{id}
    </update>

    <delete id="delete" parameterType="integer">
        DELETE FROM t_reward_guide_range
        WHERE id = #{id}
    </delete>

    <select id="getById" resultMap="baseResultMap">
        SELECT
        <include refid="baseColumn"/>
        FROM t_reward_guide_range
        WHERE id = #{id}
    </select>

    <select id="getByGuideIdWithShop" resultMap="baseResultMap">
        SELECT
        <include refid="unionColumn"/>
        FROM t_reward_guide_range r
        left join t_member_shop s on s.id = r.shop_id
        WHERE r.guide_id = #{guideId}
    </select>


    <select id="getByGuideIdAndType" resultMap="baseResultMap">
        SELECT
        <include refid="baseColumn"/>
        FROM t_reward_guide_range
        WHERE guide_id = #{guideId}
        and type = #{type}
        <if test="shopId != null  ">
            and shop_id = #{shopId},
        </if>
        <if test="province != null and province !=''  ">
            and (r.province = #{province} or r.province = '')
        </if>
        <if test="city != null and city !=''  ">
            and (r.city = #{city} or r.city = '')
        </if>
        <if test="district != null and district !=''  ">
            and (r.district = #{district} or r.district = '')
        </if>
    </select>


</mapper>