#!/bin/bash

echo "======================================"
echo "上账中心新方法测试"
echo "======================================"
echo ""

echo "正在运行上账中心四个新方法的测试..."
echo ""

# 进入shop-common目录
cd shop-common

echo "1. 测试积分奖励方法..."
mvn test -Dtest=AccountCenterMethodsTest#testScoreRewardMethods -q

echo ""
echo "2. 测试红包奖励方法..."
mvn test -Dtest=AccountCenterMethodsTest#testCashRewardMethod -q

echo ""
echo "3. 测试终端实物奖励方法..."
mvn test -Dtest=AccountCenterMethodsTest#testTerminalGoodsRewardMethod -q

echo ""
echo "4. 测试经销商实物奖励方法..."
mvn test -Dtest=AccountCenterMethodsTest#testDealerGoodsRewardMethod -q

echo ""
echo "======================================"
echo "测试完成！"
echo "======================================"
echo ""
echo "测试内容说明:"
echo "- 模拟了四个原有方法对应的新方法调用"
echo "- 展示了测试数据的组装过程"
echo "- 对比了新旧方法的特点差异"
echo ""
echo "如需实际集成测试，请提供真实数据:"
echo "- 终端/经销商信息"
echo "- 奖励配置信息"
echo "- 扫码记录数据"
echo "- 实物奖励订单信息" 