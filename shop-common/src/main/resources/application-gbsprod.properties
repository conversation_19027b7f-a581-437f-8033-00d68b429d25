#æ ¹æ®åºåºåå·è·åææç ä¿¡æ¯
gtsyn.queryDeliveryInfoByStoreId=https://gtzstest.tasly.com/enterprisewx/zy/facade/queryDeliveryInfoByStoreId/{storeId}
gtsyn.checkBeforeShipUrl =https://gtzstest.tasly.com/enterprisewx/zy/facade/query-confirm-delivery
#ç»å¤é¨æä¾æ¥å£çtokenå å¯å¯é¥
open.signKey=1Y3L1A4L5Y20A23H132G2928RA9SD8W2
#å¥åº·é¥®éé¡¾é®
drink_advisory_mgr_api.url=https://gbsgf.guotaijiu.com/drink_advisory_mgr_api
#å¥åº·é¥®éé¡¾é®ç§¯ååæ­¥æ¥å£
drink_advisory_mgr_api.sync_integral.url=${drink_advisory_mgr_api.url}/data-api/memberOpen/shopRewardReceive


#ä»æº¯æºè·åç ä¿¡æ¯ åºåºä¿¡æ¯ ç²¾ç®ç
xuanwu.getSimpleCodeInfo.url = https://gtzstest.tasly.com/enterprisewx/zy/facade/queryDeliveryDetailInfo

send.oa.orderurl = http://oa.guotaijiu.com/api/km-review/kmReviewRestService/addReview1
send.oa.orderapprovalurl = http://oa.guotaijiu.com/api/km-review/kmReviewRestService/queryApproveProcess


#åä¿¡ç­ä¿¡éç½®
huaxin.sms.url=https://dx.ipyy.net/smsJson.aspx
huaxin.sms.account=GTJ
huaxin.sms.password=123456
huaxin.sms.signName=å½å°éä¸

#é£ä¹¦åéæ¥è­¦éç½®
feishu.webhook.url = https://open.feishu.cn/open-apis/bot/v2/hook/3f70f4b6-83f0-4d6c-9524-407b4171d014
feishu.webhook.secret = mmTAO1E2GfeAANpHTjTT7e



####ä¸­å°æ¥å£ãBEGINã##########################################################################################

xuanwu.accountinfocode = 1733052964442083328
xuanwu.opentypecode = guobiaoshi-data-server
xuanwu.opentypesecret = gbs8888
xuanwu.clienttypecode = 1
xuanwu.base.domain = http://************:17000
## çæ­¦ä¸­å°åå
xuanwu.baseUrl = ${xuanwu.base.domain}/api/teapi/dy-biz

# è·åtoken
xuanwu.token.url = ${xuanwu.base.domain}/api/auth/openlogin

# ç§¯åè®¢åæ°æ®æ¥æ¶æ¥å£
xuanwu.sorce.order.url = ${xuanwu.base.domain}/api/teapi/dy-biz/1685820930619740249/1685820930619740248
# ç§¯åè®¢åå¯¹è´¦æ¥å£
xuanwu.sorce.order.check.account.url = ${xuanwu.base.domain}/api/teapi/dy-biz/1685820930619740249/1685831584265146462
xuanwu.getContractTypeUrl =  ${xuanwu.base.domain}/api/teapi/dy-biz/1404623815841026146/1734139606225522753
#å°åæ´æ¢æ¥å£
xuanwu.changeaddress.url=${xuanwu.base.domain}/api/teapi/dy-biz/1631543050440413205/1684104766726213731
####ä¸­å°æ¥å£ãENDã##########################################################################################

