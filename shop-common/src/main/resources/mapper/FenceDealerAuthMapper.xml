<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.intelliquor.cloud.shop.common.dao.FenceDealerAuthMapper">
    <select id="selectPageList" resultType="com.intelliquor.cloud.shop.common.model.resp.DealerAuthResp">
        SELECT t.id,d.dealer_name,d.dealer_code,t.contract_code,IFNULL(t.update_time,t.create_time) sys_date
        FROM t_fence_dealer_auth t,t_cloud_dealer_info d WHERE t.dealer_id = d.id AND t.is_delete =0
        <if test="dealerName != null and dealerName != ''">
            AND d.dealer_name LIKE concat('%', #{dealerName}, '%')
        </if>
        <if test="dealerCode != null and dealerCode != ''">
            AND d.dealer_code LIKE concat('%', #{dealerCode}, '%')
        </if>
        <if test="contractCode != null and contractCode != ''">
            AND t.contract_code LIKE concat('%', #{contractCode}, '%')
        </if>
        <if test="province != null and province != '' ">
            AND EXISTS (SELECT 1 FROM t_fence_dealer_auth_area da WHERE da.is_delete=0 AND da.auth_id = t.id and da.province = #{province})
        </if>
        <if test="city != null and city != '' ">
            AND EXISTS (SELECT 1 FROM t_fence_dealer_auth_area da WHERE da.is_delete=0 AND da.auth_id = t.id and da.city = #{city})
        </if>
        <if test="district != null and district != '' ">
            AND EXISTS (SELECT 1 FROM t_fence_dealer_auth_area da WHERE da.is_delete=0 AND da.auth_id = t.id and da.district = #{district})
        </if>
        ORDER BY t.id
    </select>
</mapper>
