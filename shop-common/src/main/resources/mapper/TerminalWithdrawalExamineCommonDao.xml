<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.intelliquor.cloud.shop.common.dao.TerminalWithdrawalExamineCommonDao">
    <select id="getTerminalInfoByNameOrDealerName" resultType="com.intelliquor.cloud.shop.common.model.resp.TerminalShopInfoResp">
        select id as shopId, name as shopName, linkphone
        from t_member_shop
        <where>
            status = 0 and (is_withdrawal <![CDATA[<>]]> 1 or ISNULL(is_withdrawal))
            <if test="shopName != null and shopName != ''">
                and name like CONCAT('%',#{shopName},'%')
            </if>
            <if test="dealerName != null and dealerName != ''">
                and dealer_name like CONCAT('%',#{dealerName},'%')
            </if>
        </where>
    </select>
    <select id="getTerminalWithdrawalExamine" resultType="com.intelliquor.cloud.shop.common.model.resp.TerminalShopInfoResp">
        select id as shopId, name as shopName, main_code as mainCode,
        dealer_code as dealerCode, linkphone, dealer_name as dealerName, is_withdrawal as isWithdrawal
        from t_member_shop
        <where>
            status = 0 and is_withdrawal = 1
            <if test="shopName != null and shopName != ''">
                and name like CONCAT('%',#{shopName},'%')
            </if>
            <if test="dealerName != null and dealerName != ''">
                and dealer_name like CONCAT('%',#{dealerName},'%')
            </if>
        </where>
    </select>
    <update id="updateMemberShopByIsWithdrawal">
        update t_member_shop set is_withdrawal = #{isWithdrawal} where id = #{shopId}
    </update>
    <select id="getTerminalPaymentInfoByShopId"
            resultType="com.intelliquor.cloud.shop.common.model.resp.TerminalShopInfoResp">
        SELECT
	        ms.id AS shopId,
	        ms.name AS shopName,
	        ms.linkman AS linkman,
	        ms.linkphone AS linkphone,
	        ms.main_code AS mainCode,
	        ms.dealer_code AS dealerCode,
	        ms.address AS address,
	        ts.id AS terminalShopId,
	        tsc.contract_code AS contractCode,
	        ts.receiving_payment_type AS receivingPaymentType,
	        ts.receiving_payment_name AS receivingPaymentName,
	        ts.receiving_payment_account AS receivingPaymentAccount,
	        ts.receiving_payment_bank AS receivingPaymentBank,
	        ts.receiving_payment_account_picture AS receivingPaymentAccountPicture,
	        ms.virtual_amount AS virtualAmount,
			cdi.dealer_name AS dealerName,
			cdi.linkman AS dealerUser,
			cdi.phone AS dealerPhone
        FROM
	        t_member_shop ms
	    LEFT JOIN t_terminal_shop ts ON ms.id = ts.member_shop_id
		LEFT JOIN t_terminal_shop_contract tsc ON ms.id = tsc.member_shop_id
		LEFT JOIN t_cloud_dealer_info cdi ON tsc.dealer_code = cdi.dealer_code
        WHERE
	        ms.id =  #{shopId}
    </select>

    <select id="getTerminalPaymentInfoByShopId2"
            resultType="com.intelliquor.cloud.shop.common.model.resp.TerminalShopInfoResp">
        SELECT
            ms.id AS shopId,
            ms.name AS shopName,
            ms.linkman AS linkman,
            ms.linkphone AS linkphone,
            ms.main_code AS mainCode,
            ms.address AS address,
            ts.id AS terminalShopId,
            tsc.contract_code AS contractCode,
            ts.receiving_payment_type AS receivingPaymentType,
            ts.receiving_payment_name AS receivingPaymentName,
            ts.receiving_payment_account AS receivingPaymentAccount,
            ts.receiving_payment_bank AS receivingPaymentBank,
            ts.receiving_payment_account_picture AS receivingPaymentAccountPicture,
            ms.virtual_amount AS virtualAmount,
            parent.dealer_code AS dealerCode,
            parent.dealer_name AS dealerName,
            parent.linkman AS dealerUser,
            parent.phone AS dealerPhone,
            parent.account_type AS dealerAccountType
        FROM
            t_member_shop ms
                LEFT JOIN t_terminal_shop ts ON ms.id = ts.member_shop_id
                LEFT JOIN t_terminal_shop_contract tsc ON ms.id = tsc.member_shop_id
                LEFT JOIN t_cloud_dealer_info di ON di.dealer_code = ms.dealer_code
                LEFT JOIN t_cloud_dealer_relation rel ON rel.dealer_id = di.id
                LEFT JOIN t_cloud_dealer_info parent ON parent.id = rel.parent_id
        WHERE
            ms.id =  #{shopId}
    </select>

    <select id="getDealerPaymentInfoByShopId" resultType="com.intelliquor.cloud.shop.common.model.resp.TerminalShopInfoResp">
        SELECT
            ms.id AS shopId,
            ms.name AS shopName,
            ms.linkman AS linkman,
            ms.linkphone AS linkphone,
            ms.main_code AS mainCode,
            ms.address AS address,
            rel.contract_code AS contractCode,
            payi.receiving_payment_type AS receivingPaymentType,
            payi.receiving_payment_name AS receivingPaymentName,
            payi.receiving_payment_account AS receivingPaymentAccount,
            payi.receiving_payment_bank AS receivingPaymentBank,
            payi.receiving_payment_account_picture AS receivingPaymentAccountPicture,
            ms.virtual_amount AS virtualAmount,
            parent.dealer_name AS dealerName,
            parent.dealer_code AS dealerCode,
            parent.linkman AS dealerUser,
            parent.phone AS dealerPhone,
            parent.account_type AS dealerAccountType
        FROM
            t_cloud_dealer_payment_information payi
                LEFT JOIN t_member_shop ms ON ms.id = payi.shop_id
                LEFT JOIN t_cloud_dealer_info dealer ON dealer.id = payi.dealer_id
                LEFT JOIN t_cloud_dealer_relation relation ON payi.dealer_id = relation.dealer_id
                LEFT JOIN t_cloud_dealer_info parent ON parent.id = relation.parent_id
                LEFT JOIN t_dealer_contract_rel rel ON rel.dealer_code = dealer.dealer_code
        WHERE payi.shop_id = #{shopId} and payi.is_delete=0
    </select>

    <select id="getVirtualAmountByShopId" resultType="com.intelliquor.cloud.shop.common.model.resp.TerminalShopInfoResp">
        select id AS shopId, virtual_amount from t_member_shop where id = #{shopId}
    </select>
    <update id="updateVirtualAmountByShopIdAndVirtualAmount">
        update t_member_shop
        <trim prefix="set" suffixOverrides=",">
            <if test="virtualAmount != null">
                virtual_amount = virtual_amount - #{virtualAmount},
            </if>
        </trim>
        where id = #{shopId} and (virtual_amount - #{virtualAmount} <![CDATA[ >= ]]> 0)
    </update>
    <update id="updateTerminalShopPayment">
        update t_terminal_shop
        <trim prefix="set" suffixOverrides=",">
            <if test="receivingPaymentType != null">
                receiving_payment_type = #{receivingPaymentType},
            </if>
            <if test="receivingPaymentName != null and receivingPaymentName != ''">
                receiving_payment_name = #{receivingPaymentName},
            </if>
            <if test="receivingPaymentAccount != null and receivingPaymentAccount != ''">
                receiving_payment_account = #{receivingPaymentAccount},
            </if>
            <if test="receivingPaymentBank != null and receivingPaymentBank != ''">
                receiving_payment_bank = #{receivingPaymentBank},
            </if>
            <if test="receivingPaymentAccountPicture != null and receivingPaymentAccountPicture != ''">
                receiving_payment_account_picture = #{receivingPaymentAccountPicture},
            </if>
        </trim>
        where id = #{terminalShopId}
    </update>
    <select id="checkMessageByPhoneAndCode" resultType="integer">
        select count(*) from t_system_message
        <where>
            DATE(send_time) = CURRENT_DATE()
            <if test="phone != null and phone != ''">
                and phone = #{phone}
            </if>
            <if test="code != null and code != ''">
                and code = #{code}
            </if>
        </where>
    </select>
    <select id="getTerminalWithdrawalStatusByShopId" resultType="integer">
        select IFNULL(is_withdrawal,0) from t_member_shop where id = #{shopId}
    </select>
    <select id="getWithdrawalDealerStatus" resultType="integer">
        select IFNULL(status,0) from t_terminal_withdrawal_dealer_config
        <where>
            <if test="contractCode != null and contractCode != ''">
                and contract_code = #{contractCode}
            </if>
            <if test="dealerCode != null and dealerCode != ''">
                and dealer_code = #{dealerCode}
            </if>
        </where>
    </select>
    <select id="getAccountTypeByShopId" resultType="integer">
        select nn.account_type from t_member_shop mm
        left join t_cloud_dealer_info nn on mm.dealer_code = nn.dealer_code
        where mm.id = #{shopId}
        limit 1
    </select>

    <update id="updateVirtualAmountByShopIdAddVirtualAmount">
        update t_member_shop
        <trim prefix="set" suffixOverrides=",">
            <if test="virtualAmount != null">
                virtual_amount = virtual_amount + #{virtualAmount},
            </if>
        </trim>
        where id = #{shopId}
    </update>
</mapper>
