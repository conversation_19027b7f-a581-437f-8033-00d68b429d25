package com.intelliquor.cloud.shop.common.basequery.method;

import com.baomidou.mybatisplus.core.enums.SqlMethod;
import com.baomidou.mybatisplus.core.injector.AbstractMethod;
import com.baomidou.mybatisplus.core.metadata.TableInfo;
import com.baomidou.mybatisplus.core.toolkit.sql.SqlScriptUtils;
import com.intelliquor.cloud.shop.common.basequery.method.utils.SelectiveFieldsUtils;
import org.apache.ibatis.mapping.MappedStatement;
import org.apache.ibatis.mapping.SqlSource;

public class UpdateSelectiveMethod extends AbstractMethod
{
    public UpdateSelectiveMethod() {
        super(SqlMethod.UPDATE.getMethod());
    }

    @Override
	public MappedStatement injectMappedStatement(Class<?> mapperClass,Class<?> modelClass, TableInfo tableInfo)
	{
		SqlMethod sqlMethod = SqlMethod.UPDATE;

		String sql = String.format(sqlMethod.getSql(),
				tableInfo.getTableName(),
				sqlSetSelective(true, true, tableInfo, true, ENTITY, ENTITY_DOT),
				sqlWhereEntityWrapper(true, tableInfo), sqlComment());

		SqlSource sqlSource = languageDriver.createSqlSource(configuration,sql, modelClass);
		return this.addUpdateMappedStatement(mapperClass, modelClass,"updateSelective", sqlSource);
	}

	protected String sqlSetSelective(boolean logic, boolean ew, TableInfo table,
			boolean judgeAliasNull, String alias, String prefix)
	{
		String sqlScript = SelectiveFieldsUtils.getAllSqlSet(table,logic, prefix);
        if (judgeAliasNull) {
            sqlScript = SqlScriptUtils.convertIf(sqlScript, String.format("%s != null", alias), true);
        }
        if (ew) {
            sqlScript += NEWLINE;
            sqlScript += SqlScriptUtils.convertIf(SqlScriptUtils.unSafeParam(U_WRAPPER_SQL_SET),
                String.format("%s != null and %s != null", WRAPPER, U_WRAPPER_SQL_SET), false);
        }
        sqlScript = SqlScriptUtils.convertSet(sqlScript);
        return sqlScript;
	}


}
