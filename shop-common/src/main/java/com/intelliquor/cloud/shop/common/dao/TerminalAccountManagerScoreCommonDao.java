package com.intelliquor.cloud.shop.common.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.intelliquor.cloud.shop.common.model.TerminalAccountManagerScoreCommonModel;
import com.intelliquor.cloud.shop.common.model.req.TerminalAccountManagerScoreReq;
import com.intelliquor.cloud.shop.common.model.resp.TerminalAccountManagerScoreResp;

import java.util.List;

/**
 * <p>
 * 业务员评分表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-25
 */
public interface TerminalAccountManagerScoreCommonDao extends BaseMapper<TerminalAccountManagerScoreCommonModel> {

    List<TerminalAccountManagerScoreResp> getList(TerminalAccountManagerScoreReq req);

    void updateScore(TerminalAccountManagerScoreCommonModel req);
}
