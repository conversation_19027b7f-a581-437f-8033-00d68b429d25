package com.intelliquor.cloud.shop.common.model.resp;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class CloudDealerRewardRecordResp {

    /**
     * ID
     */
    private Long id;

    /**
     * 经销商编码
     */
    private String dealerCode;

    /**
     * 经销商名称
     */
    private String dealerName;

    /**
     * 账户类型 1:终端进货 2:零售推荐奖励
     */
    private Integer originType;

    /**
     * 来源Id
     */
    private Integer originId;

    /**
     * 上帐金额
     */
    private BigDecimal rewardAmount;

    /**
     * 订单编号，进货取批次号，零售取origin_id
     */
    private String transaction;

    /**
     * 同步时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date sendTime;

    /**
     * 是否同步 0-未同步  1-同步成功  2-同步失败
     */
    private Integer status;

    /**
     * 对账状态0-未对账 1-正常 2-异常
     */
    private Integer checkStatus;
}
