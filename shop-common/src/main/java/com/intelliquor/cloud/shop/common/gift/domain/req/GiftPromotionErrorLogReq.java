package com.intelliquor.cloud.shop.common.gift.domain.req;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
/**
 * 进货赠酒错误日志 Bo对象
 * <AUTHOR> @date 2024-07-16
 */
@Data
public class GiftPromotionErrorLogReq {
    
    private static final long serialVersionUID = 1L;
	
    private Long id;

    /**
     * 订单ID
     */
    @ApiModelProperty(value = "订单ID")
    private Long orderId;
    /**
     * 订单编号
     */
    @ApiModelProperty(value = "订单编号")
    private String orderCode;
    /**
     * 错误信息
     */
    @ApiModelProperty(value = "错误信息")
    private String errorMsg;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;





}
