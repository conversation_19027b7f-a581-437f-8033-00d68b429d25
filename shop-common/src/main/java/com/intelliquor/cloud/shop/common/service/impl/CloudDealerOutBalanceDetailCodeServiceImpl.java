package com.intelliquor.cloud.shop.common.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.intelliquor.cloud.shop.common.exception.BusinessException;
import com.intelliquor.cloud.shop.common.model.CloudDealerOutBalance;
import com.intelliquor.cloud.shop.common.model.CloudDealerOutBalanceDetailCodeModel;
import com.intelliquor.cloud.shop.common.dao.CloudDealerOutBalanceDetailCodeDao;
import com.intelliquor.cloud.shop.common.model.resp.SuyuanDeliveryInfo;
import com.intelliquor.cloud.shop.common.service.CallXWCommonService;
import com.intelliquor.cloud.shop.common.service.ICloudDealerOutBalanceDetailCodeService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.intelliquor.cloud.shop.common.service.ICloudDealerOutBalanceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * 经销商出库明细码数据表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-19
 */
@Service
public class CloudDealerOutBalanceDetailCodeServiceImpl extends ServiceImpl<CloudDealerOutBalanceDetailCodeDao, CloudDealerOutBalanceDetailCodeModel> implements ICloudDealerOutBalanceDetailCodeService {

    @Autowired
    private ICloudDealerOutBalanceService cloudDealerOutBalanceService;
    @Autowired
    private CallXWCommonService callXWCommonService;

    public List<CloudDealerOutBalanceDetailCodeModel> getCodeList(Integer balanceId){
        CloudDealerOutBalance balance = cloudDealerOutBalanceService.getById(balanceId);
        if(balance == null){
            throw new BusinessException("未找到出货单");
        }
        List<CloudDealerOutBalanceDetailCodeModel> list = new ArrayList<>();
        if(balance.getSource().equals(1)){
            QueryWrapper<CloudDealerOutBalanceDetailCodeModel> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("balance_id", balanceId);
            list = this.list(queryWrapper);
        }else {
            List<SuyuanDeliveryInfo> suyuanDeliveryInfos = callXWCommonService.queryDeliveryInfoByStoreId(balance.getTransaction());
            for(SuyuanDeliveryInfo item : suyuanDeliveryInfos){
                CloudDealerOutBalanceDetailCodeModel code = new CloudDealerOutBalanceDetailCodeModel();
                code.setCode(item.getQrcode());
                code.setFromDealerCode(balance.getFromDealerCode());
                code.setToDealerCode(balance.getToDealerCode());
                list.add(code);
            }
        }
        return list;
    }
}
