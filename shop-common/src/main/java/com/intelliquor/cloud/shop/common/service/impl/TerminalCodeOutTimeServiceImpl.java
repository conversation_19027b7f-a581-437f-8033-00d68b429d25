package com.intelliquor.cloud.shop.common.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.intelliquor.cloud.shop.common.constant.RedisConstant;
import com.intelliquor.cloud.shop.common.dao.TerminalCodeOutTimeDao;
import com.intelliquor.cloud.shop.common.model.TerminalCodeOutTimeModel;
import com.intelliquor.cloud.shop.common.service.IRequestLogService;
import com.intelliquor.cloud.shop.common.service.ITerminalCodeOutTimeService;
import com.intelliquor.cloud.shop.common.utils.DateUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <p>
 * 经销商最早发货时间表。用于处理2023年之前出货的不发放奖励，本表数据来源来自退货和终端扫码收货业务 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-30
 */
@Service
public class TerminalCodeOutTimeServiceImpl extends ServiceImpl<TerminalCodeOutTimeDao, TerminalCodeOutTimeModel> implements ITerminalCodeOutTimeService {

    @Autowired
    private TerminalCodeOutTimeDao terminalCodeOutTimeDao;

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;
    @Autowired
    private IRequestLogService requestLogService;

    @Value("${gt_company_id}")
    private Integer gtCompanyId;
    /**
     * 保存码的最早发货日期
     *
     * @param list
     */
    @Override
    public void insertOrUpdate(List<TerminalCodeOutTimeModel> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        //组装码信息
        list.forEach(time -> {
            time.setCompanyId(gtCompanyId);
            if (StringUtils.isNotBlank(time.getCode())) {
                if (Objects.isNull(time.getDealerOutTime())) {
                    time.setDealerOutTime(new Date());
                }
            }
        });
        List<String> codeList = list.stream().map(TerminalCodeOutTimeModel::getCode).filter(StringUtils::isNotBlank).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(codeList)) {
            List<TerminalCodeOutTimeModel> oldList = query().in("code", codeList).list();
            if (CollectionUtils.isEmpty(oldList)) {
                saveBatch(list);
            } else {
                list.forEach(time -> {
                    //无记录的查询
                    if (!oldList.stream().map(TerminalCodeOutTimeModel::getCode).collect(Collectors.toList()).contains(time.getCode())) {
                        save(time);
                    } else {
                        List<TerminalCodeOutTimeModel> updateList = oldList.stream().filter(old ->
                                time.getDealerOutTime().compareTo(old.getDealerOutTime()) < 0).collect(Collectors.toList());
                        if (CollectionUtils.isNotEmpty(updateList)) {
                            updateList.forEach(item -> {
                                item.setDealerOutTime(time.getDealerOutTime());
                                item.setUpdateTime(new Date());
                            });
                            saveOrUpdateBatch(updateList);
                        }
                    }
                });
            }
        }
    }

    /**
     * @author: HLQ
     * @Date: 2023/3/30 18:07
     * @Description: 根据箱码和发货时间新增或者更新 返回最早的收货时间
     */
    @Override
    public Date insertOrUpdate(TerminalCodeOutTimeModel model) {
        if (Objects.isNull(model) || StringUtils.isBlank(model.getCode())) {
            return null;
        }
        //redis记录
        String terminalCodeouttimeBeanKey = String.format(RedisConstant.REDIS_TERMINAL_CODEOUTTIME_BEAN_KEY, model.getCode());
        if (redisTemplate.hasKey(terminalCodeouttimeBeanKey)) {
            TerminalCodeOutTimeModel terminalCodeOut = (TerminalCodeOutTimeModel) redisTemplate.opsForValue().get(terminalCodeouttimeBeanKey);
            Date dealerOutTime = model.getDealerOutTime();
            Date outTime = terminalCodeOut.getDealerOutTime();
            float compDate = DateUtils.compDate(dealerOutTime, outTime);
            if (compDate < 0) {//更新
                terminalCodeOut.setDealerOutTime(dealerOutTime);
                terminalCodeOut.setUpdateTime(new Date());
                terminalCodeOutTimeDao.updateById(terminalCodeOut);
                return dealerOutTime;
            } else {//取数据库的返回
                return outTime;
            }
        } else {
            //数据库记录
            TerminalCodeOutTimeModel terminalCodeOutTime = terminalCodeOutTimeDao.getBeanByCode(model.getCode());
            if (Objects.isNull(terminalCodeOutTime)) {
                model.setCreateTime(new Date());
                model.setCompanyId(gtCompanyId);
                terminalCodeOutTimeDao.insert(model);
                redisTemplate.opsForValue().set(terminalCodeouttimeBeanKey, model, 2, TimeUnit.HOURS);
                return model.getDealerOutTime();
            } else {
                Date dealerOutTime = model.getDealerOutTime();
                Date outTime = terminalCodeOutTime.getDealerOutTime();
                float compDate = DateUtils.compDate(dealerOutTime, outTime);
                if (compDate < 0) {//更新
                    terminalCodeOutTime.setDealerOutTime(dealerOutTime);
                    terminalCodeOutTime.setUpdateTime(new Date());
                    terminalCodeOutTimeDao.updateById(terminalCodeOutTime);
                    redisTemplate.opsForValue().set(terminalCodeouttimeBeanKey, terminalCodeOutTime, 2, TimeUnit.HOURS);
                    return dealerOutTime;
                } else {//取数据库的返回
                    redisTemplate.opsForValue().set(terminalCodeouttimeBeanKey, terminalCodeOutTime, 2, TimeUnit.HOURS);
                    return outTime;
                }
            }
        }


//        String terminalCodeouttimeKey =  String.format(RedisConstant.REDIS_TERMINAL_CODEOUTTIME_KEY, model.getCode());
//        String terminalCodeouttimeBeanKey =  String.format(RedisConstant.REDIS_TERMINAL_CODEOUTTIME_BEAN_KEY, model.getCode());
//        Boolean aBoolean = redisTemplate.opsForValue().setIfAbsent(terminalCodeouttimeKey, model.getCode(), 2, TimeUnit.HOURS);
//
//        TerminalCodeOutTimeModel terminalCodeOutTime = terminalCodeOutTimeDao.getBeanByCode(model.getCode());
//        if (Objects.isNull(terminalCodeOutTime) && aBoolean) {
//            model.setCreateTime(new Date());
//            model.setCompanyId(50);
//            terminalCodeOutTimeDao.insert(model);
//            redisTemplate.opsForValue().set(terminalCodeouttimeBeanKey, model, 3, TimeUnit.HOURS);
//            return model.getDealerOutTime();
//        }
//
//        if(!aBoolean){// 可能因为并发  导致上一次没有保存到数据库
//            Date dealerOutTime = model.getDealerOutTime();
//            //记录下  后期可屏蔽
//            RequestLog requestLog = new RequestLog();
//            requestLog.setReqName("记录码第一次发货时间重复:" + model.getCode());
//            requestLog.setReqKey(model.getCode());
//            requestLog.setReqType(42);
//            requestLog.setCreateDate(new Date());
//            requestLog.setReqJson(JSONObject.toJSONString(model));
//            requestLog.setReqKey(model.getCode());
//
//            Object o = redisTemplate.opsForValue().get(terminalCodeouttimeBeanKey);
//            if(Objects.nonNull(o)){
//                TerminalCodeOutTimeModel terminalCodeOut = (TerminalCodeOutTimeModel)o;
//                requestLog.setReqJson(JSONObject.toJSONString(terminalCodeOut));
//            }
//
//            requestLog.setResCode("0");
//            // requestLogService.insertLog(requestLog);
//            return dealerOutTime;
//        }else{
//            Date dealerOutTime = model.getDealerOutTime();
//            Date outTime = terminalCodeOutTime.getDealerOutTime();
//            float compDate = DateUtils.compDate(dealerOutTime, outTime);
//            if (compDate < 0) {//更新
//                terminalCodeOutTime.setDealerOutTime(dealerOutTime);
//                terminalCodeOutTime.setUpdateTime(new Date());
//                terminalCodeOutTimeDao.updateById(terminalCodeOutTime);
//                return dealerOutTime;
//            } else {//取数据库的返回
//                return outTime;
//            }
//        }
    }

    @Override
    public List<TerminalCodeOutTimeModel> selectPageList(TerminalCodeOutTimeModel model, Integer page, Integer limit) {
        PageHelper.startPage(page, limit);
        QueryWrapper<TerminalCodeOutTimeModel> qw = new QueryWrapper<>();
        qw.like(StringUtils.isNotBlank(model.getCode()), "code", model.getCode());
        List<TerminalCodeOutTimeModel> outTimeModelList = this.list(qw);
        return outTimeModelList;
    }
}
