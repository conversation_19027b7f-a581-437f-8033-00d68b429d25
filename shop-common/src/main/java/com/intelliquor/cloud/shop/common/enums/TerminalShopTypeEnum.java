package com.intelliquor.cloud.shop.common.enums;

import java.util.*;

import org.apache.commons.lang3.StringUtils;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

/**
 * @Auther: sunshine
 * @Date: 2024/4/26
 * @Description: 终端类型
 */
@Slf4j
@Getter
@AllArgsConstructor
public enum TerminalShopTypeEnum {
    // type, storeCode, name
    CHANNEL_TERMINAL(0, "5", "渠道终端"),
    CATERING_TERMINAL(1, "7", "餐饮终端"),
    TEAM_TERMINAL(2, "9", "团购终端"),
    COMPANY_TERMINAL(3, "6", "企业终端"),
    CHAIN_TERMINAL(4, "8", "连锁终端"),
    // 原代码中 case 5 没有对应的 storeType，这里暂时不添加，或者根据实际业务补充
    // 5表示会员终端
    MEMBER_TERMINAL(5, StringUtils.EMPTY, "会员终端"),
    // case 6 对应 10-渠道终端会员
    CHANNEL_TERMINAL_MEMBER(6, "10", "渠道终端会员"),
    // case 7 对应 11-连锁终端会员
    CHAIN_TERMINAL_MEMBER(7, "11", "连锁终端会员"),
    // case 8 对应 12-非会员虚拟终端
    NON_MEMBER_VIRTUAL_TERMINAL(8, "12", "非会员虚拟终端"),
    // case 9 对应 14-超级终端
    SUPER_TERMINAL(9, "14", "超级终端"),
    CHAIN_CATERING_HOTEL_TERMINAL(10, "15", "连锁型餐饮酒店"),
    FEATURED_CATERING_HOTEL_TERMINAL(11, "16", "特色型餐饮酒店"),
    BUSINESS_CATERING_HOTEL_TERMINAL(12, "17", "商务型餐饮酒店"),
    BANQUET_CATERING_HOTEL_TERMINAL(13, "18", "宴席型餐饮酒店"),
    CHANNEL_CHAIN_TERMINAL(14, "19", "渠道连锁终端"),
    ONLINE_TERMINAL(20, "20", "线上终端"), // 示例，如果type>=20的storeCode就是type的字符串值，这里可以保持一致
    SUPER_SHOP_TERMINAL(21, "21", "商超连锁终端"), // 示例
    ;

    private final Integer type;
    private final String storeCode; // storeCode是中台需要的字段，对应这里定义的terminalShopType。但是从20开始，terminalShopType和中台的字段完全对应（增加终端类型时，仍需要中台相关人员确认。）
    private final String name;

    /**
     * 不需要验证营业执照的终端类型集合
     */
    private static final Set<TerminalShopTypeEnum> NO_LICENSE_VERIFICATION_TYPES = Collections.unmodifiableSet(EnumSet.of(
            TEAM_TERMINAL,           // 团购终端
            ONLINE_TERMINAL,         // 线上终端
            SUPER_SHOP_TERMINAL      // 商超连锁终端
    ));

    /**
     * 不能进行协议变更的终端类型集合
     */
    private static final Set<TerminalShopTypeEnum> NO_PROTOCOL_CHANGE_TYPES = Collections.unmodifiableSet(EnumSet.of(
            TEAM_TERMINAL,           // 团购终端
            MEMBER_TERMINAL,          // 会员终端
            ONLINE_TERMINAL,         // 线上终端
            SUPER_SHOP_TERMINAL      // 商超连锁终端
    ));

    static Map<Integer, TerminalShopTypeEnum> enumMap = new HashMap<>();

    static {
        for (TerminalShopTypeEnum type : TerminalShopTypeEnum.values()) {
            enumMap.put(type.getType(), type);
        }
    }

    public static TerminalShopTypeEnum getName(Integer type) {
        return enumMap.get(type);
    }

    public static String getNameByType(Integer type) {
        TerminalShopTypeEnum enumValue = enumMap.get(type);
        return Objects.nonNull(enumValue) ? enumValue.getName() : StringUtils.EMPTY; // 使用Objects.nonNull避免空指针
    }

    /**
     * 获取终端类型对应的storeCode
     * @param type 终端类型
     * @return storeCode，如果未找到返回空字符串
     * @throws IllegalArgumentException 当type为null时
     */
    public static String getStoreCodeByType(Integer type) {
        if (Objects.isNull(type)) {
            log.info("终端类型为空，无法获取storeCode");
            throw new IllegalArgumentException("终端类型为空，无法获取storeCode.");
        }

        TerminalShopTypeEnum enumValue = enumMap.get(type);
        if (Objects.isNull(enumValue)) {
            log.info("未找到对应的终端类型枚举，type: {}", type);
            return StringUtils.EMPTY;
        }

        String storeCode = enumValue.getStoreCode();
        if (StringUtils.isBlank(storeCode)) {
            log.info("终端类型 {} 的storeCode为空", type);
        }

        return storeCode;
    }

    /**
     * 判断是否需要验证营业执照
     * @param type 终端类型
     * @return true-需要验证，false-不需要验证
     */
    public static boolean needsLicenseVerification(Integer type) {
        TerminalShopTypeEnum enumValue = enumMap.get(type);
        // 如果 enumValue 为 null，表示该类型未定义，根据业务需求，应视为需要验证
        // 否则，根据 NO_LICENSE_VERIFICATION_TYPES 判断是否需要验证
        return enumValue == null || !NO_LICENSE_VERIFICATION_TYPES.contains(enumValue);
    }

    /**
     * 判断是否可以进行协议变更
     * @param type 终端类型
     * @return true-可以变更，false-不可以变更
     */
    public static boolean canChangeProtocol(Integer type) {
        TerminalShopTypeEnum enumValue = enumMap.get(type);
        // 如果 enumValue 为 null，表示该类型未定义，应视为可以变更协议
        // 否则，根据 NO_PROTOCOL_CHANGE_TYPES 判断是否可以变更
        return enumValue == null || !NO_PROTOCOL_CHANGE_TYPES.contains(enumValue);
    }

    /**
     * 判断当前枚举实例是否需要验证营业执照
     * @return true-需要验证，false-不需要验证
     */
    public boolean needsLicenseVerification() {
        return !NO_LICENSE_VERIFICATION_TYPES.contains(this);
    }

    /**
     * 判断当前枚举实例是否可以进行协议变更
     * @return true-可以变更，false-不可以变更
     */
    public boolean canChangeProtocol() {
        return !NO_PROTOCOL_CHANGE_TYPES.contains(this);
    }
}
