package com.intelliquor.cloud.shop.common.service;

import com.intelliquor.cloud.shop.common.model.FenceDealerBlankMarketModel;
import com.baomidou.mybatisplus.extension.service.IService;
import com.intelliquor.cloud.shop.common.model.req.FenceDealerBlankMarketReq;
import com.intelliquor.cloud.shop.common.model.resp.FenceDealerBlankMarketResp;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【t_fence_delaer_blank_market(经销商空白市场授权)】的数据库操作Service
* @createDate 2024-03-07 15:04:47
*/
public interface FenceDealerBlankMarketService extends IService<FenceDealerBlankMarketModel> {

    List<FenceDealerBlankMarketResp> getPageList(int page, int limit, FenceDealerBlankMarketReq req);

    void saveFenceDealerBlankMarketModel(FenceDealerBlankMarketModel model);

    void editAuth(Integer id);
}
