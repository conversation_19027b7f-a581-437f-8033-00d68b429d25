package com.intelliquor.cloud.shop.common.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.intelliquor.cloud.shop.common.dao.TerminalActivityRewardCycleRelationDao;
import com.intelliquor.cloud.shop.common.enums.DeleteFlagEnum;
import com.intelliquor.cloud.shop.common.model.TerminalActivityRewardCycleRelationModel;
import com.intelliquor.cloud.shop.common.service.TerminalActivityRewardCycleRelationService;
import com.intelliquor.cloud.shop.common.utils.ObjectUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;


/**
 * <p>
 * 活动周期关系 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-18
 */
@Service
public class TerminalActivityRewardCycleRelationServiceImpl extends ServiceImpl<TerminalActivityRewardCycleRelationDao, TerminalActivityRewardCycleRelationModel>
        implements TerminalActivityRewardCycleRelationService {

    @Autowired
    private TerminalActivityRewardCycleRelationDao terminalActivityRewardCycleRelationDao;
    @Override
    public List<TerminalActivityRewardCycleRelationModel> getActivityRewardCycleRelationList(TerminalActivityRewardCycleRelationModel terminalActivityRewardCycleRelationModel) {
        LambdaQueryWrapper<TerminalActivityRewardCycleRelationModel> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(ObjectUtil.isNotNull(terminalActivityRewardCycleRelationModel.getActivityId()), TerminalActivityRewardCycleRelationModel::getActivityId, terminalActivityRewardCycleRelationModel.getActivityId());
        lambdaQueryWrapper.eq(ObjectUtil.isNotNull(terminalActivityRewardCycleRelationModel.getRewardCycleId()), TerminalActivityRewardCycleRelationModel::getRewardCycleId, terminalActivityRewardCycleRelationModel.getRewardCycleId());
        lambdaQueryWrapper.eq(TerminalActivityRewardCycleRelationModel::getIsDelete, DeleteFlagEnum.NOT_DELETE.getKey());
        List<TerminalActivityRewardCycleRelationModel> terminalActivityRewardCycleRelationList = terminalActivityRewardCycleRelationDao.selectList(lambdaQueryWrapper);
        return terminalActivityRewardCycleRelationList;
    }
}
