package com.intelliquor.cloud.shop.common.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;

import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 订货单一键收货异步任务表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-21
 */
@Getter
@Setter
@TableName("t_shop_dealer_order_async_delivery_task")
public class ShopDealerOrderAsyncDeliveryTaskModel implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;


    /**
     * 订单编号
     */
    private String orderCode;

    /**
     * 1 码信息及货权校验 2收货业务处理 3收货完成
     */
    private Integer taskId;

    /**
     * 任务名称
     */
    private String taskName;

    /**
     * 0未开始 1是正在执行 2是执行成功 3是执行失败
     */
    private Integer taskStatus;

    /**
     * 任务信息
     */
    private String taskMsg;

    /**
     * 商户ID
     */
    private Integer companyId;

    private String qrcode;

    private Date taskBeginTime;

    private Date taskEndTime;


}
