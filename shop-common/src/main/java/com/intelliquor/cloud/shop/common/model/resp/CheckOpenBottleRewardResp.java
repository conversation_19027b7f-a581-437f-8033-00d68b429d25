package com.intelliquor.cloud.shop.common.model.resp;

import com.github.pagehelper.PageInfo;
import com.intelliquor.cloud.shop.common.model.CheckOpenBottleRewardModel;
import com.intelliquor.cloud.shop.common.model.CheckSaleRewardModel;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @description 开瓶奖励稽核记录出参
 * @createDate 2023-10-24
 */
@Data
public class CheckOpenBottleRewardResp {

    /**
     * 应发积分
     */
    private BigDecimal rewardScore;

    /**
     * 实发积分
     */
    private BigDecimal actualScore;

    /**
     * 异常未发放积分
     */
    private BigDecimal abnormalScore;

    /**
     * 开瓶奖励稽核记录列表
     */
    private PageInfo<CheckOpenBottleRewardModel> checkOpenBottleRewardPageInfo;
}
