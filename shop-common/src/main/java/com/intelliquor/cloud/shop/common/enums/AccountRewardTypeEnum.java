package com.intelliquor.cloud.shop.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 奖励类型枚举
 */
@AllArgsConstructor
@Getter
public enum AccountRewardTypeEnum {

    /**
     * 积分
     */
    SCORE("1", "积分"),

    /**
     * 奖励入账
     */
    ACCOUNT_IN("2", "奖励入账"),

    /**
     * 进搭赠池
     */
    GIFT_POOL("3", "进搭赠池"),

    /**
     * 生成订单（直投）
     */
    GENERATE_ORDER("4", "生成订单（直投）");

    /**
     * 编码
     */
    private final String code;

    /**
     * 描述
     */
    private final String desc;

    /**
     * 根据 code 查找枚举
     *
     * @param code 枚举编码
     * @return 匹配的枚举，未匹配返回 null
     */
    public static AccountRewardTypeEnum findByCode(String code) {
        for (AccountRewardTypeEnum type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }
}
