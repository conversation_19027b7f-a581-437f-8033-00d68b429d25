package com.intelliquor.cloud.shop.common.gift.rule.steps;

import com.intelliquor.cloud.shop.common.basequery.lambda.LambdaUtils;
import com.intelliquor.cloud.shop.common.basequery.utils.StrUtil;
import com.intelliquor.cloud.shop.common.gift.domain.req.GiftPromotionInfoReq;
import com.intelliquor.cloud.shop.common.gift.domain.req.PromotionGiftReq;
import com.intelliquor.cloud.shop.common.gift.domain.rule.GiftRuleContractTypeDto;
import com.intelliquor.cloud.shop.common.gift.domain.rule.GiftRuleContractTypeLevelDto;
import com.intelliquor.cloud.shop.common.gift.rule.RuleResult;
import com.intelliquor.cloud.shop.common.gift.rule.RuleStep;
import java.util.List;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class ContractTypeRuleStep implements RuleStep<GiftRuleContractTypeDto, PromotionGiftReq> {


    @Override
    public RuleResult execute(RuleResult result, GiftRuleContractTypeDto giftRuleContractTypeDto, PromotionGiftReq promotionGiftReq) {
        log.info("{}:执行合同类型规则匹配",promotionGiftReq);
        if (Objects.equals(StrUtil.strValue(giftRuleContractTypeDto.getContractType()), promotionGiftReq.getContractType())) {
            log.info("合同类型匹配成功");
            List<GiftRuleContractTypeLevelDto> contractTypeLevelList = giftRuleContractTypeDto.getContractTypeLevelList();
            for (GiftRuleContractTypeLevelDto giftRuleContractTypeLevelDto : contractTypeLevelList) {
                //未来需要判断终端等级
                int giftNum=(promotionGiftReq.getProductNum()/giftRuleContractTypeLevelDto.getJoinNum())
                        *giftRuleContractTypeLevelDto.getGiftNum();
                if(giftNum<=0){
                    return result.next();
                }
                log.info("数量匹配成功");
                result.getResult().setGiftNum(giftNum);
                result.getResult().setRuleType(getRuleName());
                result.getResult().setRuleId(giftRuleContractTypeLevelDto.getId());
                return result.success();
            }
        }
        return result.next();
    }

    @Override
    public String getRuleName() {
        return LambdaUtils.property(GiftPromotionInfoReq::getContractTypeRule);
    }
}
