package com.intelliquor.cloud.shop.common.model;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 收款信息表
 * @TableName t_cloud_dealer_payment_information
 */
@TableName(value ="t_cloud_dealer_payment_information")
@Data
public class CloudDealerPaymentInformation implements Serializable {
    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * t_member_shop表id
     */
    private Integer shopId;

    /**
     * t_cloud_dealer_info表id
     */
    private Long dealerId;

    /**
     * 收款方式(0:支付宝 1:微信 2:银行)
     */
    private Integer receivingPaymentType;

    /**
     * 收款人名称
     */
    private String receivingPaymentName;

    /**
     * 收款人账户
     */
    private String receivingPaymentAccount;

    /**
     * 收款银行
     */
    private String receivingPaymentBank;

    /**
     * 收款人账户图片
     */
    private String receivingPaymentAccountPicture;

    /**
     * 账号类型(1：普通经销商,2：体验中心,3：普通分销商,4：合伙人, 5: 终端，6:平台公司)
     */
    private Integer accountType;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 是否删除（1是0否）
     */
    private Integer isDelete;

    /**
     * 公司ID
     */
    private Integer companyId;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}