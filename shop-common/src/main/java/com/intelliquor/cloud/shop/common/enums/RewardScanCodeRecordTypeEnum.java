package com.intelliquor.cloud.shop.common.enums;

/**
 * 扫码记录类型
 */
public enum RewardScanCodeRecordTypeEnum {

    NONE(1, "查无此码"),
    EXISTED(2, "已存在"),
    TIME_ERR(3, "日期不在奖励政策之内"),
    NO_REWARD(4, "暂无奖励"),
    NORMAL(5, "正常数据"),
    DEL(6, "大后台删除数据"),
    REPACT(7, "符合多条活动奖励政策，请联系管理员"),
    RECORD_CODE(8, "记录扫码");

    private Integer type;

    private String msg;

    RewardScanCodeRecordTypeEnum(Integer type, String msg) {
        this.type = type;
        this.msg = msg;
    }

    public Integer getType() {
        return type;
    }

    public String getMsg() {
        return msg;
    }

}
