package com.intelliquor.cloud.shop.common.service;

import com.intelliquor.cloud.shop.common.model.WechatPayDetailModel;
import com.baomidou.mybatisplus.extension.service.IService;
import com.intelliquor.cloud.shop.common.model.req.WechatPayDetailReq;
import com.intelliquor.cloud.shop.common.model.resp.WechatPayDetailRespModel;

import java.util.List;

/**
 * <p>
 * 微信支付记录详情 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-04
 */
public interface WechatPayDetailService extends IService<WechatPayDetailModel> {

    List<WechatPayDetailRespModel> selectRedEnvelopList(WechatPayDetailReq req);

    void onPayAgain(WechatPayDetailModel req);

    void export(WechatPayDetailReq model);
}
