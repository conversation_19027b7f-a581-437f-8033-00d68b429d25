package com.intelliquor.cloud.shop.common.model.vo;

import lombok.Data;

import java.math.BigDecimal;

/**
 * Description: 收货限制
 * <AUTHOR>
 * @create 2024-06-18 17:38
 */
@Data
public class ScanLimitVo {
    /**
     * 标准内进货数量
     */
    private Integer standardInNum;

    /**
     * 标准内单价
     */
    private BigDecimal standardPrice;

    /**
     * 标准外进货数量
     */
    private Integer standardOutNum;

    /**
     * 标准外单价
     */
    private BigDecimal standardOutPrice;

    /**
     * 门槛值
     */
    private Integer rewardThresholdNum;

    /**
     * 奖励上线
     */
    private Long rewardLimit;

    /**
     * 实际进货量
     */
    private Integer actualReceiveNum;

    /**
     * 计算公式
     */
    private String calculateFormula;
}
