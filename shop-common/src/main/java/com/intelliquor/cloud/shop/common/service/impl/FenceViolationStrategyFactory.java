package com.intelliquor.cloud.shop.common.service.impl;

import com.intelliquor.cloud.shop.common.enums.BusinessLineEnum;
import com.intelliquor.cloud.shop.common.service.IFenceViolationStrategy;
import lombok.NoArgsConstructor;

import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Description:
 *
 * <AUTHOR>
 * @since 2024/8/26 13:46
 */
@NoArgsConstructor(access = lombok.AccessLevel.PRIVATE)
public class FenceViolationStrategyFactory {
    private static final Map<BusinessLineEnum, IFenceViolationStrategy> STRATEGRY_MAP = new ConcurrentHashMap<>();

    public static void register(BusinessLineEnum businessLineEnum, IFenceViolationStrategy fenceViolationStrategy) {
        if (Objects.isNull(businessLineEnum) || Objects.isNull(fenceViolationStrategy)) {
            return;
        }
        STRATEGRY_MAP.putIfAbsent(businessLineEnum, fenceViolationStrategy);
    }

    public static IFenceViolationStrategy getInvokeHandler(BusinessLineEnum businessLineEnum) {
        return STRATEGRY_MAP.get(businessLineEnum);
    }
}
