package com.intelliquor.cloud.shop.common.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.intelliquor.cloud.shop.common.dao.TerminalDisplayActivityConfigDao;
import com.intelliquor.cloud.shop.common.dao.TerminalProductProtocolConfigDao;
import com.intelliquor.cloud.shop.common.enums.*;
import com.intelliquor.cloud.shop.common.exception.BusinessException;
import com.intelliquor.cloud.shop.common.model.*;
import com.intelliquor.cloud.shop.common.model.vo.ScanLimitVo;
import com.intelliquor.cloud.shop.common.service.*;
import com.intelliquor.cloud.shop.common.service.resp.ProtocoTimeParamResp;
import com.intelliquor.cloud.shop.common.utils.ColumnConstant;
import com.intelliquor.cloud.shop.common.utils.ObjectUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.util.*;

/**
 * <p>
 * 陈列配置 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-13
 */
@Service
public class TerminalDisplayActivityConfigServiceImpl extends ServiceImpl<TerminalDisplayActivityConfigDao, TerminalDisplayActivityConfigModel>
        implements TerminalDisplayActivityConfigService{

}
