package com.intelliquor.cloud.shop.common.model.business.terminalReward;

import com.intelliquor.cloud.shop.common.enums.ProtocolTypeEnum;
import com.intelliquor.cloud.shop.common.model.DisplayResultModel;
import com.intelliquor.cloud.shop.common.model.TerminalProtocolFinancialStatementModel;
import com.intelliquor.cloud.shop.common.service.ITerminalShopCommonService;
import com.intelliquor.cloud.shop.common.service.TerminalProductProtocolV2Service;
import com.intelliquor.cloud.shop.common.service.TerminalProtocolFinancialStatementService;
import com.intelliquor.cloud.shop.common.service.TerminalRewardCalculatorService;
import lombok.Data;
import org.springframework.beans.BeanUtils;

import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;

@Data
public abstract class TerminalRewardCalculator {

//    @Autowired
//    private ITerminalShopCommonService terminalShopCommonService;
//    @Autowired
//    private TerminalRewardCalculatorService terminalRewardCalculatorService;

    private RewardCalculateInfo rewardCalculateInfo;
    private TerminalShopInfo terminalShopInfo;
    private ProtocolRelationInfo protocolRelationInfo;
    protected ProtocolConfigInfo protocolConfigInfo;
    protected ProtocolBusinessindicatorsInfo protocolBusinessindicatorsInfo;
    private DefaultInfo defaultInfo;
//    private TerminalRewardCalculatorService terminalRewardCalculatorService;
    private Integer sourceFlag;
    private String serialNum;
    protected TerminalRewardCalculatorService rewardCalculatorService;

    protected ITerminalShopCommonService terminalShopCommonService;

    protected TerminalProductProtocolV2Service terminalProductProtocolV2Service;

    private TerminalProtocolFinancialStatementService terminalProtocolFinancialStatementService;

    private Integer rewardType;

    private DisplayResultModel displayResultModel = new DisplayResultModel();

    public TerminalRewardCalculator() {
    }

    public TerminalRewardCalculator(RewardCalculateInfo rewardCalculateInfo) {
        this.rewardCalculateInfo = rewardCalculateInfo;
    }

    protected RewardCalculateInfo getRewardCalculateInfo(String serialNum){
        this.rewardCalculateInfo = rewardCalculatorService.getRewardCalculateInfo(
                this.rewardCalculateInfo.getDisplayYear(), this.rewardCalculateInfo.getDisplayQuarter(),
                this.rewardCalculateInfo.getTerminalProductProtocolRelationModel(), this.rewardCalculateInfo.getBatchId(),
                this.rewardCalculateInfo.getOldId(),serialNum, this.getRewardType());
        return this.rewardCalculateInfo;
    }

    private TerminalShopInfo getTerminalShopInfo() {
        this.terminalShopInfo = terminalShopCommonService.getTerminalInfoByTerminalShopId(rewardCalculateInfo.getTerminalProductProtocolRelationModel().getTerminalShopId());
        return this.terminalShopInfo;
    }

    private ProtocolRelationInfo getProtocolRelation() {
        this.protocolRelationInfo = rewardCalculatorService.getProtocolRelation(this.getRewardCalculateInfo().getTerminalProductProtocolRelationModel());
        return this.protocolRelationInfo;
    }

    abstract ProtocolConfigInfo getProtocolConfigInfo();

    abstract ProtocolBusinessindicatorsInfo getProtocolBusinessindicatorsInfo();

    private DefaultInfo getDefaultInfo() {
        this.defaultInfo = rewardCalculatorService.getDefaultInfo(sourceFlag);
        return this.defaultInfo;
    }

    public void start(){
        this.getRewardCalculateInfo(serialNum);
        this.getTerminalShopInfo();
        this.getProtocolRelation();
        this.getDefaultInfo();
    }

    public DisplayResultModel output(){


        this.mappingRewardCalculateInfo();
        this.mappingTerminalShopInfo();
        this.mappingProtocolRelation();
        this.mappingProtocolConfigInfo();
        this.mappingProtocolBusinessindicatorsInfo();
        this.mappingDefaultInfo();

        return this.displayResultModel;
    }


    private void mappingRewardCalculateInfo() {
        BeanUtils.copyProperties(rewardCalculateInfo, displayResultModel);
    }
    private void mappingTerminalShopInfo() {
        BeanUtils.copyProperties(terminalShopInfo, displayResultModel);
        displayResultModel.setAffiliateId(terminalShopInfo.getAffiliateId());
        displayResultModel.setRegionId(terminalShopInfo.getRegionId());
    }
    private void mappingProtocolRelation() { BeanUtils.copyProperties(protocolRelationInfo, displayResultModel); }
    private void mappingProtocolConfigInfo() {
        BeanUtils.copyProperties(protocolConfigInfo, displayResultModel);
        displayResultModel.setLevelCode(Objects.nonNull(protocolConfigInfo.getLevelCode()) ? protocolConfigInfo.getLevelCode().longValue() :null);
    }
    private void mappingProtocolBusinessindicatorsInfo() {
        BeanUtils.copyProperties(protocolBusinessindicatorsInfo, displayResultModel);
//      与测试沟通：陈列协议积分展示为空，包量协议无值展示是0
        displayResultModel.setDisplayAmount(
                protocolConfigInfo.getProtocolTypeNew() == ProtocolTypeEnum.DISPLAY.getKey() ? null :
                Objects.nonNull(protocolBusinessindicatorsInfo.getDisplayAmount()) ? protocolBusinessindicatorsInfo.getDisplayAmount() : BigDecimal.ZERO
        );
    }
    private void mappingDefaultInfo() {
        BeanUtils.copyProperties(defaultInfo, displayResultModel);
    }

    public List<TerminalProtocolFinancialStatementModel> getDisplayDataRecord(){
        List<TerminalProtocolFinancialStatementModel> terminalProtocolFinancialStatementDisplayList = terminalProtocolFinancialStatementService.getDisplayDataRecord(this.rewardCalculateInfo);
        return terminalProtocolFinancialStatementDisplayList;
    }

    public List<TerminalProtocolFinancialStatementModel> getScanDataRecord(){
        List<TerminalProtocolFinancialStatementModel> terminalProtocolFinancialStatementScanList = terminalProtocolFinancialStatementService.getScanDataRecord(this.rewardCalculateInfo);
        return terminalProtocolFinancialStatementScanList;
    }

}
