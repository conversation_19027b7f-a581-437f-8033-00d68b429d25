package com.intelliquor.cloud.shop.common.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.intelliquor.cloud.shop.common.dao.ShopDealerOrderDetailPlusMapper;
import com.intelliquor.cloud.shop.common.model.ShopDealerOrderDetailPlus;
import com.intelliquor.cloud.shop.common.service.ShopDealerOrderDetailPlusService;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【t_shop_dealer_order_detail(分销商订货单商品表)】的数据库操作Service实现
* @createDate 2023-08-01 16:20:55
*/
@Service
public class ShopDealerOrderDetailPlusServiceImpl extends ServiceImpl<ShopDealerOrderDetailPlusMapper, ShopDealerOrderDetailPlus>
    implements ShopDealerOrderDetailPlusService{

}




