package com.intelliquor.cloud.shop.common.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.intelliquor.cloud.shop.common.model.CloudDealerActivityAppointDealerCommonModel;
import com.intelliquor.cloud.shop.common.model.DealerActivityNewCommonModel;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;
import java.util.Map;

@Repository
public interface CloudDealerActivityAppointDealerCommonDao extends BaseMapper<CloudDealerActivityAppointDealerCommonModel> {

    List<DealerActivityNewCommonModel> selectDealerActivityNewListByShopInStock(@Param("dealerCode") String dealerCode,
                                                                                @Param("nowDate") Date nowDate,
                                                                                @Param("goodsCode") String goodsCode);

    /**
     * 通过省市区获取活动列表
     *
     * @param province
     * @param nowDate
     * @param goodsCode
     * @return
     */
    List<DealerActivityNewCommonModel> selectDealerActivityNewListByShopInStockAddress(@Param("province") String province,
                                                                                       @Param("city") String city,
                                                                                       @Param("district") String district,
                                                                                       @Param("nowDate") Date nowDate,
                                                                                       @Param("goodsCode") String goodsCode);

    List<DealerActivityNewCommonModel> selectDealerActivityNewListByDealerOutStock(@Param("dealerCode") String dealerCode,
                                                                                   @Param("nowDate") Date nowDate,
                                                                                   @Param("goodsCode") String goodsCode);

    List<DealerActivityNewCommonModel> selectDealerActivityNewListByDealerOutStockAddress(@Param("province") String province,
                                                                                          @Param("city") String city,
                                                                                          @Param("district") String district,
                                                                                          @Param("nowDate") Date nowDate,
                                                                                          @Param("goodsCode") String goodsCode);

    List<DealerActivityNewCommonModel> selectDealerActivityNewListByDealerCodeAndGoodsCode(@Param("dealerCode") String dealerCode,
                                                                                           @Param("goodsCode") String goodsCode,
                                                                                           @Param("nowDate") Date nowDate);

    List<DealerActivityNewCommonModel> selectDealerActivityNewListByDealerAreaAndGoodsCodeAddress(@Param("province") String province,
                                                                                                  @Param("city") String city,
                                                                                                  @Param("district") String district,
                                                                                                  @Param("goodsCode") String goodsCode,
                                                                                                  @Param("nowDate") Date nowDate);

    List<DealerActivityNewCommonModel> selectDealerActivityNewListByTimeAndGoodsCode(@Param("goodsCode") String goodsCode,
                                                                                     @Param("nowDate") Date nowDate,
                                                                                     @Param("rewardType") Integer rewardType);

    Integer selectDealerActivityAppointDealerMember(Map<String, Object> paramMap);
}
