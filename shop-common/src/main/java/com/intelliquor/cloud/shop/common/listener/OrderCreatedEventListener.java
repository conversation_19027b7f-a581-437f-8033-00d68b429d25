package com.intelliquor.cloud.shop.common.listener;

import com.intelliquor.cloud.shop.common.event.OrderCreatedEvent;
import com.intelliquor.cloud.shop.common.yongyou.service.YyShopDealerOrderService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;
import org.springframework.transaction.event.TransactionPhase;
import org.springframework.transaction.event.TransactionalEventListener;

/**
 * 订单创建事件监听器
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class OrderCreatedEventListener {

    private final YyShopDealerOrderService yyShopDealerOrderService;

    /**
     * 监听订单创建事件，在事务提交后执行异步操作
     * @param event 订单创建事件
     */
    @EventListener
    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT)
    public void handleOrderCreatedEvent(OrderCreatedEvent event) {
        try {
            Long mainOrderId = event.getSource();
            log.info("订单创建事件触发，开始异步保存订单，mainOrderId: {}", mainOrderId);
            yyShopDealerOrderService.saveOrderAsync(mainOrderId);
            log.info("订单创建事件处理完成，mainOrderId: {}", mainOrderId);
        } catch (Exception e) {
            log.error("订单创建事件处理异常，mainOrderId: {}", event.getSource(), e);
        }
    }
} 