package com.intelliquor.cloud.shop.common.service.impl;

import com.intelliquor.cloud.shop.common.model.ReturnApplyBlackGoodsModel;
import com.intelliquor.cloud.shop.common.dao.ReturnApplyBlackGoodsDao;
import com.intelliquor.cloud.shop.common.service.ReturnApplyBlackGoodsService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 溯源退货申请限制表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-15
 */
@Service
public class ReturnApplyBlackGoodsServiceImpl extends ServiceImpl<ReturnApplyBlackGoodsDao, ReturnApplyBlackGoodsModel> implements ReturnApplyBlackGoodsService {

}
