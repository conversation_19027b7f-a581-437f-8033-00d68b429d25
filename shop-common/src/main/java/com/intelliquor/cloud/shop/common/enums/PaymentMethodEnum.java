package com.intelliquor.cloud.shop.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * Description:
 *
 * <AUTHOR>
 * @since 2025/03/20 09:01
 */
@Getter
@AllArgsConstructor
public enum PaymentMethodEnum {

    // 兑付方式：1.进搭赠池,2.直投酒,3.积分

    GIFT_POOL(1, "进搭赠池"),

    DIRECT_INVESTMENT(2, "直投酒"),

    POINTS(3, "积分"),
    ;

    private final Integer key;

    private final String value;


    /**
     * 根据key获取value
     */
    public static String getValue(Integer key) {
        for (PaymentMethodEnum value : values()) {
            if (value.getKey().equals(key)) {
                return value.getValue();
            }
        }
        return null;
    }

    public static PaymentMethodEnum getByKey(Integer key) {
        for (PaymentMethodEnum value : values()) {
            if (value.getKey().equals(key)) {
                return value;
            }
        }
        return null;
    }

    public static PaymentMethodEnum getByValue(String value) {
        for (PaymentMethodEnum e : values()) {
            if (e.getValue().equals(value)) {
                return e;
            }
        }
        return null;
    }

}
