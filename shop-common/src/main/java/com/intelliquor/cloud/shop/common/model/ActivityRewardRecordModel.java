package com.intelliquor.cloud.shop.common.model;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.Objects;
import java.util.Optional;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.intelliquor.cloud.shop.common.enums.ActivityRewardSubTypeEnum;
import com.intelliquor.cloud.shop.common.enums.ActivityRewardTypeEnum;
import com.intelliquor.cloud.shop.common.exception.BusinessException;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * 联盟活动正常奖励记录
 * @TableName t_activity_reward_record
 */
@Slf4j
@TableName(value ="t_activity_reward_record")
@Data
public class ActivityRewardRecordModel implements Serializable {
    /**
     *
     */
    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * 奖励对象Id（经销商、终端等角色）
     */
    private Integer shopId;

    /**
     * 活动类型，与下面的说明对应
     */
    private Integer activityType;

    /**
     * 事件类型
     */
    private Integer eventType;

    /**
     * 场景说明
     */
    private String sceneRemark;

    /**
     * 记录来源业务主表
     */
    private String originTable;

    /**
     * 记录来源业务主表id
     */
    private Integer originId;

    /**
     * 类型 1-收入 2-支出
     */
    private Integer type;

    /**
     * 活动的id,t_reward_activity表的主键
     */
    private Integer activityId;

    /**
     * 活动配置版本t_reward_activity_version表的id
     */
    private Integer activityVersion;

    /**
     * 奖励类型 1 红包 2实物 3积分
     */
    private Integer rewardType;

    /**
     * 红包金额
     */
    private BigDecimal money;

    /**
     * 商户Id
     */
    private Integer companyId;

    /**
     * 奖励是实物时t_shop_activity_settting表的id
     */
    private Long rewardId;

    /**
     * 积分
     */
    private BigDecimal integral;

    /**
     * 发放状态0待发放 1发放中 2发放成功 3发放失败
     */
    private Integer sendStatus;

    /**
     * 发放结果信息
     */
    private String sendMsg;

    /**
     * 扩展字段，用于存储需要展示的关联信息
     */
    private String extendData;

    /**
     * 创建时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 更新时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /**
     * 备注
     */
    private String remark;

    /**
     * 是否删除0-否1-是
     */
    private Integer isDelete;

    /**
     * 是否有效 0无效 1有效
     */
    private Integer status;

    /**
     * 码信息，开瓶、进货时扫的码
     */
    private String code;

    /**
     * 进货订单编号
     */
    private String orderCode;

    /**
     * 与t_cloud_dealer_info中对应 1：普通经销商,2：体验中心,3：普通分销商,4：合伙人, 5: 终端，6:平台公司
     */
    private Integer accountType;

    /**
     * 会员 0否 1是
     */
    private String isMember;

    /**
     * 合同编号
     */
    private String contractCode;

    /**
     * 重试次数
     */
    private Integer retryCount;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    @TableField(exist = false)
    private String openid;

    @TableField(exist = false)
    private String shopName;

    /**
     * 微信每条转账的批次单号
     */
    @TableField(exist = false)
    private String outDetailNo;


    /**
     * 码对应数量
     */
    @TableField(exist = false)
    private Integer codeCount;

    /**
     *同步中台预算时间
     */
    private Date sendZtTime;

    /**
     *同步中台预算返回信息
     */
    private String sendZtMsg;

    /**
     *同步中台预算状态(0:未推送,1:推送成功(需回调);2:推送失败;3:中台处理成功;4:中台预算失败)
     */
    private Integer sysState;

    /**
     *中台预算回调时间
     */
    private Date backZtTime;

    /**
     *中台预算回调信息
     */
    private String backZtMsg;


    /**
     * 合同类型
     */
    private Integer contractType;

    /**
     *  推送数据业务ID
     */
    private String  businessId;


    /**
     * 获取活动奖励子类型
     * <p>
     * 从扩展数据中解析子类型，如果解析失败则返回默认子类型
     * </p>
     * 
     * @return 活动奖励子类型编码
     */
    public String getRewardSubType() {
        return Optional.ofNullable(extendData)
                .filter(StringUtils::isNotEmpty)
                .map(data -> {
                    try {
                        return JSONObject.parseObject(data, ExtendDataBean.class);
                    } catch (Exception e) {
                        log.warn("获取活动奖励子类型异常: {}", e.getMessage());
                        if (log.isDebugEnabled()) {
                            log.debug("活动奖励子类型解析详细异常:", e);
                        }
                        throw new BusinessException("获取活动奖励子类型失败，请检查数据格式");
                    }
                })
                .map(ExtendDataBean::getRewardSubType)
                .orElse(getDefaultRewardSubType());
    }

    private String getDefaultRewardSubType(){
        if(Objects.equals(activityType, ActivityRewardTypeEnum.OPEN_BOTTLE.getCode())){
            return ActivityRewardSubTypeEnum.OPEN_BOTTLE_NORMAL_REWARD.getCode();
        }
        return ActivityRewardSubTypeEnum.DYNAMIC_MARKET_NORMAL_REWARD.getCode();
    }
}

