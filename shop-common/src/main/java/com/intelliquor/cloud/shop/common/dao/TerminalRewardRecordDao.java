package com.intelliquor.cloud.shop.common.dao;

import com.intelliquor.cloud.shop.common.model.TerminalRewardRecordModel;
import com.intelliquor.cloud.shop.common.model.req.PaymentRecordReq;
import com.intelliquor.cloud.shop.common.model.req.ScoreOrderSummaryReq;
import com.intelliquor.cloud.shop.common.model.resp.*;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 描述：终端联盟收支明细记录 Dao接口
 *
 * <AUTHOR>
 * @date 2022-09-30
 */
@Repository
public interface TerminalRewardRecordDao {


    /**
     * 查询数据信息
     *
     * @param searchMap
     * @return
     */
    List<TerminalRewardRecordModel> selectList(Map<String, Object> searchMap);


    List<TerminalRewardRecordModel> getScoreList(TerminalRewardRecordModel model);

    List<TerminalRewardRecordResp> getScoreList2(TerminalRewardRecordModel model);

    BigDecimal selectInScore(TerminalRewardRecordModel model);

    BigDecimal selectOutScore(TerminalRewardRecordModel model);

    BigDecimal selectOutScoreByShopId(@Param("shopId") Long shopId);

    BigDecimal selectInScoreByShopId(@Param("shopId") Long shopId);

    /**
     * 新增
     *
     * @param model
     * @return
     */
    Integer insert(TerminalRewardRecordModel model);

    /**
     * 更新
     *
     * @param model
     * @return
     */
    Integer update(TerminalRewardRecordModel model);

    /**
     * 删除
     *
     * @param id
     * @return
     */
    Integer delete(Integer id);

    /**
     * 根据ID查询
     *
     * @param id
     * @return
     */
    TerminalRewardRecordModel getById(Integer id);

    List<PaymentRecordResp> selectPaymentRecord(PaymentRecordReq paymentRecordReq);

    BigDecimal selectMonthSumVirtualAmount(PaymentRecordReq paymentRecordReq);

    /**
     * 扫码奖励查询
     */
    PaymentRecordDetailResp selectTerminalScanDetailByBalance(Integer balanceId);

    /**
     * 扫码奖励产品查询
     */
    List<PaymentRecordDetailProductResp> selectTerminalScanDetailProductList(Integer balanceId);

    /**
     * 消费者扫码奖励查询
     */
    PaymentRecordDetailResp selectConsumerScanDetail(Integer id);

    /**
     * 消费者扫码奖励产品查询
     */
    List<PaymentRecordDetailProductResp> selectConsumerScanDetailProductList(Integer id);

    /**
     * 年返奖励查询
     */
    PaymentRecordDetailResp selectYearReturnRecordDetail(Integer id);

    /**
     * 年返奖励产品查询
     */
    List<PaymentRecordDetailProductResp> selectYearReturnRecordDetailProductList(Integer id);

    /**
     * 扫码消耗查询
     */
    PaymentRecordDetailResp selectShopDealerOrder(Integer id);

    /**
     * 扫码消耗产品查询
     */
    List<PaymentRecordDetailProductResp> selectShopDealerOrderProductList(Integer orderId);

    /**
     * 周期奖励查询
     */
    PaymentRecordDetailResp selectCycleDetailByBalanceId(Integer balanceId);

    /**
     * 扫码消耗产品查询
     */
    List<PaymentRecordDetailProductResp> selectCycleDetailProductList(Integer orderId);

    /**
     * 查询收支记录pc端明细
     */
    List<PaymentRecordPCResp> selectTerminalRewardRecordPCList(Map<String, Object> paymentRecordPCReq);

    /**
     * 条件查询
     */
    List<TerminalRewardRecordModel> selectTerminalRewardRecordList(Map<String, Object> searchMap);

    List<TerminalAccountChangeRewardRecordResp> selectTerminalRewardRecordListByShopId(Map<String, Object> map);

    List<TerminalRewardRecordModel> selectSysList();


    String getGoodCodeByConsumerScanDetailId(Integer id);

    Map<String, String> getParamByConsumerScanDetailId(Integer id);

    String getBanquetAccountLogId(Integer id);

    String getDisplayResultOrderById(Integer id);


    /**
     * @author: HLQ
     * @Date: 2023/4/18 15:38
     * @Description: 处理数据使用，勿乱用
     */
    List<TerminalRewardRecordModel> selectListByIds();

    List<TerminalRewardRecordModel> selectListByRemark();

    /**
     * @author: HLQ
     * @Date: 2023/4/18 15:38
     * @Description: 处理数据使用，勿乱用
     */
    TerminalRewardRecordModel selectTerminalRewardRecordModelByShopId(Integer shopId);

    //@Param("startDate") String startDate,@Param("endDate") String endDate
    List<TerminalRewardRecordModel> selectOrderList(Map<String, Object> map);

    List<TerminalRewardRecordModel> selectAllTypeScoreByShopId(Integer shopId);

    /**
     * 根据shop_id集合更新是否发送短信字段（is_send_message）
     *
     * @param shopIdList shop_id集合
     */
    void updateIsSendMessageByShopIds(@Param("shopIdList") List<Integer> shopIdList);

    List<TerminalRewardRecordResp> getConsumerScanDetailListByIds(@Param("ids") List<Integer> ids);

    List<TerminalRewardRecordResp> getBanquetAccountLogByIds(@Param("ids") List<Integer> ids);

    List<TerminalRewardRecordResp> getDisplayResultByIds(@Param("ids") List<Integer> ids);

    List<TerminalRewardRecordModel> getListBySourceParam(TerminalRewardRecordModel model);

    /**
     * 查询合同下终端积分统计数据
     *
     * @param contractCode 合同编号
     * @param shopName     门店名称
     * @return List<PointsSummaryResp>
     */
    List<ShopPointsSummaryResp> selectShopPointsSummary(@Param("contractCode") String contractCode, @Param("shopName") String shopName);

    /**
     * 根据ID查询终端积分历史记录
     *
     * @param shopId 终端
     * @return List<PointsUsedRecordListResp>
     */
    List<PointsUsedRecordListResp> selectPointsUsedRecordListByShopId(@Param("shopId") Long shopId, @Param("type") Integer type);

    /**
     * 查询下级获取积分类型统计
     *
     * @param shopId 终端ID
     * @return List<PointsSummaryResp>
     */
    List<ShopPointsSummaryTypeResp> selectShopPointsSummaryGroupBySource(@Param("shopId") Long shopId);


    List<TerminalRewardRecordModel> selectDistributorSysList();

    BigDecimal selectInScoreByShopIds(@Param("list") List<Integer> shopIdList);

    BigDecimal selectOutScoreByShopIds(@Param("list") List<Integer> shopIdList);

    List<TerminalRewardRecordModel> selectPointsOrderNoReport();

    List<ScoreUsedSummary> scoreUsedSummary(@Param("list") List<Integer> shopIdList);

    List<ScoreUsedOrderSummary> scoreUsedOrderSummary(ScoreOrderSummaryReq req);

    List<ScorePayoutsSummary> scorePayoutsSummary(ScoreOrderSummaryReq req);
}
