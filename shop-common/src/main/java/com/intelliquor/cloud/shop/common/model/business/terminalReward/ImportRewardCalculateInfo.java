package com.intelliquor.cloud.shop.common.model.business.terminalReward;


import com.intelliquor.cloud.shop.common.model.TerminalProductProtocolRelationModel;
import com.intelliquor.cloud.shop.common.model.req.ImportDisplayResultReq;
import io.swagger.models.auth.In;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;


@Getter
@Setter

public class ImportRewardCalculateInfo {


    private Integer terminalShopId;

    /**
     * 协议标准：0-23年标准、1-24 年标准
     */
    private Integer protocolStandard;
    /**
     * 核算批次
     */
    private String batchId;

    /**
     * 流水号
     */
    private String serialNum;

    /**
     * 计算类型(1:主协议的月度奖励  2、超级终端月度奖励  3、陈列月度奖励（算附加协议） 4、超年度包量奖励 5，包量季度奖励；6，陈列季度奖励)
     */
    private Integer computeType;
    /**
     * 奖励周期类型：月、季度、年(0：老数据；1-月度；2-季度；3-年度)
     */
    private Integer cycleType;

    /**
     * 统计年
     */
    private Integer displayYear;

    /**
     * 统计月
     */
    private Integer displayMonth;

    /**
     * 统计季度
     */
    private Integer displayQuarter;
    /**
     * 发送来源 0-job 1-手动
     */
    private Integer sendSource;
    /**
     * 导入数据
     */
    private ImportDisplayResultReq importDisplayResultReq;

    /**
     * 终端副编码
     */
    private Integer deputyCode;

    /**
     * 终端名称
     */
//    private String shopName;
    /**
     * 分公司名称
     */
//    private String affiliateName;
//    /**
//     * 大区名称
//     */
//    private String regionName;
    /**
     * 经销商编码
     */
//    private Integer dealerCode;
//    /**
//     * 经销商名称
//     */
//    private String dealerName;
//    /**
//     * 分销商编码
//     */
//    private Integer distributorCode;
    /**
     * 分销商名称
     */
//    private String distributorName;
//    /**
//     * 合同编码
//     */
//    private Integer contractCode;
//    /**
//     * 合同类型
//     */
//    private Integer contractType;
//
//    /**
//     * 分销商名称
//     */
//    private String computeTypeName;

    /**
     * 奖励积分
     */
//    private BigDecimal displayAmount;
//    /**
//     * 应发产品sku
//     */
//    private Integer productSku;
//
//    /**
//     * 应发产品名称
//     */
//    private String productSkuName;
//
//    /**
//     * 应发产品数量
//     */
//    private Integer productSkuNum;
//
//    /**
//     * 应发产品单位
//     */
//    private String rewardAmountUnit;

//    /**
//     * 是否执行
//     */
//    private Integer isExecute;
//
//    /**
//     * 是否发放
//     */
//    private Integer isScore;
}
