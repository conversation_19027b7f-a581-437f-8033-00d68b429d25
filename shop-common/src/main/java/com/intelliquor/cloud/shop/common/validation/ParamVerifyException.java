package com.intelliquor.cloud.shop.common.validation;

import com.intelliquor.cloud.shop.common.entity.Response;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.validation.BindException;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import javax.validation.ConstraintViolation;
import javax.validation.ConstraintViolationException;
import java.util.List;
import java.util.Set;
import java.util.TreeSet;


/**
 * <h5>描述:全局参数验证异常处理</h5>
 * 设定执行顺序,只要比全局异常处理类靠前就行,否则会报500或者404错误信息
 * 这里的全局异常处理类是MyExceptionHandler.java
 */
@RestControllerAdvice
@Order(Ordered.LOWEST_PRECEDENCE - 1)
public class ParamVerifyException {
    private static final Logger LOG = LoggerFactory.getLogger(ParamVerifyException.class);

    /**
     * 操作成功
     */
    public static Integer  SUCCESS = 200;
    /**
     * 参数无效
     */
    public static Integer  PARAM_INVALID = 400;
    /**
     * 功能:处理普通参数校验失败的异常
     * @param ex
     * @return
     */
    @ExceptionHandler(value = ConstraintViolationException.class)
    public Response ConstraintViolationException(ConstraintViolationException ex) {
        Response restResponse = new Response<>();
        // 使用TreeSet是为了让输出的内容有序输出(默认验证的顺序是随机的)
        Set<String> errorInfoSet = new TreeSet<String>();
        Set<ConstraintViolation<?>> violations = ex.getConstraintViolations();
        if (!violations.isEmpty()) {
            // 设置验证结果状态码
            restResponse.setCode(PARAM_INVALID);
            for (ConstraintViolation<?> item : violations) {
                System.out.println(item.getPropertyPath());
                // 遍历错误字段信息
                errorInfoSet.add(item.getMessage());
                LOG.debug("[{}]{}", item.getPropertyPath(), item.getMessage());
            }

            StringBuffer sbf = new StringBuffer();
            for (String errorInfo : errorInfoSet) {
                sbf.append(errorInfo);
                sbf.append(",");
            }
            restResponse.setError(sbf.substring(0, sbf.length() - 1));
        }

        return restResponse;
    }

    /**
     * 功能: 处理实体类参数校验失败的异常
     * @param bindingResult
     * @return
     */
    @ExceptionHandler(value = BindException.class)
    public Response BindException(BindException bindingResult) {
        Response restResponse = new Response<>();
        if(bindingResult.hasErrors()) {
            // 设置验证结果状态码
            restResponse.setCode(PARAM_INVALID);
            // 获取错误字段信息集合
            List<FieldError> fieldErrorList = bindingResult.getFieldErrors();

            // 使用TreeSet是为了让输出的内容有序输出(默认验证的顺序是随机的)
            Set<String> errorInfoSet = new TreeSet<String>();
            for (FieldError fieldError : fieldErrorList) {
                // 遍历错误字段信息
                errorInfoSet.add(fieldError.getDefaultMessage());
                LOG.debug("[{}.{}]{}", fieldError.getObjectName() , fieldError.getField(), fieldError.getDefaultMessage());
            }

            StringBuffer sbf = new StringBuffer();
            for (String errorInfo : errorInfoSet) {
                sbf.append(errorInfo);
                sbf.append(",");
            }
            // restResponse.setMessage(sbf.substring(0, sbf.length() - 1));
            restResponse.setError(sbf.substring(0, sbf.length() - 1));
        }
        return restResponse;
    }
}

