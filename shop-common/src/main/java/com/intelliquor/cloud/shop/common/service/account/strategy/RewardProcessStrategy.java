package com.intelliquor.cloud.shop.common.service.account.strategy;

import com.intelliquor.cloud.shop.common.model.ActivityRewardRecordModel;
import com.intelliquor.cloud.shop.common.model.ZtBookRewardRecordModel;

/**
 * 奖励处理策略接口
 * 用于统一处理不同类型的奖励推送逻辑
 */
public interface RewardProcessStrategy {
    
    /**
     * 处理奖励
     * @param recordModel 奖励记录
     * @param ztBookRewardRecordModel 中台记录
     * @param uuidThread 线程标识
     */
    void processReward(ActivityRewardRecordModel recordModel, ZtBookRewardRecordModel ztBookRewardRecordModel, String uuidThread);
    
    /**
     * 检查策略是否支持处理此类型的奖励
     * @param recordModel 奖励记录
     * @return 是否支持
     */
    boolean supports(ActivityRewardRecordModel recordModel);
    
    /**
     * 获取策略名称
     * @return 策略名称
     */
    String getStrategyName();
} 