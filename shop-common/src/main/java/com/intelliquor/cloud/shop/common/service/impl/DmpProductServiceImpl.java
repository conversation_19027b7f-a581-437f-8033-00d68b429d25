package com.intelliquor.cloud.shop.common.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.intelliquor.cloud.shop.common.exception.BusinessException;
import com.intelliquor.cloud.shop.common.service.DmpProductService;
import io.searchbox.client.JestClient;
import io.searchbox.core.Search;
import io.searchbox.core.SearchResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.IOException;

/**
 * 直连es产品库
 */
@Service
@Slf4j
public class DmpProductServiceImpl implements DmpProductService {
    @Autowired
    private JestClient jestClient;
    /**
     * @Title: getProductByCode
     * @Description: 根据产品code获取产品信息
     * @param @param code
     * @param @return
     * @return JSONObject
     * @throws
     */
    @Override
    public JSONObject getProductByCodeOrId(String code, String id, Integer companyId) {
        log.info("获取商品信息：code:{},id:{},companyId:{}",code,id,companyId);
        JSONObject product = null;
        if (StringUtils.isNotBlank(code) || StringUtils.isNotBlank(id)) {
            String indexName = "product_"+companyId;
            SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
            BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
            if (StringUtils.isNotBlank(code)) {
                boolQueryBuilder.must(QueryBuilders.termQuery("product_code.keyword", code));
            }
            if (StringUtils.isNotBlank(id)) {
                boolQueryBuilder.must(QueryBuilders.termQuery("id", id));
            }
            boolQueryBuilder.must(QueryBuilders.termQuery("is_delete", 0));
            searchSourceBuilder.query(boolQueryBuilder);
            Search.Builder searchBuild = new Search.Builder(searchSourceBuilder.toString()).addIndex(indexName)
                    .addType("_doc");
            SearchResult rs = null;
            try {
                rs = jestClient.execute(searchBuild.build());
            } catch (IOException e) {
                throw new BusinessException("产品查询异常", e.getMessage());
            }
            if (rs == null || !rs.isSucceeded()) {
                throw new BusinessException("产品查询异常", rs.getErrorMessage());
            } else {
                JSONArray hits = JSONArray.parseArray(StringUtils.join("[", rs.getSourceAsString(), "]"));
                if (hits != null && hits.size() != 0) {
                    product = JSONObject.parseObject(hits.get(0).toString());
                }
            }
        }
        log.info("获取到的产品信息：{}",JSONObject.toJSONString(product));
        return product;
    }


    @Override
    public JSONArray getAllProducts( Integer companyId) {
        log.info("获取商品信息：companyId:{}",companyId);
        JSONArray products = null;
        String indexName = "product_"+companyId;
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();

        boolQueryBuilder.must(QueryBuilders.termQuery("is_delete", 0));
        searchSourceBuilder.query(boolQueryBuilder);
        searchSourceBuilder.size(10000);
        Search.Builder searchBuild = new Search.Builder(searchSourceBuilder.toString()).addIndex(indexName)
                .addType("_doc");
        SearchResult rs = null;
        try {
            rs = jestClient.execute(searchBuild.build());
        } catch (IOException e) {
            throw new BusinessException("产品查询异常", e.getMessage());
        }
        if (rs == null || !rs.isSucceeded()) {
            throw new BusinessException("产品查询异常", rs.getErrorMessage());
        } else {
            products = JSONArray.parseArray(StringUtils.join("[", rs.getSourceAsString(), "]"));
        }
        //log.info("获取到的产品信息：{}",products.toJSONString());
        return products;
    }
}
