package com.intelliquor.cloud.shop.common.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.intelliquor.cloud.shop.common.model.ActivityRewardExceptionRecordModel;
import com.intelliquor.cloud.shop.common.model.FenceViolateOpenBottleAreaRecordModel;
import com.intelliquor.cloud.shop.common.model.req.FenceViolateOpenBottleAreaRecordReq;
import com.intelliquor.cloud.shop.common.model.resp.FenceViolateOpenBottleAreaRecordResp;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【t_fence_violate_open_bottle_area_record(异地开瓶违约记录表)】的数据库操作Mapper
 * @createDate 2024-02-27 16:10:53
 * @Entity com.intelliquor.cloud.shop.common.model.FenceViolateOpenBottleAreaRecordModel
 */
public interface FenceViolateOpenBottleAreaRecordDao extends BaseMapper<FenceViolateOpenBottleAreaRecordModel> {

    List<FenceViolateOpenBottleAreaRecordResp> selectPageList(FenceViolateOpenBottleAreaRecordReq fenceViolateOpenBottleAreaRecordReq);

    /**
     * 查询未处理的数据
     *
     * @return 未处理的数据
     */
    List<ActivityRewardExceptionRecordModel> selectUnprocessedFenceAreaData(@Param("list") List<Long> originIds);


    /**
     * 查询未处理的开瓶记录id
     *
     * @return List<Long>
     */
    List<Long> selectDealScanIdList();

    List<ActivityRewardExceptionRecordModel> selectNoDealFenceType();
}




