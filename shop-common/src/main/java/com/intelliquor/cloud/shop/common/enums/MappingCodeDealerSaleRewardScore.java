package com.intelliquor.cloud.shop.common.enums;


import lombok.Getter;

/**
 * 经销商动销奖励
 * 合同类型t_contract_type     2-->停用
 *   0	国台主品合同	2
 *   1	国台酱酒合同	2
 *   2	常规渠道经销合同	1
 *   3	国台酱酒经销合同	1
 *   4	专卖店经销合同	1
 *   5	数智体验中心经销合同	1
 *   6	团购特约经销合同	1
 *   7	电商平台经销合同	1
 *   8	商超连锁经销合同	1
 *   9	国台酱酒经销合同（电商）	1
 *   10	国台酱酒金品合同	2
 *   11	团购特约经销合同（非渠道）	2
 *   12	常规渠道经销合同（高端）	1
 *   13	定制酒合同	1
 *   14	封坛酒合同	1
 *   15	国台酱酒经销合同（经典版）	1
 *   16	葡萄酒合同	2
 *   17	大客户特约经销合同	1
 *   18	龙酒联盟体经销合同	1
 *   19	体验馆经销合同	1
 *   20	十五年平台经销合同	1
 *   21	大系统特约经销合同	1
 */
@Getter
public enum MappingCodeDealerSaleRewardScore {
    // 经销商动销奖励-->积分
    DEALER_SALE_REWARD_SCORE_CONVENTION_CHANNEL(2, "YSPZ-JL-001"), // 常规渠道经销合同
    DEALER_SALE_REWARD_SCORE_GUOTAI_SAUCE_WINE(3, "YSPZ-JL-001"), // 国台酱酒经销合同
    DEALER_SALE_REWARD_SCORE_CLASSIC_EDITION(15, "YSPZ-JL-001"), // 国台酱酒经销合同（经典版）
//    DEALER_SALE_REWARD_SCORE_COMMERCE(9, "YSPZ-JL-007"), // 国台酱酒经销合同（电商）
    DEALER_SALE_REWARD_SCORE_COMMERCE_PLATFORM(7, "YSPZ-JL-007"), // 电商平台经销合同
    DEALER_SALE_REWARD_SCORE_SUPERMARKET_CHAIN(8, "YSPZ-JL-012"), // 商超连锁经销合同
    DEALER_SALE_REWARD_SCOR_MULT_CONVENTION_CHANNEL(24, "YSPZ-JL-001"), // 常规渠道综合经销商合同
//    DEALER_SALE_REWARD_SCORE_HOTEL_CHANNEL(26, "YSPZ-JL-011"), // 餐饮渠道经销合同
    DEALER_SALE_REWARD_SCORE_WINE_CHAIN(28, "YSPZ-JL-012"), //酒类连锁经销合同

    DEALER_SALE_REWARD_SCOR_BIG_CUSTOMER(17, "YSPZ-JL-016"), // 大客户特约经销合同
    DEALER_SALE_REWARD_SCOR_BIG_SYSTEM(21, "YSPZ-JL-016"), // 大系统特约经销合同
    DEALER_SALE_REWARD_SCOR_LONGWINE_ALLIANCE(18, "YSPZ-JL-016"), // 龙酒联盟体经销合同
    DEALER_SALE_REWARD_SCOR_MEDICAL_EXAMINATION_CENTER(5, "YSPZ-JL-016"), //  数智体验中心经销合同
    DEALER_SALE_REWARD_SCOR_SEXPERIENCE_HALL(19, "YSPZ-JL-016"), // 体验馆经销合同
    DEALER_SALE_REWARD_SCOR_GROUP_CONTRIBUTING(6, "YSPZ-JL-016"), // 团购特约经销合同
    ;
    private Integer code;
    private String msg;

    MappingCodeDealerSaleRewardScore(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public static String getName(Integer code){
        for (MappingCodeDealerSaleRewardScore state : MappingCodeDealerSaleRewardScore.values()) {
            if (state.getCode().equals(code)){
                return state.getMsg();
            }
        }
        return "";
    }

}
