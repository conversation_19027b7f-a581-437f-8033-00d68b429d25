package com.intelliquor.cloud.shop.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum ProtocolStandardByYearEnum {
    /**
     * 无标准
     */
    NOT_STANDARD(99, "无标准"),
    /**
     * 24年标准
     */
    STANDARD_24(1, "24年及24年后标准"),

    /**
     * 23年标准
     */
    STANDARD_23(0, "23年标准"),
    ;

    private final Integer key;

    private final String value;


    /**
     * 根据key获取value
     */
    public static String getValue(Integer key) {
        for (ProtocolStandardByYearEnum value : values()) {
            if (value.getKey().equals(key)) {
                return value.getValue();
            }
        }
        return null;
    }
}
