package com.intelliquor.cloud.shop.common.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import com.intelliquor.cloud.shop.common.constant.CommonConstant;
import com.intelliquor.cloud.shop.common.constant.RedisConstant;
import com.intelliquor.cloud.shop.common.dao.*;
import com.intelliquor.cloud.shop.common.enums.*;
import com.intelliquor.cloud.shop.common.exception.BusinessException;
import com.intelliquor.cloud.shop.common.model.*;
import com.intelliquor.cloud.shop.common.model.resp.CodeInfoResp;
import com.intelliquor.cloud.shop.common.service.*;
import com.intelliquor.cloud.shop.common.utils.ContractUtils;
import com.intelliquor.cloud.shop.common.utils.DateUtils;
import com.intelliquor.cloud.shop.common.utils.RandomUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.StopWatch;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;


/**
 * 发放经销商奖励
 */
@Slf4j
@Service
public class SendAwardDealerServiceImpl implements SendAwardDealerService {

    @Autowired
    private CloudDealerActivityAppointDealerCommonDao cloudDealerActivityAppointDealerCommonDao;

    @Autowired
    private ContractUtils contractUtils;

    @Autowired
    private DealerInfoCommonDao dealerInfoCommonDao;

    @Autowired
    private CloudDealerRewardRecordDao cloudDealerRewardRecordDao;

    @Autowired
    private ShopDao shopDao;

    @Autowired
    private CloudDealerRewardRecordPrepareMapper cloudDealerRewardRecordPrepareMapper;

    @Autowired
    private IRequestLogService requestLogService;

    @Autowired
    private TerminalScanDetailPlusDao terminalScanDetailPlusDao;

    @Autowired
    private ISystemMessageService systemMessageService;

    @Autowired
    private ShopDealerOrderDetailPlusMapper shopDealerOrderDetailPlusMapper;


    @Autowired
    private IActivityDealerContractRewardConfigCommonService activityDealerContractRewardConfigCommonService;


    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    @Autowired
    private CloudDealerRewardRecordHandleMapper cloudDealerRewardRecordHandleMapper;

    @Autowired
    private CloudDealerRewardRecordPrepareHandleMapper cloudDealerRewardRecordPrepareHandleMapper;

    @Autowired
    private ActivityRewardExceptionRecordDao activityRewardExceptionRecordDao;

    @Autowired
    private ActivityRewardRecordDao activityRewardRecordDao;

    @Autowired
    private TerminalShopContractCommonDao terminalShopContractCommonDao;

    @Autowired
    private ISendRewardCommomService sendRewardCommomService;

    @Autowired
    private TerminalScanBalanceCommonDao terminalScanBalanceCommonDao;

    @Autowired
    private ICommonService commonService;

    @Resource
    private DealerContractRelCommonDao dealerContractRelCommonDao;

    @Resource
    private CloudDealerActivityNewLimitDao cloudDealerActivityNewLimitDao;

    @Resource
    private ITerminalShopCommonService terminalShopCommonService;

    @Resource
    private HotelActivityRewardConfigService hotelActivityRewardConfigService;

    @Resource
    private ICloudDealerActivityNewCommonService cloudDealerActivityNewCommonService;

    /**
     * 发经销商奖励的方法
     *
     * @param codeInfoRespList 瓶为单位的数据集合
     * @param dealerInfo       终端在t_cloud_dealer_info对应的数据 3:分销商 5:终端 4:合伙人
     * @param originType       来源类型 1:按单收货 2:智能布奖 3:经销商出库
     * @param shopInfo         终端店id
     * @param detail           在线订单订单号
     * @param receiptType      收货类型:(0:扫码收货;1:出库收货;2:一键收货;3:客户经理代收)
     * @param handleDataType   是否处理数据请求:(0:收货;1:处理数据)
     */
    @Override
    public void sendAwardDealerByOrderNew(List<CodeInfoResp> codeInfoRespList,
                                          CloudDealerInfoModel dealerInfo,
                                          Integer originType,
                                          ShopModel shopInfo,
                                          TerminalScanDetailModel detail,
                                          Integer receiptType,
                                          Integer handleDataType,
                                          RewardReason rewardReason) {
        String shopType = dealerInfo.getAccountType().toString();
        Date dealerOutTime = codeInfoRespList.get(0).getDealerOutTime();
        String receiptTypeName = "";
        switch (receiptType) {
            case 0:
                receiptTypeName = "【扫码收货】";
                break;
            case 1:
                receiptTypeName = "【出库收货】";
                break;
            case 2:
                receiptTypeName = "【一键收货】";
                break;
            case 3:
                receiptTypeName = "【经理代收】";
                break;
            default:
                receiptTypeName = "【未知】";
                break;
        }
        String reqName = receiptTypeName;
        if (handleDataType == 1) {
            reqName = "【处理数据】" + reqName;
        }
        reqName = reqName.concat("收货经销商积分:单号=" + detail.getReceivedOrderCode() + ";qrcode=" + detail.getQrcode());
        try {
            Integer shopId = shopInfo.getId();
            shopInfo.setShopId(shopInfo.getId());
            String orderCode = detail.getReceivedOrderCode();
            Integer originId = detail.getBalanceId();
            Integer companyId = detail.getCompanyId();
            log.info("我是收货的时候发放经销商奖励的接口的参数=====终端店类型:{},来源id:{},来源类型:{},终端店id:{},码信息:{}", shopType, originId, originType, shopId, JSON.toJSONString(codeInfoRespList));
            ExtendDataBean extendDataBean = new ExtendDataBean();
            extendDataBean.setRoleName("经销商");
            extendDataBean.setGoodsName(detail.getGoodsName());
            extendDataBean.setGoodsCode(detail.getGoodsCode());
            extendDataBean.setQrCode(detail.getQrcode());
            extendDataBean.setNum(detail.getQuantity());
            extendDataBean.setDeliveryName(shopInfo.getName());
            extendDataBean.setDeliveryShopId(detail.getShopId());
            extendDataBean.setScanDetailId(detail.getId());
            String receivedOrderCode = detail.getReceivedOrderCode();
            if (StringUtils.isNotEmpty(receivedOrderCode) && "GH".equals(receivedOrderCode.substring(0, 2))) {
                extendDataBean.setOrderTypeName("线上订单");
            } else {
                extendDataBean.setOrderTypeName("直发订单");
            }
            extendDataBean.setDeliveryDate(detail.getCreateTime());
            TerminalScanBalanceCommonModel terminalScanBalanceModel = terminalScanBalanceCommonDao.getById(detail.getBalanceId());
            extendDataBean.setDeliveryAreaName(terminalScanBalanceModel.getAddress());
            //没有酒的话直接返回
            if (CollectionUtils.isEmpty(codeInfoRespList)) {
                log.info("我终端店入库奖励因为没有产品直接返回啦");
                rewardReason.setIsSave(true);
                rewardReason.setHasDealer(0);
                rewardReason.setDealerDesc("没有产品直接返回啦,不发奖励");
                saveActivityRewardExceptionRecordModel(detail, extendDataBean, "码信息判断", "码信息有误", EventType.DEALER_SALE_REWARD.getCode());
                return;
            }
            //ADD HLQ  判断是否符合条件 20230330  1:dealerOutTime（商品最早经销商的发货时间） 在活动要求发货的时间段内;2:是否只有会员类型的终端参与
            //是否是会员 0-否 1-是
            Integer isMember = shopInfo.getIsMember();

            //根据酒的类型分组
            Map<String, List<CodeInfoResp>> cycleMap = codeInfoRespList.stream().collect(Collectors.groupingBy(CodeInfoResp::getGoodsCode));

            //1.获取发放这个产品的经销商编码
            String dealerCode = codeInfoRespList.get(0).getDealerCode();

            // 获取产品的生产批次，生产日期以及国台出库时间
            extendDataBean.setBatchNo(codeInfoRespList.get(0).getBatchNo());
            extendDataBean.setProductionTime(codeInfoRespList.get(0).getProductionTime());
            extendDataBean.setOutTime(codeInfoRespList.get(0).getOutTime());

            //如果经销商编码为空 说明没有活动 直接返回
            if (StringUtils.isBlank(dealerCode)) {
                log.info("我终端店入库奖励因为没有经销商编码直接返回啦");
                rewardReason.setIsSave(true);
                rewardReason.setHasDealer(0);
                rewardReason.setDealerDesc("没有经销商编码【" + dealerCode + "】直接返回啦,不发奖励");
                rewardReason.setDealerType(7);
                saveActivityRewardExceptionRecordModel(detail, extendDataBean, "经销商编码判断", "经销商编码为空", EventType.DEALER_SALE_REWARD.getCode());
                return;
            }
            //查询经销商信息
            CloudDealerInfoModel dealerInfoModel = dealerInfoCommonDao.selectDealerIdByDealerCode(dealerCode);
            ShopModel shopModel = shopDao.getByDealerCode(dealerCode);
            extendDataBean.setShopName(shopModel.getName());
            extendDataBean.setDealerCode(shopModel.getDealerCode());

            //根据酒的类型遍历
            for (Map.Entry<String, List<CodeInfoResp>> goodGroup : cycleMap.entrySet()) {
                //商品数量
                Integer number = 0;
                for (CodeInfoResp resp : goodGroup.getValue()) {
                    number = number + resp.getNumber();
                }

                //补发奖励时，qrcode保存的盒码，实际收货数据可能不只一瓶
                if (handleDataType == 1) {
                    number = detail.getQuantity();
                }

                //查询收到终端的合同
                LambdaQueryWrapper<TerminalShopContractCommonModel> lqw = Wrappers.lambdaQuery();
                lqw.eq(TerminalShopContractCommonModel::getMemberShopId, shopId);
                lqw.orderByDesc(TerminalShopContractCommonModel::getId);
                lqw.last(" LIMIT 1");
                TerminalShopContractCommonModel contractCommonModel = terminalShopContractCommonDao.selectOne(lqw);
                //判断合同的类型
                if (Objects.isNull(contractCommonModel)) {
                    log.info("终端shopid={}没有合同", shopId);
                    rewardReason.setIsSave(true);
                    rewardReason.setHasDealer(0);
                    rewardReason.setDealerType(8);
                    rewardReason.setDealerDesc("【shop_id=" + shopId + "】终端没有合同,不发奖励");
                    saveActivityRewardExceptionRecordModel(detail, extendDataBean, "终端合同判断", "【shop_id=" + shopId + "】终端没有合同", EventType.DEALER_SALE_REWARD.getCode());
                    return;
                }

                DealerContractRelModel contractInfo = dealerContractRelCommonDao.selectContractInfo(contractCommonModel.getContractCode());
                if (Objects.isNull(contractInfo)) {
                    log.info("终端shopid={}未查询到合同信息,合同编码:{}", shopId, contractCommonModel.getContractCode());
                    rewardReason.setIsSave(true);
                    rewardReason.setHasDealer(0);
                    rewardReason.setDealerType(8);
                    rewardReason.setDealerDesc("【shop_id=" + shopId + "】终端没有合同,不发奖励");
                    saveActivityRewardExceptionRecordModel(detail, extendDataBean, "终端合同判断", "【shop_id=" + shopId + "】终端没有合同", EventType.DEALER_SALE_REWARD.getCode());
                    return;
                }
                extendDataBean.setDealerType(contractInfo.getDealerType());

                //判断重复
                String redisComputeJxsdxRewardIdKey = String.format(RedisConstant.REDIS_COMPUTE_JXSDX_REWARD_ID_KEY, detail.getReceivedOrderCode(), detail.getQrcode(), detail.getId());
                Boolean aBoolean = redisTemplate.opsForValue().setIfAbsent(redisComputeJxsdxRewardIdKey, detail.getReceivedOrderCode(), 1, TimeUnit.MINUTES);
                if (Boolean.FALSE.equals(aBoolean)) {
                    log.info("码qrCode={}重复处理动销奖励", detail.getQrcode());
                    rewardReason.setIsSave(true);
                    rewardReason.setHasDealer(0);
                    rewardReason.setDealerType(8);
                    rewardReason.setDealerDesc("【码qrCode=" + detail.getQrcode() + "】重复处理动销奖励");
                    saveActivityRewardExceptionRecordModel(detail, extendDataBean, "分销商活动判断", "【码qrCode=" + detail.getQrcode() + "】重复处理动销奖励", EventType.DISTRIBUTOR_SALE_REWARD.getCode());
                    return;
                }

                LambdaQueryWrapper<ActivityRewardRecordModel> clqw = Wrappers.lambdaQuery();
                clqw.eq(ActivityRewardRecordModel::getOriginId, detail.getId());
                clqw.eq(ActivityRewardRecordModel::getActivityType, ActivityType.SALE_REWARD.getCode());
                clqw.eq(ActivityRewardRecordModel::getEventType, EventType.DEALER_SALE_REWARD.getCode());
                clqw.eq(ActivityRewardRecordModel::getOriginTable, CommonConstant.T_TERMINAL_SCAN_DETAIL_TABLE);
                clqw.eq(ActivityRewardRecordModel::getIsDelete, 0);
                clqw.last(" LIMIT 1");
                ActivityRewardRecordModel activityReward = activityRewardRecordDao.selectOne(clqw);
                if (Objects.nonNull(activityReward)) {
                    log.info("码qrCode={}已发放经销商奖励", detail.getQrcode());
                    rewardReason.setIsSave(true);
                    rewardReason.setHasDealer(0);
                    rewardReason.setDealerType(8);
                    rewardReason.setDealerDesc("【码qrCode=" + detail.getQrcode() + "】已发放经销商奖励");
                    saveActivityRewardExceptionRecordModel(detail, extendDataBean, "分销商活动判断", "【码qrCode=" + detail.getQrcode() + "】已发放经销商奖励", EventType.DISTRIBUTOR_SALE_REWARD.getCode());
                    return;
                }

                Boolean isHotelTerminal = terminalShopCommonService.isHotelTerminalByMemberShopId(terminalScanBalanceModel.getShopId());
                log.info("memberShopId为[{}]的终端是否为餐饮类型的终端：[{}]", terminalScanBalanceModel.getShopId(), isHotelTerminal);
                if (isHotelTerminal) {
                     // 餐饮渠道2024年控盘分利活动禁用(SZYXPT-1715)
                     log.info("餐厅类型终端shopId={}不参与动销奖励， 合同类型是[{}], 商品编码是[{}], 活动类型是[{}]", shopId, contractInfo.getContractType(), goodGroup.getKey(), ActivityType.SALE_REWARD.getCode());
                     rewardReason.setIsSave(true);
                     rewardReason.setHasDealer(0);
                     rewardReason.setDealerDesc("餐饮类型终端不参与动销奖励， 收货终端id是[" + shopInfo.getId() + "]，合同类型是[" + contractInfo.getContractType() + "], 商品编码是[" + goodGroup.getKey() + "]");
                     rewardReason.setDealerType(7);
                     saveActivityRewardExceptionRecordModel(detail, extendDataBean, "餐饮类型终端不参与动销奖励", "餐饮类型终端不参与动销奖励，合同类型是[" + contractInfo.getContractType() + "], 商品编码是[" + goodGroup.getKey() + "]", EventType.DEALER_SALE_REWARD.getCode());
                     return;
                } else {
                    // 非餐饮类型终端，保持原来的逻辑
                    List<DealerActivityNewCommonModel> selectActivityNewList = Lists.newArrayList();
                    //2.如果经销商编码不为空 去匹配对应的经销商活动 并且倒叙排序查询，按经销商编码查
                    List<DealerActivityNewCommonModel> selectActivityNewListOne =
                            cloudDealerActivityAppointDealerCommonDao.selectDealerActivityNewListByShopInStock(dealerCode, detail.getCreateTime(), goodGroup.getKey());
                    log.info("订单编码为:{}，，，，可参与的活动一有{}条", detail.getOrderCode(), selectActivityNewListOne.size());
                    if (CollectionUtils.isNotEmpty(selectActivityNewListOne)) {
                        String ids = selectActivityNewListOne.stream().map(e -> e.getId() + "").collect(Collectors.joining(","));
                    }
                    if (CollectionUtils.isNotEmpty(selectActivityNewListOne)) {
                        selectActivityNewList.addAll(selectActivityNewListOne);
                    }
                    //2.1 按终端所在的省市区匹配活动
                    if (StringUtils.isNotBlank(shopInfo.getProvince())) {
                        String provinces = shopInfo.getProvince();
                        String city = shopInfo.getCity();
                        String district = shopInfo.getDistrict();
                        //领导要求，4个直辖市特殊处理，只处理省、区
                        if (provinces.equals("北京市") || provinces.equals("上海市") || provinces.equals("天津市") || provinces.equals("重庆市")) {
                            if (StringUtils.isBlank(district)) {
                                district = city;
                            }
                            city = "";
                        }
                        List<DealerActivityNewCommonModel> selectActivityNewListTwo =
                                cloudDealerActivityAppointDealerCommonDao.selectDealerActivityNewListByShopInStockAddress(provinces, city, district, detail.getCreateTime(), goodGroup.getKey());
                        log.info("订单编码为:{}，，，，可参与的活动二有{}条", detail.getOrderCode(), selectActivityNewListTwo.size());
                        if (CollectionUtils.isNotEmpty(selectActivityNewListTwo)) {
                            selectActivityNewList.addAll(selectActivityNewListTwo);
                        }
                    }

                    // TODO: 2024/11/8 在下面增加新维度的筛选条件，如果新维度为null，表示该维度不参与筛选
                    //筛选所有匹配的奖励
                    List<String> activityRewardSubTypeList = ActivityRewardSubTypeEnum.getByParentType(ActivityRewardTypeEnum.DYNAMIC_MARKET).stream().map(ActivityRewardSubTypeEnum::getCode).collect(Collectors.toList());
                    for (String rewardSubType : activityRewardSubTypeList) {
                        List<DealerActivityNewCommonModel> values = selectActivityNewList.stream().filter(activity -> rewardSubType.equals(activity.getRewardSubType())).sorted(Comparator.comparing(DealerActivityNewCommonModel::getId).reversed()).collect(Collectors.toList());
                        if (CollUtil.isEmpty(values)) {
                            log.info("码qrCode={}未匹配到活动", detail.getQrcode());
                            rewardReason.setIsSave(true);
                            rewardReason.setHasDealer(0);
                            rewardReason.setDealerDesc("码【" + detail.getQrcode() + "】未匹配到活动");
                            rewardReason.setDealerType(7);
                            saveActivityRewardExceptionRecordModel(detail, extendDataBean, "经销商编码判断", "码【" + detail.getQrcode() + "】未匹配到活动，奖励子类型为:" + rewardSubType , EventType.DEALER_SALE_REWARD.getCode());
                            continue;
                        }
                        List<DealerActivityNewCommonModel> selectActivityList = new ArrayList<>();
                        for (DealerActivityNewCommonModel dealerActivityNewCommonModel : values) {
                            //如果类型为空直接跳出这次
                            if (StringUtils.isBlank(dealerActivityNewCommonModel.getDistributorType())) {
                                continue;
                            }
                            //厂家发货时间是否开启 0.关闭  1.开启
                            Integer deliveryTimeOpen = dealerActivityNewCommonModel.getDeliveryTimeOpen();
                            if (ObjectUtil.isNotEmpty(deliveryTimeOpen) && deliveryTimeOpen == 1) {
                                Date deliveryTimeStart = dealerActivityNewCommonModel.getDeliveryTimeStart();
                                Date deliveryTimeEnd = dealerActivityNewCommonModel.getDeliveryTimeEnd();
                                if (ObjectUtil.isEmpty(deliveryTimeStart) || ObjectUtil.isEmpty(deliveryTimeEnd)) {
                                    continue;
                                }
                                //通过箱码查询第一次发货时间
                                if (Objects.isNull(dealerOutTime)) {
                                    continue;
                                }
                                if (!DateUtils.isEffectiveDate(dealerOutTime, dealerActivityNewCommonModel.getDeliveryTimeStart(), dealerActivityNewCommonModel.getDeliveryTimeEnd())) {
                                    continue;
                                }
                            }
                            if (!dealerInfo.getAccountType().equals(5)) {
                                continue;
                            }
                            //如果有合同 也有分销对象类型 劈开看看能不能匹配   3：分销商, 5:终端，4:合伙人 6会员
                            //最新需求  distributorType 只会是5,6
                            String[] distributorType = dealerActivityNewCommonModel.getDistributorType().split(",");
                            //遍历匹配
                            boolean isCondition = false;
                            if (ArrayUtil.contains(distributorType, "6") && (shopInfo.getIsMember() == 1 || shopInfo.getIsMember() == 2)) { //会员
                                isCondition = true;
                            } else if (ArrayUtil.contains(distributorType, "5") && isMember == 0) { // 终端
                                isCondition = true;
                            }
                            if (!isCondition) {
                                continue;
                            }
                            //限制经销商开瓶奖励规则
                            if (dealerActivityNewCommonModel.getIsUseLimit().equals(1)) {
                                List<CloudDealerActivityNewLimitModel> limitList = cloudDealerActivityNewLimitDao.selectSourceRuleByActivityId(dealerActivityNewCommonModel.getId());
                                if (CollectionUtils.isNotEmpty(limitList)) {
                                    //合同类型限制
                                    List<CloudDealerActivityNewLimitModel> contractTypeLimit = limitList.stream().filter(limit ->
                                                    limit.getType().equals(DealerActivityLimitEnum.LIMIT_CONTRACT_TYPE.getCode()))
                                            .collect(Collectors.toList());
                                    if (CollectionUtils.isNotEmpty(contractTypeLimit)) {
                                        List<String> contractTypeList = new ArrayList<>();
                                        contractTypeLimit.forEach(limit -> contractTypeList.addAll(JSON.parseArray(limit.getRangeJson(), String.class)));
                                        if (contractTypeList.contains(contractCommonModel.getContractType().toString())) {
                                            continue;
                                        }
                                    }
                                    //经销商类型限制
                                    List<CloudDealerActivityNewLimitModel> dealerTypeLimit = limitList.stream().filter(limit ->
                                                    limit.getType().equals(DealerActivityLimitEnum.LIMIT_DEALER_TYPE.getCode()))
                                            .collect(Collectors.toList());
                                    if (CollectionUtils.isNotEmpty(dealerTypeLimit)) {
                                        List<String> dealerTypeList = new ArrayList<>();
                                        dealerTypeLimit.forEach(limit -> dealerTypeList.addAll(JSON.parseArray(limit.getRangeJson(), String.class)));
                                        if (dealerTypeList.contains(contractInfo.getDealerType())) {
                                            continue;
                                        }
                                    }
                                }
                            }

                            if (!cloudDealerActivityNewCommonService.isSatisfiedExtraCondition(detail, dealerActivityNewCommonModel, extendDataBean)) {
                                continue;
                            }

                            selectActivityList.add(dealerActivityNewCommonModel);
                        }
                        if (CollectionUtils.isEmpty(selectActivityList)) {
                            log.info("码qrCode={}未匹配到活动", detail.getQrcode());
                            rewardReason.setIsSave(true);
                            rewardReason.setHasDealer(0);
                            rewardReason.setDealerDesc("码【" + detail.getQrcode() + "】未匹配到活动");
                            rewardReason.setDealerType(7);
                            saveActivityRewardExceptionRecordModel(detail, extendDataBean, "经销商编码判断", "码【" + detail.getQrcode() + "】未匹配到活动，奖励子类型为:" + rewardSubType, EventType.DEALER_SALE_REWARD.getCode());
                            return;
                        }
                        //筛选出开启了奖励分配的活动 (优先匹配有分配奖励的活动)
                        List<DealerActivityNewCommonModel> asignActivityList = new ArrayList<>();
                        for (DealerActivityNewCommonModel dealerActivityNewCommonModel : selectActivityList) {
                            if (!dealerActivityNewCommonModel.getDistributorRewardAssignWhetherOpen().equals(1)) {
                                continue;
                            }
                            if (Objects.isNull(activityDealerContractRewardConfigCommonService
                                    .getActivityDealerContractRewardConfigByDealerInfo(
                                            dealerActivityNewCommonModel, dealerInfo, 1, detail.getReceivedOrderCode()))) {
                                continue;
                            }
                            asignActivityList.add(dealerActivityNewCommonModel);
                        }
                        //优先匹配分销商分配奖励
                        if (CollectionUtils.isNotEmpty(asignActivityList)) {
                            selectActivityList = asignActivityList;
                        }
                        //新集合按id排序
                        selectActivityList = selectActivityList.stream().sorted(Comparator.comparing(DealerActivityNewCommonModel::getId).reversed()).collect(Collectors.toList());
                        //遍历查出来的所有经销商活动
                        out:
                        for (DealerActivityNewCommonModel activityNew : selectActivityList) {
                            String msg = "";
                            ActivityDealerContractRewardConfigCommonModel rewardConfig = activityDealerContractRewardConfigCommonService.getActivityDealerContractRewardConfigByDealerInfo(activityNew, dealerInfo, 1, detail.getReceivedOrderCode());
                            //计算集合的数量 乘等于
                            BigDecimal sumAwardVirtualAmount;
                            BigDecimal oneScore;
                            if (Objects.isNull(rewardConfig) || !activityNew.getDistributorRewardAssignWhetherOpen().equals(1)) {
                                oneScore = activityNew.getAwardMoney();
                                sumAwardVirtualAmount = activityNew.getAwardMoney().multiply(new BigDecimal(number));
                            } else {
                                oneScore = rewardConfig.getDealerReward();
                                sumAwardVirtualAmount = rewardConfig.getDealerReward().multiply(new BigDecimal(number));
                            }
                            reqName = reqName.concat(">积分:" + sumAwardVirtualAmount);
                            log.info("订单编码为:{}，，，，奖励金额：{}", detail.getOrderCode(), sumAwardVirtualAmount);
                            extendDataBean.setActivityName(activityNew.getActivityName());
                            extendDataBean.setRewardSubType(rewardSubType);
                            extendDataBean.setAllowRewardOrder(activityNew.getRewardOrderFlag());
                            //判断其他规则
                            Map<String, Object> rtnMap = sendRewardCommomService.judgmentOtherRule(shopInfo, codeInfoRespList.get(0), 0, extendDataBean);
                            if (Objects.isNull(rtnMap)) {
                                ActivityRewardRecordModel recordModel = new ActivityRewardRecordModel();
                                recordModel.setShopId(shopModel.getId());
                                recordModel.setAccountType(shopModel.getAccountType());
                                recordModel.setActivityType(ActivityType.SALE_REWARD.getCode());
                                recordModel.setEventType(EventType.DEALER_SALE_REWARD.getCode());
                                recordModel.setOriginTable(CommonConstant.T_TERMINAL_SCAN_DETAIL_TABLE);
                                recordModel.setOriginId(detail.getId());
                                recordModel.setType(1);
                                recordModel.setActivityId(activityNew.getId());
                                recordModel.setActivityVersion(activityNew.getVersionId());
                                recordModel.setRewardType(RewardType.SEND_SCORE_TYPE.getCode());
                                recordModel.setCompanyId(detail.getCompanyId());
                                recordModel.setIntegral(sumAwardVirtualAmount);
                                recordModel.setSendStatus(SendStatus.WAIT_TYPE.getCode());
                                recordModel.setCode(detail.getQrcode());
                                recordModel.setOrderCode(detail.getReceivedOrderCode());
                                recordModel.setIsMember("0");
                                extendDataBean.setUnitAmount(oneScore);
                                extendDataBean.setRewardContent(sumAwardVirtualAmount + "");
                                recordModel.setExtendData(JSONObject.toJSONString(extendDataBean));
                                String remark = receiptTypeName + "动销奖励-箱码-" + detail.getCodeXiang() + "-" + detail.getGoodsName();
                                if (!detail.getQrcode().startsWith("1")) {
                                    remark = receiptTypeName + "动销奖励-盒码-" + detail.getQrcode() + "-" + detail.getGoodsName();
                                }
                                recordModel.setSceneRemark(remark);
                                //经谈论取终端的合同 合同可能过期经销商经理无法发奖励
                                recordModel.setContractCode(contractCommonModel.getContractCode());
                                recordModel.setContractType(contractCommonModel.getContractType());
                                recordModel.setBusinessId(RandomUtils.getActivityRewardRecordBusinessId());
                                activityRewardRecordDao.insert(recordModel);
                            } else {
                                String rtnMsg = rtnMap.get("msg").toString();
                                NoSendRewardType noSendRewardType = (NoSendRewardType) rtnMap.get("type");
                                ActivityRewardExceptionRecordModel exceptionRecordModel = new ActivityRewardExceptionRecordModel();
                                exceptionRecordModel.setShopId(shopModel.getId());
                                exceptionRecordModel.setAccountType(shopModel.getAccountType());
                                exceptionRecordModel.setActivityType(ActivityType.SALE_REWARD.getCode());
                                exceptionRecordModel.setEventType(EventType.DEALER_SALE_REWARD.getCode());
                                exceptionRecordModel.setOriginTable(CommonConstant.T_TERMINAL_SCAN_DETAIL_TABLE);
                                exceptionRecordModel.setOriginId(detail.getId());
                                exceptionRecordModel.setType(1);
                                exceptionRecordModel.setActivityId(activityNew.getId());
                                exceptionRecordModel.setActivityVersion(activityNew.getVersionId());
                                exceptionRecordModel.setRewardType(RewardType.SEND_SCORE_TYPE.getCode());
                                exceptionRecordModel.setSceneRemark(noSendRewardType.getMsg());
                                exceptionRecordModel.setCompanyId(detail.getCompanyId());
                                exceptionRecordModel.setIntegral(sumAwardVirtualAmount);
                                exceptionRecordModel.setSendMsg(NoSendRewardType.getName(noSendRewardType.getCode()) + ":" + rtnMsg);
                                exceptionRecordModel.setExceptionType(noSendRewardType.getCode());
                                exceptionRecordModel.setCode(detail.getQrcode());
                                exceptionRecordModel.setOrderCode(detail.getReceivedOrderCode());
                                exceptionRecordModel.setIsMember(0);
                                extendDataBean.setUnitAmount(oneScore);
                                extendDataBean.setRewardContent(sumAwardVirtualAmount + "");
                                exceptionRecordModel.setExtendData(JSONObject.toJSONString(extendDataBean));
                                exceptionRecordModel.setContractCode(contractCommonModel.getContractCode());
                                exceptionRecordModel.setContractType(contractCommonModel.getContractType());
                                exceptionRecordModel.setScanTime(detail.getCreateTime());
                                activityRewardExceptionRecordDao.insert(exceptionRecordModel);
                                rewardReason.setIsSave(true);
                                rewardReason.setHasDealer(0);
                                rewardReason.setDealerType(6);
                                rewardReason.setDealerDesc(noSendRewardType.name());
                            }
                            //循环执行的  可能其他活动能匹配上
                            rewardReason.setHasDealer(null);
                            rewardReason.setDealerType(null);
                            //是否发给分销商/合伙人
                            ExtendDataBean distributorExtendDataBean = new ExtendDataBean();
                            BeanUtils.copyProperties(extendDataBean, distributorExtendDataBean);
                            distributorExtendDataBean.setRoleName("分销商");
                            if (Objects.nonNull(rewardConfig) && activityNew.getDistributorRewardAssignWhetherOpen().equals(1)) {
                                ShopModel distributor = shopDao.getByDealerCode(rewardConfig.getDistributorDealerCode());
                                distributorExtendDataBean.setShopName(distributor.getName());
                                distributorExtendDataBean.setDealerCode(distributor.getDealerCode());
                                BigDecimal distributorAmount = rewardConfig.getDistributorReward().multiply(new BigDecimal(number));
                                String contractCode = commonService.getContractCodeByShopId(distributor.getId());
                                //判断其他规则
                                Map<String, Object> rtnDistributorMap = sendRewardCommomService.judgmentOtherRule(shopInfo, codeInfoRespList.get(0), 2, extendDataBean);
                                if (Objects.isNull(rtnDistributorMap)) {
                                    ActivityRewardRecordModel record = new ActivityRewardRecordModel();
                                    record.setShopId(distributor.getId());
                                    record.setAccountType(distributor.getAccountType());
                                    record.setActivityType(ActivityType.SALE_REWARD.getCode());
                                    record.setEventType(EventType.DISTRIBUTOR_SALE_REWARD.getCode());
                                    record.setOriginTable(CommonConstant.T_TERMINAL_SCAN_DETAIL_TABLE);
                                    record.setOriginId(detail.getId());
                                    record.setType(1);
                                    record.setActivityId(activityNew.getId());
                                    record.setActivityVersion(activityNew.getVersionId());
                                    record.setRewardType(RewardType.SEND_SCORE_TYPE.getCode());
                                    record.setCompanyId(detail.getCompanyId());
                                    record.setIntegral(distributorAmount);
                                    record.setSendStatus(SendStatus.WAIT_TYPE.getCode());
                                    record.setCode(detail.getQrcode());
                                    record.setOrderCode(detail.getReceivedOrderCode());
                                    record.setIsMember("0");
                                    record.setContractCode(contractCode);
                                    record.setContractType(commonService.getContractTypeByContractCode(contractCode));
                                    String remark = receiptTypeName + "动销奖励-箱码-" + detail.getCodeXiang() + "-" + detail.getGoodsName();
                                    if (!detail.getQrcode().startsWith("1")) {
                                        remark = receiptTypeName + "动销奖励-盒码-" + detail.getQrcode() + "-" + detail.getGoodsName();
                                    }
                                    record.setSceneRemark(remark);
                                    extendDataBean.setRewardContent(distributorAmount + "");
                                    extendDataBean.setUnitAmount(rewardConfig.getDistributorReward());
                                    record.setExtendData(JSONObject.toJSONString(extendDataBean));
                                    record.setBusinessId(RandomUtils.getActivityRewardRecordBusinessId());
                                    activityRewardRecordDao.insert(record);
                                } else {
                                    String rtnMsg = rtnDistributorMap.get("msg").toString();
                                    NoSendRewardType noSendRewardType = (NoSendRewardType) rtnDistributorMap.get("type");
                                    ActivityRewardExceptionRecordModel exceptionRecordModel = new ActivityRewardExceptionRecordModel();
                                    exceptionRecordModel.setShopId(distributor.getId());
                                    exceptionRecordModel.setAccountType(distributor.getAccountType());
                                    exceptionRecordModel.setActivityType(ActivityType.SALE_REWARD.getCode());
                                    exceptionRecordModel.setEventType(EventType.DISTRIBUTOR_SALE_REWARD.getCode());
                                    exceptionRecordModel.setOriginTable(CommonConstant.T_TERMINAL_SCAN_DETAIL_TABLE);
                                    exceptionRecordModel.setOriginId(detail.getId());
                                    exceptionRecordModel.setType(1);
                                    exceptionRecordModel.setActivityId(activityNew.getId());
                                    exceptionRecordModel.setActivityVersion(activityNew.getVersionId());
                                    exceptionRecordModel.setRewardType(RewardType.SEND_SCORE_TYPE.getCode());
                                    exceptionRecordModel.setCompanyId(detail.getCompanyId());
                                    exceptionRecordModel.setIntegral(distributorAmount);
                                    exceptionRecordModel.setSendMsg(NoSendRewardType.getName(noSendRewardType.getCode()) + ":" + rtnMsg);
                                    exceptionRecordModel.setExceptionType(noSendRewardType.getCode());
                                    exceptionRecordModel.setCode(detail.getQrcode());
                                    exceptionRecordModel.setOrderCode(detail.getReceivedOrderCode());
                                    exceptionRecordModel.setIsMember(0);
                                    extendDataBean.setRewardContent(distributorAmount + "");
                                    extendDataBean.setUnitAmount(rewardConfig.getDistributorReward());
                                    exceptionRecordModel.setExtendData(JSONObject.toJSONString(extendDataBean));
                                    exceptionRecordModel.setContractCode(contractCode);
                                    exceptionRecordModel.setContractType(commonService.getContractTypeByContractCode(contractCode));
                                    exceptionRecordModel.setSceneRemark(noSendRewardType.getMsg());
                                    exceptionRecordModel.setScanTime(detail.getCreateTime());
                                    activityRewardExceptionRecordDao.insert(exceptionRecordModel);
                                    rewardReason.setIsSave(true);
                                    rewardReason.setHasDistributor(0);
                                    rewardReason.setDistributorType(6);
                                    rewardReason.setDistributorDesc(noSendRewardType.name());
                                }
                                rewardReason.setHasDistributor(null);
                                rewardReason.setDistributorType(null);
                            } else {
                                rewardReason.setIsSave(true);
                                rewardReason.setHasDistributor(0);
                                msg = "活动:" + activityNew.getId() + "无高级配置,不发放奖励";
                                rewardReason.setDistributorType(0);
                                rewardReason.setDistributorDesc(StringUtils.isBlank(rewardReason.getDistributorDesc()) ? msg : rewardReason.getDistributorDesc() + ">" + msg);
                                distributorExtendDataBean.setShopName("");
                                distributorExtendDataBean.setDealerCode("");
                                saveActivityRewardExceptionRecordModel(detail, distributorExtendDataBean, "分销商活动判断", "没有匹配到分销商的活动", EventType.DISTRIBUTOR_SALE_REWARD.getCode());
                            }
                            //发放完奖励直接跳出到最外层
                            break out;
                        }
                    }
                }
            }
        } catch (Exception e) {
            rewardReason.setRemark("经销商发奖励时报错" + e.getMessage());
            log.error("收货时给经销商发奖励时报错:{}", e.getMessage(), e);
            throw new BusinessException("发经销商奖励失败:" + e.getMessage());
        }
    }

    private ActivityRewardRecordModel getHotelTypeSaleReward(TerminalScanDetailModel detail, ShopModel shopModel, HotelActivityRewardConfigModel hotelActivityRewardConfigModel, BigDecimal sumAwardVirtualAmount, ExtendDataBean extendDataBean, String receiptTypeName, DealerContractRelModel contractInfo) {
        ActivityRewardRecordModel recordModel = new ActivityRewardRecordModel();
        recordModel.setShopId(shopModel.getId());
        recordModel.setAccountType(shopModel.getAccountType());
        recordModel.setActivityType(ActivityType.SALE_REWARD.getCode());
        recordModel.setEventType(EventType.DEALER_SALE_REWARD.getCode());
        recordModel.setOriginTable(CommonConstant.T_TERMINAL_SCAN_DETAIL_TABLE);
        recordModel.setOriginId(detail.getId());
        recordModel.setType(1);
        recordModel.setActivityId(0);
        recordModel.setActivityVersion(0);
        recordModel.setRewardType(RewardType.SEND_SCORE_TYPE.getCode());
        recordModel.setCompanyId(detail.getCompanyId());
        recordModel.setIntegral(sumAwardVirtualAmount);
        recordModel.setSendStatus(SendStatus.WAIT_TYPE.getCode());
        recordModel.setCode(detail.getQrcode());
        recordModel.setOrderCode(detail.getReceivedOrderCode());
        recordModel.setIsMember("0");
        extendDataBean.setUnitAmount(hotelActivityRewardConfigModel.getReward());
        extendDataBean.setRewardContent(sumAwardVirtualAmount + "");
        extendDataBean.setActivityName("餐饮类型终端动销奖励_hotel_activity_reward_config_" + hotelActivityRewardConfigModel.getId());
        extendDataBean.setRewardSubType(ActivityRewardSubTypeEnum.OPEN_BOTTLE_NORMAL_REWARD.getCode());
        extendDataBean.setAllowRewardOrder(RewardOrderAllowedEnum.NOT_ALLOWED.getCode());
        recordModel.setExtendData(JSONObject.toJSONString(extendDataBean));
        String remark = receiptTypeName + "动销奖励-箱码-" + detail.getCodeXiang() + "-" + detail.getGoodsName();
        if (!detail.getQrcode().startsWith("1")) {
            remark = receiptTypeName + "动销奖励-盒码-" + detail.getQrcode() + "-" + detail.getGoodsName();
        }
        recordModel.setSceneRemark(remark);
        recordModel.setContractCode(contractInfo.getContractCode());
        recordModel.setContractType(contractInfo.getContractType());
        recordModel.setBusinessId(RandomUtils.getActivityRewardRecordBusinessId());
        return recordModel;
    }


    @Override
    public void sendAwardDealerByOrderNewTemp(List<CodeInfoResp> codeInfoRespList,
                                          CloudDealerInfoModel dealerInfo,
                                          Integer originType,
                                          ShopModel shopInfo,
                                          TerminalScanDetailModel detail,
                                          Integer receiptType,
                                          Integer handleDataType) {
        String shopType = dealerInfo.getAccountType().toString();
        Date dealerOutTime = codeInfoRespList.get(0).getDealerOutTime();
        String receiptTypeName = "";
        switch (receiptType) {
            case 0:
                receiptTypeName = "【扫码收货】";
                break;
            case 1:
                receiptTypeName = "【出库收货】";
                break;
            case 2:
                receiptTypeName = "【一键收货】";
                break;
            case 3:
                receiptTypeName = "【经理代收】";
                break;
            default:
                receiptTypeName = "【未知】";
                break;
        }
        String reqName = receiptTypeName;
        if (handleDataType == 1) {
            reqName = "【处理数据】" + reqName;
        }
        reqName = reqName.concat("收货经销商积分:单号=" + detail.getReceivedOrderCode() + ";qrcode=" + detail.getQrcode());
        try {
            Integer shopId = shopInfo.getId();
            shopInfo.setShopId(shopInfo.getId());
            String orderCode = detail.getReceivedOrderCode();
            Integer originId = detail.getBalanceId();
            Integer companyId = detail.getCompanyId();
            log.info("我是收货的时候发放经销商奖励的接口的参数=====终端店类型:{},来源id:{},来源类型:{},终端店id:{},码信息:{}", shopType, originId, originType, shopId, JSON.toJSONString(codeInfoRespList));
            ExtendDataBean extendDataBean = new ExtendDataBean();
            extendDataBean.setRoleName("经销商");
            extendDataBean.setGoodsName(detail.getGoodsName());
            extendDataBean.setGoodsCode(detail.getGoodsCode());
            extendDataBean.setQrCode(detail.getQrcode());
            extendDataBean.setNum(detail.getQuantity());
            extendDataBean.setDeliveryName(shopInfo.getName());
            extendDataBean.setDeliveryShopId(detail.getShopId());
            extendDataBean.setScanDetailId(detail.getId());
            String receivedOrderCode = detail.getReceivedOrderCode();
            if (StringUtils.isNotEmpty(receivedOrderCode) && "GH".equals(receivedOrderCode.substring(0, 2))) {
                extendDataBean.setOrderTypeName("线上订单");
            } else {
                extendDataBean.setOrderTypeName("直发订单");
            }
            extendDataBean.setDeliveryDate(detail.getCreateTime());
            TerminalScanBalanceCommonModel terminalScanBalanceModel = terminalScanBalanceCommonDao.getById(detail.getBalanceId());
            extendDataBean.setDeliveryAreaName(terminalScanBalanceModel.getAddress());
            //没有酒的话直接返回
            if (CollectionUtils.isEmpty(codeInfoRespList)) {
                log.info("我终端店入库奖励因为没有产品直接返回啦");
                saveActivityRewardExceptionRecordModel(detail, extendDataBean, "码信息判断[2023-05-31]", "码信息有误", EventType.DEALER_SALE_REWARD.getCode());
                return;
            }
            //ADD HLQ  判断是否符合条件 20230330  1:dealerOutTime（商品最早经销商的发货时间） 在活动要求发货的时间段内;2:是否只有会员类型的终端参与
            //是否是会员 0-否 1-是
            Integer isMember = shopInfo.getIsMember();

            //根据酒的类型分组
            Map<String, List<CodeInfoResp>> cycleMap = codeInfoRespList.stream().collect(Collectors.groupingBy(CodeInfoResp::getGoodsCode));

            //1.获取发放这个产品的经销商编码
            String dealerCode = codeInfoRespList.get(0).getDealerCode();

            // 获取产品的生产批次，生产日期以及国台出库时间
            extendDataBean.setBatchNo(codeInfoRespList.get(0).getBatchNo());
            extendDataBean.setProductionTime(codeInfoRespList.get(0).getProductionTime());
            extendDataBean.setOutTime(codeInfoRespList.get(0).getOutTime());

            //如果经销商编码为空 说明没有活动 直接返回
            if (StringUtils.isBlank(dealerCode)) {
                log.info("我终端店入库奖励因为没有经销商编码直接返回啦");
                saveActivityRewardExceptionRecordModel(detail, extendDataBean, "经销商编码判断[2023-05-31]", "经销商编码为空", EventType.DEALER_SALE_REWARD.getCode());
                return;
            }
            //查询经销商信息
            CloudDealerInfoModel dealerInfoModel = dealerInfoCommonDao.selectDealerIdByDealerCode(dealerCode);
            ShopModel shopModel = shopDao.getByDealerCode(dealerCode);
            extendDataBean.setShopName(shopModel.getName());
            extendDataBean.setDealerCode(shopModel.getDealerCode());

            //根据酒的类型遍历
            for (Map.Entry<String, List<CodeInfoResp>> goodGroup : cycleMap.entrySet()) {
                //商品数量
                Integer number = 0;
                for (CodeInfoResp resp : goodGroup.getValue()) {
                    number = number + resp.getNumber();
                }

                //补发奖励时，qrcode保存的盒码，实际收货数据可能不只一瓶
                if (handleDataType == 1) {
                    number = detail.getQuantity();
                }

                //查询收到终端的合同
                LambdaQueryWrapper<TerminalShopContractCommonModel> lqw = Wrappers.lambdaQuery();
                lqw.eq(TerminalShopContractCommonModel::getMemberShopId, shopId);
                lqw.orderByDesc(TerminalShopContractCommonModel::getId);
                lqw.last(" LIMIT 1");
                TerminalShopContractCommonModel contractCommonModel = terminalShopContractCommonDao.selectOne(lqw);
                //判断合同的类型
                if (Objects.isNull(contractCommonModel)) {
                    log.info("终端shopid={}没有合同", shopId);
                    saveActivityRewardExceptionRecordModel(detail, extendDataBean, "终端合同判断", "【shop_id=" + shopId + "】终端没有合同", EventType.DEALER_SALE_REWARD.getCode());
                    return;
                }

                DealerContractRelModel contractInfo = dealerContractRelCommonDao.selectContractInfo(contractCommonModel.getContractCode());
                if (Objects.isNull(contractInfo)) {
                    log.info("终端shopid={}未查询到合同信息,合同编码:{}", shopId, contractCommonModel.getContractCode());
                    saveActivityRewardExceptionRecordModel(detail, extendDataBean, "终端合同判断", "【shop_id=" + shopId + "】终端没有合同", EventType.DEALER_SALE_REWARD.getCode());
                    return;
                }
                extendDataBean.setDealerType(contractInfo.getDealerType());

                //判断重复
                String redisComputeJxsdxRewardIdKey = String.format(RedisConstant.REDIS_COMPUTE_JXSDX_REWARD_ID_KEY, detail.getReceivedOrderCode(), detail.getQrcode(), detail.getId());
                Boolean aBoolean = redisTemplate.opsForValue().setIfAbsent(redisComputeJxsdxRewardIdKey, detail.getReceivedOrderCode(), 1, TimeUnit.MINUTES);
                if (Boolean.FALSE.equals(aBoolean)) {
                    log.info("码qrCode={}重复处理动销奖励", detail.getQrcode());
                    saveActivityRewardExceptionRecordModel(detail, extendDataBean, "分销商活动判断", "【码qrCode=" + detail.getQrcode() + "】重复处理动销奖励", EventType.DISTRIBUTOR_SALE_REWARD.getCode());
                    return;
                }

                LambdaQueryWrapper<ActivityRewardRecordModel> clqw = Wrappers.lambdaQuery();
                clqw.eq(ActivityRewardRecordModel::getOriginId, detail.getId());
                clqw.eq(ActivityRewardRecordModel::getActivityType, ActivityType.SALE_REWARD.getCode());
                clqw.eq(ActivityRewardRecordModel::getEventType, EventType.DEALER_SALE_REWARD.getCode());
                clqw.eq(ActivityRewardRecordModel::getOriginTable, CommonConstant.T_TERMINAL_SCAN_DETAIL_TABLE);
                clqw.eq(ActivityRewardRecordModel::getIsDelete, 0);
                clqw.last(" LIMIT 1");
                ActivityRewardRecordModel activityReward = activityRewardRecordDao.selectOne(clqw);
                if (Objects.nonNull(activityReward)) {
                    log.info("码qrCode={}已发放经销商奖励", detail.getQrcode());
                    saveActivityRewardExceptionRecordModel(detail, extendDataBean, "分销商活动判断", "【码qrCode=" + detail.getQrcode() + "】已发放经销商奖励", EventType.DISTRIBUTOR_SALE_REWARD.getCode());
                    return;
                }

                Boolean isHotelTerminal = terminalShopCommonService.isHotelTerminalByMemberShopId(terminalScanBalanceModel.getShopId());
                log.info("memberShopId为[{}]的终端是否为餐饮类型的终端：[{}]", terminalScanBalanceModel.getShopId(), isHotelTerminal);
                if (isHotelTerminal) {
                    // 餐饮渠道2024年控盘分利活动禁用(SZYXPT-1715)
                    log.info("餐厅类型终端shopId={}不参与动销奖励， 合同类型是[{}], 商品编码是[{}], 活动类型是[{}]", shopId, contractInfo.getContractType(), goodGroup.getKey(), ActivityType.SALE_REWARD.getCode());
                    saveActivityRewardExceptionRecordModel(detail, extendDataBean, "餐饮类型终端不参与动销奖励", "餐饮类型终端不参与动销奖励，合同类型是[" + contractInfo.getContractType() + "], 商品编码是[" + goodGroup.getKey() + "]", EventType.DEALER_SALE_REWARD.getCode());
                    return;
                } else {
                    // 非餐饮类型终端，保持原来的逻辑
                    List<DealerActivityNewCommonModel> selectActivityNewList = Lists.newArrayList();
                    //2.如果经销商编码不为空 去匹配对应的经销商活动 并且倒叙排序查询，按经销商编码查
                    List<DealerActivityNewCommonModel> selectActivityNewListOne =
                            cloudDealerActivityAppointDealerCommonDao.selectDealerActivityNewListByShopInStock(dealerCode, new Date(), goodGroup.getKey());
                    log.info("订单编码为:{}，，，，可参与的活动一有{}条", detail.getOrderCode(), selectActivityNewListOne.size());
                    if (CollectionUtils.isNotEmpty(selectActivityNewListOne)) {
                        String ids = selectActivityNewListOne.stream().map(e -> e.getId() + "").collect(Collectors.joining(","));
                    }
                    if (CollectionUtils.isNotEmpty(selectActivityNewListOne)) {
                        selectActivityNewList.addAll(selectActivityNewListOne);
                    }
                    //2.1 按终端所在的省市区匹配活动
                    if (StringUtils.isNotBlank(shopInfo.getProvince())) {
                        String provinces = shopInfo.getProvince();
                        String city = shopInfo.getCity();
                        String district = shopInfo.getDistrict();
                        //领导要求，4个直辖市特殊处理，只处理省、区
                        if (provinces.equals("北京市") || provinces.equals("上海市") || provinces.equals("天津市") || provinces.equals("重庆市")) {
                            if (StringUtils.isBlank(district)) {
                                district = city;
                            }
                            city = "";
                        }
                        List<DealerActivityNewCommonModel> selectActivityNewListTwo =
                                cloudDealerActivityAppointDealerCommonDao.selectDealerActivityNewListByShopInStockAddress(provinces, city, district, new Date(), goodGroup.getKey());
                        log.info("订单编码为:{}，，，，可参与的活动二有{}条", detail.getOrderCode(), selectActivityNewListTwo.size());
                        if (CollectionUtils.isNotEmpty(selectActivityNewListTwo)) {
                            selectActivityNewList.addAll(selectActivityNewListTwo);
                        }
                    }
                    if (CollectionUtils.isEmpty(selectActivityNewList)) {
                        log.info("码qrCode={}未匹配到活动", detail.getQrcode());
                        saveActivityRewardExceptionRecordModel(detail, extendDataBean, "经销商编码判断[2023-05-31]", "码【" + detail.getQrcode() + "】未匹配到活动", EventType.DEALER_SALE_REWARD.getCode());
                        return;
                    }

                    //筛选所有匹配的奖励
                    List<String> activityRewardSubTypeList = ActivityRewardSubTypeEnum.getByParentType(ActivityRewardTypeEnum.DYNAMIC_MARKET).stream().map(ActivityRewardSubTypeEnum::getCode).collect(Collectors.toList());
                    for (String rewardSubType : activityRewardSubTypeList) {
                        List<DealerActivityNewCommonModel> values = selectActivityNewList.stream().filter(activity -> rewardSubType.equals(activity.getRewardSubType())).sorted(Comparator.comparing(DealerActivityNewCommonModel::getId).reversed()).collect(Collectors.toList());
                        if (CollUtil.isEmpty(values)) {
                            log.info("码qrCode={}未匹配到活动", detail.getQrcode());
                            saveActivityRewardExceptionRecordModel(detail, extendDataBean, "经销商编码判断", "码【" + detail.getQrcode() + "】未匹配到活动", EventType.DEALER_SALE_REWARD.getCode());
                            continue;
                        }
                        List<DealerActivityNewCommonModel> selectActivityList = new ArrayList<>();
                        for (DealerActivityNewCommonModel dealerActivityNewCommonModel : values) {
                            //如果类型为空直接跳出这次
                            if (StringUtils.isBlank(dealerActivityNewCommonModel.getDistributorType())) {
                                continue;
                            }
                            //厂家发货时间是否开启 0.关闭  1.开启
                            Integer deliveryTimeOpen = dealerActivityNewCommonModel.getDeliveryTimeOpen();
                            if (ObjectUtil.isNotEmpty(deliveryTimeOpen) && deliveryTimeOpen == 1) {
                                Date deliveryTimeStart = dealerActivityNewCommonModel.getDeliveryTimeStart();
                                Date deliveryTimeEnd = dealerActivityNewCommonModel.getDeliveryTimeEnd();
                                if (ObjectUtil.isEmpty(deliveryTimeStart) || ObjectUtil.isEmpty(deliveryTimeEnd)) {
                                    continue;
                                }
                                //通过箱码查询第一次发货时间
                                if (Objects.isNull(dealerOutTime)) {
                                    continue;
                                }
                                if (!DateUtils.isEffectiveDate(dealerOutTime, dealerActivityNewCommonModel.getDeliveryTimeStart(), dealerActivityNewCommonModel.getDeliveryTimeEnd())) {
                                    continue;
                                }
                            }
                            if (!dealerInfo.getAccountType().equals(5)) {
                                continue;
                            }
                            //如果有合同 也有分销对象类型 劈开看看能不能匹配   3：分销商, 5:终端，4:合伙人 6会员
                            //最新需求  distributorType 只会是5,6
                            String[] distributorType = dealerActivityNewCommonModel.getDistributorType().split(",");
                            //遍历匹配
                            boolean isCondition = false;
                            if (ArrayUtil.contains(distributorType, "6") && (shopInfo.getIsMember() == 1 || shopInfo.getIsMember() == 2)) { //会员
                                isCondition = true;
                            } else if (ArrayUtil.contains(distributorType, "5") && isMember == 0) { // 终端
                                isCondition = true;
                            }
                            if (!isCondition) {
                                continue;
                            }
                            //限制经销商开瓶奖励规则
                            if (dealerActivityNewCommonModel.getIsUseLimit().equals(1)) {
                                List<CloudDealerActivityNewLimitModel> limitList = cloudDealerActivityNewLimitDao.selectSourceRuleByActivityId(dealerActivityNewCommonModel.getId());
                                if (CollectionUtils.isNotEmpty(limitList)) {
                                    //合同类型限制
                                    List<CloudDealerActivityNewLimitModel> contractTypeLimit = limitList.stream().filter(limit ->
                                                    limit.getType().equals(DealerActivityLimitEnum.LIMIT_CONTRACT_TYPE.getCode()))
                                            .collect(Collectors.toList());
                                    if (CollectionUtils.isNotEmpty(contractTypeLimit)) {
                                        List<String> contractTypeList = new ArrayList<>();
                                        contractTypeLimit.forEach(limit -> contractTypeList.addAll(JSON.parseArray(limit.getRangeJson(), String.class)));
                                        if (contractTypeList.contains(contractCommonModel.getContractType().toString())) {
                                            continue;
                                        }
                                    }
                                    //经销商类型限制
                                    List<CloudDealerActivityNewLimitModel> dealerTypeLimit = limitList.stream().filter(limit ->
                                                    limit.getType().equals(DealerActivityLimitEnum.LIMIT_DEALER_TYPE.getCode()))
                                            .collect(Collectors.toList());
                                    if (CollectionUtils.isNotEmpty(dealerTypeLimit)) {
                                        List<String> dealerTypeList = new ArrayList<>();
                                        dealerTypeLimit.forEach(limit -> dealerTypeList.addAll(JSON.parseArray(limit.getRangeJson(), String.class)));
                                        if (dealerTypeList.contains(contractInfo.getDealerType())) {
                                            continue;
                                        }
                                    }
                                }
                            }

                            if (!cloudDealerActivityNewCommonService.isSatisfiedExtraCondition(detail, dealerActivityNewCommonModel, extendDataBean)) {
                                log.info("码qrCode={}未匹配到活动", detail.getQrcode());
                                continue;
                            }

                            selectActivityList.add(dealerActivityNewCommonModel);
                        }
                        if (CollectionUtils.isEmpty(selectActivityList)) {
                            log.info("码qrCode={}未匹配到活动", detail.getQrcode());
                            saveActivityRewardExceptionRecordModel(detail, extendDataBean, "经销商编码判断[2023-05-31]", "码【" + detail.getQrcode() + "】未匹配到活动", EventType.DEALER_SALE_REWARD.getCode());
                            return;
                        }
                        //筛选出开启了奖励分配的活动 (优先匹配有分配奖励的活动)
                        List<DealerActivityNewCommonModel> asignActivityList = new ArrayList<>();
                        for (DealerActivityNewCommonModel dealerActivityNewCommonModel : selectActivityList) {
                            if (!dealerActivityNewCommonModel.getDistributorRewardAssignWhetherOpen().equals(1)) {
                                continue;
                            }
                            if (Objects.isNull(activityDealerContractRewardConfigCommonService
                                    .getActivityDealerContractRewardConfigByDealerInfo(
                                            dealerActivityNewCommonModel, dealerInfo, 1, detail.getReceivedOrderCode()))) {
                                continue;
                            }
                            asignActivityList.add(dealerActivityNewCommonModel);
                        }
                        //优先匹配分销商分配奖励
                        if (CollectionUtils.isNotEmpty(asignActivityList)) {
                            selectActivityList = asignActivityList;
                        }
                        //新集合按id排序
                        selectActivityList = selectActivityList.stream().sorted(Comparator.comparing(DealerActivityNewCommonModel::getId).reversed()).collect(Collectors.toList());
                        out:
                        for (DealerActivityNewCommonModel activityNew : values) {
                            String msg = "";
                            ActivityDealerContractRewardConfigCommonModel rewardConfig = activityDealerContractRewardConfigCommonService.getActivityDealerContractRewardConfigByDealerInfo(activityNew, dealerInfo, 1, detail.getReceivedOrderCode());
                            //计算集合的数量 乘等于
                            BigDecimal sumAwardVirtualAmount;
                            BigDecimal oneScore;
                            if (Objects.isNull(rewardConfig) || !activityNew.getDistributorRewardAssignWhetherOpen().equals(1)) {
                                oneScore = activityNew.getAwardMoney();
                                sumAwardVirtualAmount = activityNew.getAwardMoney().multiply(new BigDecimal(number));
                            } else {
                                oneScore = rewardConfig.getDealerReward();
                                sumAwardVirtualAmount = rewardConfig.getDealerReward().multiply(new BigDecimal(number));
                            }
                            reqName = reqName.concat(">积分:" + sumAwardVirtualAmount);
                            log.info("订单编码为:{}，，，，奖励金额：{}", detail.getOrderCode(), sumAwardVirtualAmount);
                            extendDataBean.setActivityName(activityNew.getActivityName());
                            extendDataBean.setAllowRewardOrder(activityNew.getRewardOrderFlag());

                            LambdaQueryWrapper<ActivityRewardRecordModel> clqw3 = Wrappers.lambdaQuery();
                            clqw3.eq(ActivityRewardRecordModel::getOriginId, detail.getId());
                            clqw3.eq(ActivityRewardRecordModel::getActivityType, ActivityType.SALE_REWARD.getCode());
                            clqw3.eq(ActivityRewardRecordModel::getEventType, EventType.DEALER_SALE_REWARD.getCode());
                            clqw3.eq(ActivityRewardRecordModel::getOriginTable, CommonConstant.T_TERMINAL_SCAN_DETAIL_TABLE);
                            clqw3.in(ActivityRewardRecordModel::getIsDelete, Arrays.asList("0", "10"));
                            clqw3.apply("extend_data -> '$.reward_sub_type' = {0}", activityNew.getRewardSubType());
                            clqw3.last(" LIMIT 1");
                            ActivityRewardRecordModel activityReward1 = activityRewardRecordDao.selectOne(clqw3);
                            Integer contractType = commonService.getContractTypeByContractCode(detail.getContractCode());
                            if (Objects.nonNull(activityReward1)) { //
                                log.info("码qrCode={}已发放经销商奖励", detail.getQrcode());
                                saveActivityRewardExceptionRecordModel(detail, extendDataBean, "分销商活动判断[2023-05-31]", "【码qrCode=" + detail.getQrcode() + "】已发放经销商奖励", EventType.DISTRIBUTOR_SALE_REWARD.getCode());
                            } else {
                                //判断其他规则
                                Map<String, Object> rtnMap = sendRewardCommomService.judgmentOtherRule(shopInfo, codeInfoRespList.get(0), 0, extendDataBean);
                                if (Objects.isNull(rtnMap)) {
                                    ActivityRewardRecordModel recordModel = new ActivityRewardRecordModel();
                                    recordModel.setShopId(shopModel.getId());
                                    recordModel.setAccountType(shopModel.getAccountType());
                                    recordModel.setActivityType(ActivityType.SALE_REWARD.getCode());
                                    recordModel.setEventType(EventType.DEALER_SALE_REWARD.getCode());
                                    recordModel.setOriginTable(CommonConstant.T_TERMINAL_SCAN_DETAIL_TABLE);
                                    recordModel.setOriginId(detail.getId());
                                    recordModel.setType(1);
                                    recordModel.setActivityId(activityNew.getId());
                                    recordModel.setActivityVersion(activityNew.getVersionId());
                                    recordModel.setRewardType(RewardType.SEND_SCORE_TYPE.getCode());
                                    recordModel.setCompanyId(detail.getCompanyId());
                                    recordModel.setIntegral(sumAwardVirtualAmount);
                                    recordModel.setSendStatus(SendStatus.WAIT_TYPE.getCode());
                                    recordModel.setCode(detail.getQrcode());
                                    recordModel.setOrderCode(detail.getReceivedOrderCode());
                                    recordModel.setIsMember("0");
                                    extendDataBean.setUnitAmount(oneScore);
                                    extendDataBean.setRewardContent(sumAwardVirtualAmount + "");
                                    recordModel.setExtendData(JSONObject.toJSONString(extendDataBean));
                                    String remark = receiptTypeName + "动销奖励-箱码-" + detail.getCodeXiang() + "-" + detail.getGoodsName();
                                    if (!detail.getQrcode().startsWith("1")) {
                                        remark = receiptTypeName + "动销奖励-盒码-" + detail.getQrcode() + "-" + detail.getGoodsName();
                                    }
                                    recordModel.setSceneRemark(remark);
                                    //经谈论取终端的合同 合同可能过期经销商经理无法发奖励
                                    recordModel.setContractCode(detail.getContractCode());
                                    recordModel.setContractType(contractType);
                                    recordModel.setBusinessId(RandomUtils.getActivityRewardRecordBusinessId());
                                    recordModel.setIsDelete(10);
                                    recordModel.setRemark("2024-05-31重算");
                                    activityRewardRecordDao.insert(recordModel);
                                } else {
                                    String rtnMsg = rtnMap.get("msg").toString();
                                    NoSendRewardType noSendRewardType = (NoSendRewardType) rtnMap.get("type");
                                    ActivityRewardExceptionRecordModel exceptionRecordModel = new ActivityRewardExceptionRecordModel();
                                    exceptionRecordModel.setShopId(shopModel.getId());
                                    exceptionRecordModel.setAccountType(shopModel.getAccountType());
                                    exceptionRecordModel.setActivityType(ActivityType.SALE_REWARD.getCode());
                                    exceptionRecordModel.setEventType(EventType.DEALER_SALE_REWARD.getCode());
                                    exceptionRecordModel.setOriginTable(CommonConstant.T_TERMINAL_SCAN_DETAIL_TABLE);
                                    exceptionRecordModel.setOriginId(detail.getId());
                                    exceptionRecordModel.setType(1);
                                    exceptionRecordModel.setActivityId(activityNew.getId());
                                    exceptionRecordModel.setActivityVersion(activityNew.getVersionId());
                                    exceptionRecordModel.setRewardType(RewardType.SEND_SCORE_TYPE.getCode());
                                    exceptionRecordModel.setSceneRemark(noSendRewardType.getMsg());
                                    exceptionRecordModel.setCompanyId(detail.getCompanyId());
                                    exceptionRecordModel.setIntegral(sumAwardVirtualAmount);
                                    exceptionRecordModel.setSendMsg(NoSendRewardType.getName(noSendRewardType.getCode()) + ":" + rtnMsg);
                                    exceptionRecordModel.setExceptionType(noSendRewardType.getCode());
                                    exceptionRecordModel.setCode(detail.getQrcode());
                                    exceptionRecordModel.setOrderCode(detail.getReceivedOrderCode());
                                    exceptionRecordModel.setIsMember(0);
                                    extendDataBean.setUnitAmount(oneScore);
                                    extendDataBean.setRewardContent(sumAwardVirtualAmount + "");
                                    exceptionRecordModel.setExtendData(JSONObject.toJSONString(extendDataBean));
                                    exceptionRecordModel.setContractCode(detail.getContractCode());
                                    exceptionRecordModel.setContractType(contractType);
                                    exceptionRecordModel.setScanTime(detail.getCreateTime());
                                    exceptionRecordModel.setRemark("2024-05-31重算");
                                    activityRewardExceptionRecordDao.insert(exceptionRecordModel);
                                }
                            }

                            //循环执行的  可能其他活动能匹配上
                            //是否发给分销商/合伙人
                            ExtendDataBean distributorExtendDataBean = new ExtendDataBean();
                            BeanUtils.copyProperties(extendDataBean, distributorExtendDataBean);
                            distributorExtendDataBean.setRoleName("分销商");
                            if (Objects.nonNull(rewardConfig) && activityNew.getDistributorRewardAssignWhetherOpen().equals(1)) {
                                ShopModel distributor = shopDao.getByDealerCode(rewardConfig.getDistributorDealerCode());
                                distributorExtendDataBean.setShopName(distributor.getName());
                                distributorExtendDataBean.setDealerCode(distributor.getDealerCode());
                                BigDecimal distributorAmount = rewardConfig.getDistributorReward().multiply(new BigDecimal(number));
                                String contractCode = commonService.getContractCodeByShopId(distributor.getId());
                                //判断其他规则
                                Map<String, Object> rtnDistributorMap = sendRewardCommomService.judgmentOtherRule(shopInfo, codeInfoRespList.get(0), 2, extendDataBean);

                                LambdaQueryWrapper<ActivityRewardRecordModel> clqw1 = Wrappers.lambdaQuery();
                                clqw1.eq(ActivityRewardRecordModel::getOriginId, detail.getId());
                                clqw1.eq(ActivityRewardRecordModel::getActivityType, ActivityType.SALE_REWARD.getCode());
                                clqw1.eq(ActivityRewardRecordModel::getEventType, EventType.DISTRIBUTOR_SALE_REWARD.getCode());
                                clqw1.eq(ActivityRewardRecordModel::getOriginTable, CommonConstant.T_TERMINAL_SCAN_DETAIL_TABLE);
                                clqw1.in(ActivityRewardRecordModel::getIsDelete, Arrays.asList("0", "10"));
                                clqw1.apply("extend_data -> '$.reward_sub_type' = {0}", activityNew.getRewardSubType());
                                clqw1.last(" LIMIT 1");
                                ActivityRewardRecordModel activityReward2 = activityRewardRecordDao.selectOne(clqw1);
                                if (Objects.nonNull(activityReward2)) { //
                                    log.info("码qrCode={}已发放分销商奖励", detail.getQrcode());
                                    saveActivityRewardExceptionRecordModel(detail, extendDataBean, "分销商活动判断[2023-05-31]", "【码qrCode=" + detail.getQrcode() + "】已发放经销商奖励", EventType.DISTRIBUTOR_SALE_REWARD.getCode());
                                    return;
                                }

                                if (Objects.isNull(rtnDistributorMap)) {
                                    ActivityRewardRecordModel record = new ActivityRewardRecordModel();
                                    record.setShopId(distributor.getId());
                                    record.setAccountType(distributor.getAccountType());
                                    record.setActivityType(ActivityType.SALE_REWARD.getCode());
                                    record.setEventType(EventType.DISTRIBUTOR_SALE_REWARD.getCode());
                                    record.setOriginTable(CommonConstant.T_TERMINAL_SCAN_DETAIL_TABLE);
                                    record.setOriginId(detail.getId());
                                    record.setType(1);
                                    record.setActivityId(activityNew.getId());
                                    record.setActivityVersion(activityNew.getVersionId());
                                    record.setRewardType(RewardType.SEND_SCORE_TYPE.getCode());
                                    record.setCompanyId(detail.getCompanyId());
                                    record.setIntegral(distributorAmount);
                                    record.setSendStatus(SendStatus.WAIT_TYPE.getCode());
                                    record.setCode(detail.getQrcode());
                                    record.setOrderCode(detail.getReceivedOrderCode());
                                    record.setIsMember("0");
                                    record.setContractCode(contractCode);
                                    record.setContractType(commonService.getContractTypeByContractCode(contractCode));
                                    String remark = receiptTypeName + "动销奖励-箱码-" + detail.getCodeXiang() + "-" + detail.getGoodsName();
                                    if (!detail.getQrcode().startsWith("1")) {
                                        remark = receiptTypeName + "动销奖励-盒码-" + detail.getQrcode() + "-" + detail.getGoodsName();
                                    }
                                    record.setSceneRemark(remark);
                                    extendDataBean.setRewardContent(distributorAmount + "");
                                    extendDataBean.setUnitAmount(rewardConfig.getDistributorReward());
                                    record.setExtendData(JSONObject.toJSONString(extendDataBean));
                                    record.setBusinessId(RandomUtils.getActivityRewardRecordBusinessId());
                                    record.setIsDelete(10);
                                    record.setRemark("2024-05-31重算");
                                    activityRewardRecordDao.insert(record);
                                } else {
                                    String rtnMsg = rtnDistributorMap.get("msg").toString();
                                    NoSendRewardType noSendRewardType = (NoSendRewardType) rtnDistributorMap.get("type");
                                    ActivityRewardExceptionRecordModel exceptionRecordModel = new ActivityRewardExceptionRecordModel();
                                    exceptionRecordModel.setShopId(distributor.getId());
                                    exceptionRecordModel.setAccountType(distributor.getAccountType());
                                    exceptionRecordModel.setActivityType(ActivityType.SALE_REWARD.getCode());
                                    exceptionRecordModel.setEventType(EventType.DISTRIBUTOR_SALE_REWARD.getCode());
                                    exceptionRecordModel.setOriginTable(CommonConstant.T_TERMINAL_SCAN_DETAIL_TABLE);
                                    exceptionRecordModel.setOriginId(detail.getId());
                                    exceptionRecordModel.setType(1);
                                    exceptionRecordModel.setActivityId(activityNew.getId());
                                    exceptionRecordModel.setActivityVersion(activityNew.getVersionId());
                                    exceptionRecordModel.setRewardType(RewardType.SEND_SCORE_TYPE.getCode());
                                    exceptionRecordModel.setCompanyId(detail.getCompanyId());
                                    exceptionRecordModel.setIntegral(distributorAmount);
                                    exceptionRecordModel.setSendMsg(NoSendRewardType.getName(noSendRewardType.getCode()) + ":" + rtnMsg);
                                    exceptionRecordModel.setExceptionType(noSendRewardType.getCode());
                                    exceptionRecordModel.setCode(detail.getQrcode());
                                    exceptionRecordModel.setOrderCode(detail.getReceivedOrderCode());
                                    exceptionRecordModel.setIsMember(0);
                                    extendDataBean.setRewardContent(distributorAmount + "");
                                    extendDataBean.setUnitAmount(rewardConfig.getDistributorReward());
                                    exceptionRecordModel.setExtendData(JSONObject.toJSONString(extendDataBean));
                                    exceptionRecordModel.setContractCode(contractCode);
                                    exceptionRecordModel.setContractType(commonService.getContractTypeByContractCode(contractCode));
                                    exceptionRecordModel.setSceneRemark(noSendRewardType.getMsg());
                                    exceptionRecordModel.setScanTime(detail.getCreateTime());
                                    exceptionRecordModel.setRemark("2024-05-31重算");
                                    activityRewardExceptionRecordDao.insert(exceptionRecordModel);
                                }
                            } else {
                                msg = "活动:" + activityNew.getId() + "无高级配置,不发放奖励";
                                distributorExtendDataBean.setShopName("");
                                distributorExtendDataBean.setDealerCode("");
                                saveActivityRewardExceptionRecordModel(detail, distributorExtendDataBean, "分销商活动判断[2023-05-31]", "没有匹配到分销商的活动", EventType.DISTRIBUTOR_SALE_REWARD.getCode());
                            }
                            //发放完奖励直接跳出到最外层
                            break out;
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error("收货时给经销商发奖励时报错:{}", e.getMessage(), e);
            throw new BusinessException("发经销商奖励失败:" + e.getMessage());
        }
    }

    private void saveActivityRewardExceptionRecordModel(TerminalScanDetailModel detail, ExtendDataBean
            extendData, String sceneRemark, String sendMsg, Integer eventType) {
        ActivityRewardExceptionRecordModel exceptionRecordModel = new ActivityRewardExceptionRecordModel();
        exceptionRecordModel.setActivityType(ActivityType.SALE_REWARD.getCode());
        exceptionRecordModel.setEventType(eventType);
        exceptionRecordModel.setSceneRemark(sceneRemark);
        exceptionRecordModel.setOriginTable(CommonConstant.T_TERMINAL_SCAN_DETAIL_TABLE);
        exceptionRecordModel.setOriginId(detail.getId());
        exceptionRecordModel.setType(1);
        exceptionRecordModel.setExtendData(JSONObject.toJSONString(extendData));
        exceptionRecordModel.setSendMsg(sendMsg);
        exceptionRecordModel.setCode(detail.getQrcode());
        exceptionRecordModel.setOrderCode(detail.getReceivedOrderCode());
        exceptionRecordModel.setScanTime(detail.getCreateTime());
        exceptionRecordModel.setCompanyId(detail.getCompanyId());
        activityRewardExceptionRecordDao.insert(exceptionRecordModel);
    }

    // 同一个码被重复收货 不发奖励
    private Boolean getJudgeDealerInfo(TerminalScanDetailModel detail, RewardReason rewardReason) {
        Boolean rtnVal = false;
        TerminalScanDetailModel detailModel = terminalScanDetailPlusDao.hasJudgeTerminalRrwordByQrCode(detail.getId(), detail.getQrcode());
        if (Objects.nonNull(detailModel)) {
            rewardReason.setIsSave(true);
            rewardReason.setHasDealer(0);
            String msg = "码:" + detail.getQrcode() + "被" + detailModel.getReceivedOrderCode() + "收过货且已发奖励";
            rewardReason.setDealerDesc(msg);
            rewardReason.setHasDistributor(0);
            rewardReason.setDistributorDesc(msg);
            rtnVal = true;
        }
        return rtnVal;
    }


    /**
     * 已废弃
     */
    @Deprecated
   /* @Override
    @Transactional*/
    public void asyncSendAwardDealerByOrderNew(List<CodeInfoResp> codeInfoRespList, CloudDealerInfoModel
            dealerInfo, Integer originType,
                                               ShopModel shopInfo, List<TerminalScanDetailModel> detailModelList, Integer receiptType) {
        StopWatch watch = new StopWatch();
        shopInfo.setShopId(shopInfo.getId());

        RequestLog requestLog = new RequestLog();
        requestLog.setReqName("【不能删除】异步【经销商】动销奖励:" + detailModelList.get(0).getReceivedOrderCode() + "->shopId=" + shopInfo.getId());
        requestLog.setReqType(40);
        requestLog.setReqUrlPath("SendAwardDealerServiceImpl->asyncSendAwardDealerByOrderNew");
        requestLog.setCreateDate(new Date());
        requestLog.setReqKey(detailModelList.get(0).getReceivedOrderCode());
        /*AsyncSendDealerRewardReq asyncSendDealerRewardReq = new AsyncSendDealerRewardReq();
        asyncSendDealerRewardReq.setCodeInfoRespList(codeInfoRespList);
        asyncSendDealerRewardReq.setDetailModelList(detailModelList);
        asyncSendDealerRewardReq.setShopType(shopType);
        asyncSendDealerRewardReq.setOriginType(originType);
        asyncSendDealerRewardReq.setShopInfo(shopInfo);
        requestLog.setReqJson(JSONObject.toJSONString(asyncSendDealerRewardReq));*/
        try {
            for (CodeInfoResp codeInfoResp : codeInfoRespList) {
                List<CodeInfoResp> infoList = new ArrayList<>();
                infoList.add(codeInfoResp);
                TerminalScanDetailModel detailModel = detailModelList.stream().filter(e -> e.getCodeXiang().equals(codeInfoResp.getCodeXiang()) && e.getCodeIn().equals(codeInfoResp.getCodeIn())).findFirst().orElse(null);
                watch.start();
                RequestLog reqLog = new RequestLog();
                reqLog.setReqName("异步【经销商】动销奖励:" + detailModel.getReceivedOrderCode() + "->" + codeInfoResp.getQrcode() + "->detailId=" + detailModel.getId());
                reqLog.setReqType(40);
                reqLog.setReqUrlPath("SendAwardDealerServiceImpl->asyncSendAwardDealerByOrderNew");
                reqLog.setCreateDate(new Date());
                reqLog.setReqJson(JSONObject.toJSONString(detailModel));
                reqLog.setResJson(JSONObject.toJSONString(codeInfoResp));
                reqLog.setReqKey(detailModel.getReceivedOrderCode() + ">" + codeInfoResp.getQrcode());
                try {
                    //更新积分的经销商计算积分标识
                    TerminalScanDetailModel updateModel = new TerminalScanDetailModel();
                    updateModel.setIsDealerReward(1);
                    updateModel.setVirtualAmount(null);
                    updateModel.setId(detailModel.getId());
                    log.info("更新的经销商奖励是否发放:{}", detailModel.getReceivedOrderCode());
                    terminalScanDetailPlusDao.updateById(updateModel);
                    reqLog.setResCode("0");
                } catch (Exception e) {
                    reqLog.setResCode("-1");
                    reqLog.setResMsg(e.getMessage());
                    e.printStackTrace();
                    log.error("【异步经销商奖励异常】={}", e);
                    throw new BusinessException(e.getMessage());
                } finally {
                    watch.stop();
                    String diffTime = DateUtils.longToHMS(watch.getLastTaskTimeMillis());
                    reqLog.setReqTime(diffTime);
                    requestLogService.insertLog(reqLog);
                }
            }
            requestLog.setResCode("0");
        } catch (Exception e) {
            String msg = "【" + shopInfo.getName() + "(" + shopInfo.getId() + ")" + "】(" + detailModelList.get(0).getReceivedOrderCode() + ")一键收货异步计算经销商动销奖励失败";
            // 需发送短信通知
            systemMessageService.asyncSendMsg("***********", msg);
            requestLog.setResCode("-1");
            requestLog.setResMsg(e.getMessage());
            e.printStackTrace();
            log.error("【异步经销商奖励异常1】={}", e);
            throw new BusinessException(e.getMessage());
        } finally {
            String diffTime = DateUtils.longToHMS(watch.getTotalTimeMillis());
            requestLog.setReqTime(diffTime);
            requestLogService.insertLog(requestLog);
        }
    }

    /**
     * 按单收货的时候发放经销商奖励的接口 【暂时废弃】
     *
     * @param codeInfoRespList 瓶为单位的数据集合
     * @param shopType         终端店类型 3:分销商 5:终端 4:合伙人
     * @param originId         来源id
     * @param originType       来源类型 1:按单收货 2:智能布奖 3:经销商出库
     * @param shopId           终端店id
     * @param companyId        公司id
     * @param orderCode        在线订单订单号
     */
    @Override
    public void sendAwardDealerByOrder(List<CodeInfoResp> codeInfoRespList,
                                       String shopType,
                                       Integer originId,
                                       Integer originType,
                                       Integer shopId,
                                       Integer companyId,
                                       String orderCode) {

        log.info("我是按单收货的时候发放经销商奖励的接口的参数=====终端店类型:{},来源id:{},来源类型:{},终端店id:{}", shopType, originId, originType, shopId);

        //没有酒的话直接返回
        if (null == codeInfoRespList || codeInfoRespList.size() < 1) {
            log.info("我终端店入库奖励因为没有产品直接返回啦");
            return;
        }

        //根据酒的类型分组
        Map<String, List<CodeInfoResp>> cycleMap = codeInfoRespList.stream().collect(Collectors.groupingBy(CodeInfoResp::getGoodsCode));

        //1.获取发放这个产品的经销商编码
        String dealerCode = codeInfoRespList.get(0).getDealerCode();
        String qrCode = codeInfoRespList.get(0).getQrcode();

        //如果经销商编码为空 说明没有活动 直接返回
        if (StringUtils.isBlank(dealerCode)) {
            log.info("我终端店入库奖励因为没有经销商编码直接返回啦");
            return;
        }
        //查询经销商信息
        CloudDealerInfoModel dealerInfoModel = dealerInfoCommonDao.selectDealerIdByDealerCode(dealerCode);

        //根据酒的类型遍历
        for (Map.Entry<String, List<CodeInfoResp>> goodGroup : cycleMap.entrySet()) {
            //商品数量
            Integer number = 0;
            for (CodeInfoResp resp : goodGroup.getValue()) {
                number = number + resp.getNumber();
            }
            List<DealerActivityNewCommonModel> selectActivityNewList = Lists.newArrayList();
            //2.如果经销商编码不为空 去匹配对应的经销商活动 并且倒叙排序查询，按经销商编码查
            List<DealerActivityNewCommonModel> selectActivityNewListOne =
                    cloudDealerActivityAppointDealerCommonDao.selectDealerActivityNewListByShopInStock(dealerCode, new Date(), goodGroup.getKey());
            if (CollectionUtils.isNotEmpty(selectActivityNewListOne)) {
                selectActivityNewList.addAll(selectActivityNewListOne);
            }
            //2.1 按经销商所在的省市区匹配活动
            if (StringUtils.isNotBlank(dealerInfoModel.getProvinces())) {
                String provinces = dealerInfoModel.getProvinces();
                String city = dealerInfoModel.getCity();
                String district = dealerInfoModel.getDistrict();
                //领导要求，4个直辖市特殊处理，只处理省、区
                if (provinces.equals("北京市") || provinces.equals("上海市") || provinces.equals("天津市") || provinces.equals("重庆市")) {
                    if (StringUtils.isBlank(district)) {
                        district = city;
                    }
                    city = "";
                }
                List<DealerActivityNewCommonModel> selectActivityNewListTwo =
                        cloudDealerActivityAppointDealerCommonDao.selectDealerActivityNewListByShopInStockAddress(provinces, city, district, new Date(), goodGroup.getKey());
                if (CollectionUtils.isNotEmpty(selectActivityNewListTwo)) {
                    selectActivityNewList.addAll(selectActivityNewListTwo);
                }
            }
            //新集合按id排序
            selectActivityNewList = selectActivityNewList.stream().sorted(Comparator.comparing(DealerActivityNewCommonModel::getId).reversed()).collect(Collectors.toList());
            //3.查询经销商是否扫码出库 无需判断 这时候经销商一定已经扫码出库

            //4.先查询一下 这个经销商有没有合同
            List<CloudDealerGtFxsContractCommonModel> selectContractList = contractUtils.getContractListFromXw(dealerCode, null, null);
            //如果没有合同 或者没有分销对象类型 就直接跳出
            if (null == selectContractList) {
                log.info("我是:{}经销商没有合同", dealerCode);
                return;
            }

            //遍历查出来的所有经销商活动
            out:
            for (DealerActivityNewCommonModel activityNew : selectActivityNewList) {

                //如果类型为空直接跳出这次
                if (StringUtils.isBlank(activityNew.getDistributorType())) {
                    log.info("我是:{}活动没有分销对象类型", activityNew.getId());
                    continue;
                }

                //如果有合同 也有分销对象类型 劈开看看能不能匹配
                String[] distributorType = activityNew.getDistributorType().split(",");
                //遍历匹配
                for (String shopTypeSplit : distributorType) {
                    //看看能不能匹配 如果匹配上就发放奖励
                    if (shopTypeSplit.equals(shopType)) {
                        //计算集合的数量 乘等于
                        BigDecimal sumAwardVirtualAmount = activityNew.getAwardMoney().multiply(new BigDecimal(number));
                        //发放奖励
                        dealerInfoCommonDao.addVirtualAmount(dealerCode, sumAwardVirtualAmount);
                        //发放完奖励 去奖励表记录一下
                        insertDealerRewardRecord(
                                originId,
                                originType,
                                qrCode,
                                activityNew.getId(),
                                activityNew.getActivityName(),
                                goodGroup.getKey(),
                                goodGroup.getValue().get(0).getGoodsName(),
                                number,
                                dealerCode,
                                shopId,
                                1,
                                activityNew.getAwardMoney(),
                                sumAwardVirtualAmount,
                                companyId,
                                orderCode,
                                null,
                                0
                        );
                        //发放完奖励直接跳出到最外层
                        break out;
                    }
                }
            }
        }
    }

    /**
     * 经销商出库的时候发给经销商奖励
     *
     * @param shopType   终端店类型 3:分销商 5:终端 4:合伙人
     * @param originId   来源id
     * @param originType 来源类型 1:按单收货 2:智能布奖 3:经销商出库
     * @param shopId     终端店id
     * @param companyId  公司id
     * @param orderCode  在线订单订单号
     */
    @Override
    public void sendRewardDealerByOutStock(List<Map<String, Object>> productList,
                                           String shopType,
                                           Long originId,
                                           Integer originType,
                                           Integer shopId,
                                           Integer companyId,
                                           String dealerCode,
                                           String orderCode) {

        //如果经销商编码为空直接返回
        if (StringUtils.isBlank(dealerCode)) {
            log.info("我经销商出库奖励因为没有经销商编码直接返回啦");
            return;
        }

        //如果产品为空也直接返回
        if (null == productList || productList.size() < 1) {
            log.info("我经销商出库奖励因为没有产品直接返回啦");
            return;
        }

        //如果这个来源id已经发过钱了 拒绝二次发钱
        Long rewardRecordCount = cloudDealerRewardRecordDao.selectCount(new QueryWrapper<CloudDealerRewardRecordModel>().
                eq("origin_id", originId).
                eq("origin_type", originType)
        );

        if (rewardRecordCount > 0) {
            log.info("我经销商出库奖励因为已经发给过经销商奖励直接返回啦");
            return;
        }

        //查询经销商信息
        CloudDealerInfoModel dealerInfoModel = dealerInfoCommonDao.selectDealerIdByDealerCode(dealerCode);

        //4.先查询一下 这个经销商有没有合同
        List<CloudDealerGtFxsContractCommonModel> selectContractList = contractUtils.getContractListFromXw(dealerCode, null, null);

        //如果没有合同 或者没有分销对象类型 就直接跳出
        if (null == selectContractList) {
            log.info("我是:{}经销商没有合同", dealerCode);
            return;
        }

        //遍历产品
        for (Map<String, Object> productMap : productList) {
            List<DealerActivityNewCommonModel> selectActivityNewList = Lists.newArrayList();
            //1.如果经销商编码不为空 去匹配对应的经销商活动 并且倒叙排序查询 - 按经销商编码查
            List<DealerActivityNewCommonModel> selectActivityNewListOne =
                    cloudDealerActivityAppointDealerCommonDao.selectDealerActivityNewListByDealerOutStock(dealerCode, new Date(), productMap.get("productCode").toString());
            if (CollectionUtils.isNotEmpty(selectActivityNewListOne)) {
                selectActivityNewList.addAll(selectActivityNewListOne);
            }
            //1.1 按省市区匹配活动
            if (StringUtils.isNotBlank(dealerInfoModel.getProvinces())) {
                String provinces = dealerInfoModel.getProvinces();
                String city = dealerInfoModel.getCity();
                String district = dealerInfoModel.getDistrict();
                //领导要求，4个直辖市特殊处理，只处理省、区
                if (provinces.equals("北京市") || provinces.equals("上海市") || provinces.equals("天津市") || provinces.equals("重庆市")) {
                    if (StringUtils.isBlank(district)) {
                        district = city;
                    }
                    city = "";
                }
                List<DealerActivityNewCommonModel> selectActivityNewListTwo =
                        cloudDealerActivityAppointDealerCommonDao.selectDealerActivityNewListByDealerOutStockAddress(provinces, city, district, new Date(), productMap.get("productCode").toString());
                if (CollectionUtils.isNotEmpty(selectActivityNewListTwo)) {
                    selectActivityNewList.addAll(selectActivityNewListTwo);
                }
            }

            //新集合按id排序
            selectActivityNewList = selectActivityNewList.stream().sorted(Comparator.comparing(DealerActivityNewCommonModel::getId).reversed()).collect(Collectors.toList());
            //遍历查出来的所有经销商活动
            out:
            for (DealerActivityNewCommonModel activityNew : selectActivityNewList) {

                //如果类型为空直接跳出这次
                if (StringUtils.isBlank(activityNew.getDealerOutStockType())) {
                    log.info("我是:{}活动没有分销对象类型", activityNew.getId());
                    continue;
                }

                //如果有合同 也有分销对象类型 劈开看看能不能匹配
                String[] dealerType = activityNew.getDealerOutStockType().split(",");

                //遍历匹配
                for (String shopTypeSplit : dealerType) {
                    //看看能不能匹配 如果匹配上就发放奖励
                    if (shopTypeSplit.equals(shopType)) {
                        //计算集合的数量 乘等于
                        BigDecimal sumAwardVirtualAmount = activityNew.getDealerOutStockRewardMoney().multiply(new BigDecimal(Integer.parseInt(productMap.get("productNumber").toString())));
                        //发放奖励
                        dealerInfoCommonDao.addVirtualAmount(dealerCode, sumAwardVirtualAmount);
                        //发放完奖励 去奖励表记录一下
                        insertDealerRewardRecord(
                                originId,
                                originType,
                                activityNew.getId(),
                                activityNew.getActivityName(),
                                productMap.get("productCode").toString(),
                                productMap.get("productName").toString(),
                                Integer.parseInt(productMap.get("productNumber").toString()),
                                dealerCode,
                                shopId,
                                1,
                                activityNew.getDealerOutStockRewardMoney(),
                                sumAwardVirtualAmount,
                                companyId,
                                orderCode

                        );
                        //发放完奖励直接跳出到最外层
                        break out;
                    }
                }
            }
        }
    }


    /**
     * 添加一条经销商受到的奖励记录
     *
     * @param originId                来源id
     * @param originType              来源类型 1:按单收货 2:智能布奖 3:经销商出库 4:终端店进货 5:终端店取消进货 6:扫码退货
     * @param activityId              活动id
     * @param activityName            活动名称
     * @param goodCode                产品编码
     * @param goodName                产品名称
     * @param goodNumber              产品数量
     * @param dealerCode              经销商编码
     * @param shopId                  终端店id
     * @param rewardType              发放的奖励类型 1:虚拟货款
     * @param unitPriceOfRewardAmount 发放的奖励金额单价
     * @param rewardAmount            发放的奖励金额
     * @param companyId               商户id
     * @param orderCode               在线订单订单号
     */
    @Override
    public void insertDealerRewardRecord(Long originId,
                                         Integer originType,
                                         Integer activityId,
                                         String activityName,
                                         String goodCode,
                                         String goodName,
                                         Integer goodNumber,
                                         String dealerCode,
                                         Integer shopId,
                                         Integer rewardType,
                                         BigDecimal unitPriceOfRewardAmount,
                                         BigDecimal rewardAmount,
                                         Integer companyId,
                                         String orderCode) {
        //创建实体类
        CloudDealerRewardRecordModel insertData = new CloudDealerRewardRecordModel();
        //来源id
        insertData.setOriginId(originId);
        //来源类型
        insertData.setOriginType(originType);
        //活动id
        insertData.setActivityId(activityId);
        //活动名称
        insertData.setActivityName(activityName);
        //产品编码
        insertData.setGoodCode(goodCode);
        //产品名称
        insertData.setGoodName(goodName);
        //产品数量
        insertData.setGoodNumber(goodNumber);
        //经销商编码
        insertData.setDealerCode(dealerCode);
        //扫码的终端店id
        insertData.setShopId(shopId);
        //发放的奖励类型
        insertData.setRewardType(rewardType);
        //发放的奖励金额单价
        insertData.setUnitPriceOfRewardAmount(unitPriceOfRewardAmount);
        //发放的奖励金额
        insertData.setRewardAmount(rewardAmount);
        //原始在线订单订单号
        insertData.setOrderCode(orderCode);
        //是否删除 默认0 (0:未删除 1:已删除)
        insertData.setIsDelete(0);
        //当前时间
        Date now = new Date();
        //创建时间
        insertData.setCreateTime(now);
        //更新时间
        insertData.setUpdateTime(now);
        // 商户id
        insertData.setCompanyId(companyId);
        //添加
        cloudDealerRewardRecordDao.insert(insertData);
    }

    @Override
    public void insertDealerRewardRecord(ShopDealerOrderPlus dealerOrder) {
        LambdaQueryWrapper<ShopDealerOrderDetailPlus> detailLqw = Wrappers.lambdaQuery();
        detailLqw.eq(ShopDealerOrderDetailPlus::getOrderId, dealerOrder.getId());
        List<ShopDealerOrderDetailPlus> detailPlusList = shopDealerOrderDetailPlusMapper.selectList(detailLqw);

        CloudDealerInfoModel dealerInfo = dealerInfoCommonDao.selectDealerIdByDealerCode(dealerOrder.getDealerCode());
        Long parentId = dealerInfoCommonDao.getParentIdToId(dealerInfo.getId());
        DealerInfoCommonModel infoModelReq = dealerInfoCommonDao.selectById(parentId);

        if (Objects.nonNull(infoModelReq) && infoModelReq.getAccountType() == 1) {//
            CloudDealerRewardRecordModel insertData = new CloudDealerRewardRecordModel();
            insertData.setOriginId(dealerOrder.getId());
            insertData.setOriginType(4);
            insertData.setActivityName("0-终端店进货,扣除虚拟货款给经销商");
            insertData.setGoodCode(detailPlusList.get(0).getGoodsCode());
            insertData.setGoodName(detailPlusList.get(0).getGoodsName());
            insertData.setGoodNumber(detailPlusList.get(0).getQty());
            insertData.setRewardType(1);
            insertData.setShopId(dealerOrder.getShopId());
            insertData.setRewardAmount(dealerOrder.getVirtualAmount());
            insertData.setOrderCode(dealerOrder.getOrderCode());
            insertData.setIsDelete(0);
            insertData.setCreateTime(new Date());
            insertData.setCompanyId(dealerOrder.getCompanyId());
            insertData.setDealerCode(infoModelReq.getDealerCode());
            insertData.setMessage("定时对账接口添加");
            insertData.setStatus(1);
            cloudDealerRewardRecordDao.insert(insertData);
        }
    }

    /**
     * 添加一条经销商受到的奖励记录
     *
     * @param originId                来源id
     * @param originType              来源类型 1:按单收货 2:智能布奖 3:经销商出库 4:终端店进货 5:终端店取消进货 6:扫码退货
     * @param activityId              活动id
     * @param activityName            活动名称
     * @param goodCode                产品编码
     * @param goodName                产品名称
     * @param goodNumber              产品数量
     * @param dealerCode              经销商编码
     * @param shopId                  终端店id
     * @param rewardType              发放的奖励类型 1:虚拟货款
     * @param unitPriceOfRewardAmount 发放的奖励金额单价
     * @param rewardAmount            发放的奖励金额
     * @param companyId               商户id
     * @param orderCode               在线订单订单号
     */
    public void insertDealerRewardRecord(Integer originId,
                                         Integer originType,
                                         String qrcode,
                                         Integer activityId,
                                         String activityName,
                                         String goodCode,
                                         String goodName,
                                         Integer goodNumber,
                                         String dealerCode,
                                         Integer shopId,
                                         Integer rewardType,
                                         BigDecimal unitPriceOfRewardAmount,
                                         BigDecimal rewardAmount,
                                         Integer companyId,
                                         String orderCode,
                                         TerminalScanDetailModel detail,
                                         Integer handleDataType) {
        //创建实体类
        CloudDealerRewardRecordModel insertData = new CloudDealerRewardRecordModel();
        //来源id
        insertData.setOriginId(Long.valueOf(originId));
        //来源类型
        insertData.setOriginType(originType);
        //码
        insertData.setQrCode(qrcode);
        //活动id
        insertData.setActivityId(activityId);
        //活动名称
        insertData.setActivityName(activityName);
        //产品编码
        insertData.setGoodCode(goodCode);
        //产品名称
        insertData.setGoodName(goodName);
        //产品数量
        insertData.setGoodNumber(goodNumber);
        //经销商编码
        insertData.setDealerCode(dealerCode);
        //扫码的终端店id
        insertData.setShopId(shopId);
        //发放的奖励类型
        insertData.setRewardType(rewardType);
        //发放的奖励金额单价
        insertData.setUnitPriceOfRewardAmount(unitPriceOfRewardAmount);
        //发放的奖励金额
        insertData.setRewardAmount(rewardAmount);
        //原始在线订单订单号
        insertData.setOrderCode(orderCode);
        //是否删除 默认0 (0:未删除 1:已删除)
        insertData.setIsDelete(0);
        //当前时间
        Date now = new Date();
        //创建时间
        insertData.setCreateTime(now);
        //更新时间
        insertData.setUpdateTime(now);
        // 商户id
        insertData.setCompanyId(companyId);
        insertData.setDetailId(detail.getId());

        insertData.setStatus(0);
        /**
         *  判断是否是预备终端
         */
        ShopModel shopModel = shopDao.getById(shopId);
        Integer isPrepare = shopModel.getIsPrepare();
        log.info("终端id:{}-----》{}是终端信息:{}，奖励到临时表:{}", shopId, isPrepare, JSONObject.toJSONString(shopModel));
        log.info("hasCheck=={}---checkState》{}===>是:{}", detail.getHasCheck(), detail.getCheckState(), detail.getHasCheck() == 1 && Arrays.asList("0", "2").contains(detail.getCheckState() + ""));
        //添加
        if (handleDataType == 0) {
            if (detail.getHasCheck() == 1 && Arrays.asList("0", "2").contains(detail.getCheckState() + "")) {
                CloudDealerRewardRecordPrepare prepare = new CloudDealerRewardRecordPrepare();
                BeanUtils.copyProperties(insertData, prepare, "id");
                prepare.setId(null);
                prepare.setIsFlag(1);
                if (detail.getCheckState() == 2) {//不发的原因
                    prepare.setRemark(detail.getCheckMsg());
                }
                log.info("终端id:{}是预备终端，奖励到临时表{}", shopId, JSONObject.toJSONString(prepare));
                cloudDealerRewardRecordPrepareMapper.insert(prepare);
            } else if (isPrepare == 1) {
                CloudDealerRewardRecordPrepare prepare = new CloudDealerRewardRecordPrepare();
                BeanUtils.copyProperties(insertData, prepare, "id");
                prepare.setId(null);
                log.info("终端id:{}是预备终端，奖励到临时表{}", shopId, JSONObject.toJSONString(prepare));
                cloudDealerRewardRecordPrepareMapper.insert(prepare);
            } else {
                cloudDealerRewardRecordDao.insert(insertData);
            }
        } else {
            if (detail.getHasCheck() == 1 && Arrays.asList("0", "2").contains(detail.getCheckState() + "")) {
                CloudDealerRewardRecordPrepareHandle prepare = new CloudDealerRewardRecordPrepareHandle();
                BeanUtils.copyProperties(insertData, prepare);
                prepare.setId(null);
                prepare.setIsFlag(1);
                if (detail.getCheckState() == 2) {//不发的原因
                    prepare.setRemark(detail.getCheckMsg());
                }
                log.info("终端id:{}是预备终端，奖励到临时表{}", shopId, JSONObject.toJSONString(prepare));
                cloudDealerRewardRecordPrepareHandleMapper.insert(prepare);
            } else if (isPrepare == 1) {
                CloudDealerRewardRecordPrepareHandle prepare = new CloudDealerRewardRecordPrepareHandle();
                BeanUtils.copyProperties(insertData, prepare, "id");
                prepare.setId(null);
                log.info("终端id:{}是预备终端，奖励到临时表{}", shopId, JSONObject.toJSONString(prepare));
                cloudDealerRewardRecordPrepareHandleMapper.insert(prepare);
            } else {
                CloudDealerRewardRecordHandle recordHandle = new CloudDealerRewardRecordHandle();
                BeanUtils.copyProperties(insertData, recordHandle, "id");
                recordHandle.setId(null);
                cloudDealerRewardRecordHandleMapper.insert(recordHandle);
            }
        }
    }

    public static void main(String[] args) {
        System.out.println(Arrays.asList(0, 2).contains(0));
    }

    private RewardReason initRewardReason(TerminalScanDetailModel detail, Integer receiptType) {
        RewardReason rewardReason = new RewardReason();
        rewardReason.setReceiptType(receiptType);
        rewardReason.setDetailId(detail.getId());
        rewardReason.setShopId(detail.getShopId());
        rewardReason.setAccountType(detail.getAccountType());
        rewardReason.setRewardType(0);
        rewardReason.setOrderCode(detail.getReceivedOrderCode());
        rewardReason.setQrCode(detail.getQrcode());
        rewardReason.setIsSave(false);
        rewardReason.setCreateTime(new Date());
        return rewardReason;
    }
}
