package com.intelliquor.cloud.shop.common.model;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 扫码奖励活动限制条件
 * @TableName t_reward_activity_limit
 */
@TableName("t_reward_activity_limit")
@Data
public class RewardActivityLimitModel implements Serializable {
    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * t_reward_activity表id
     */
    private Integer activityId;

    /**
     * 条件类型1合同类型2终端编码
     */
    private Integer type;

    /**
     * 动销奖励（1不发放0发放）
     */
    private Integer useSaleReward;

    /**
     * 开瓶奖励（1不发放0发放）
     */
    private Integer useOpenBottleReward;

    /**
     * 范围
     */
    private String rangeJson;

    /**
     * 创建人
     */
    private Integer createUser;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 是否删除（1是0否）
     */
    private Integer isDelete;

    /**
     * 公司id
     */
    private Integer companyId;

    private static final long serialVersionUID = 1L;
}