package com.intelliquor.cloud.shop.common.dao;

import com.intelliquor.cloud.shop.common.model.ActivityRewardDataDownloadRecordModel;
import com.intelliquor.cloud.shop.common.model.WechatPayDetailModel;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.intelliquor.cloud.shop.common.model.req.WechatPayDetailReq;
import com.intelliquor.cloud.shop.common.model.resp.WechatPayDetailRespModel;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 微信支付记录详情 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-04
 */
public interface WechatPayDetailDao extends BaseMapper<WechatPayDetailModel> {

    List<WechatPayDetailRespModel> selectRedEnvelopList(WechatPayDetailReq req);

    List<WechatPayDetailRespModel> exportData(Map<String, Object> search);

    List<WechatPayDetailRespModel> findExportList(ActivityRewardDataDownloadRecordModel model);

    List<WechatPayDetailRespModel> checkBill(WechatPayDetailReq req);
}
