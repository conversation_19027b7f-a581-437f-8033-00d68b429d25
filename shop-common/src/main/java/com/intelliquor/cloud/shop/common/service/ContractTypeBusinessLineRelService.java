package com.intelliquor.cloud.shop.common.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.intelliquor.cloud.shop.common.enums.BusinessLineEnum;
import com.intelliquor.cloud.shop.common.model.ContractTypeBusinessLineRelModel;

import java.util.List;

/**
 * 合同类型与业务条线关系Service接口
 *
 * <AUTHOR>
 * @since 2024/7/19
 */
public interface ContractTypeBusinessLineRelService extends IService<ContractTypeBusinessLineRelModel> {
    
    /**
     * 根据合同类型查询业务条线关系
     * 
     * @param contractType 合同类型
     * @return 业务条线关系信息
     * @throws com.intelliquor.cloud.shop.common.exception.BusinessException 如果未找到对应的业务条线关系
     */
    ContractTypeBusinessLineRelModel getByContractType(Integer contractType);

    /**
     * 判断合同类型是否属于高端业务条线
     *
     * @param contractType 合同类型
     * @return true-属于高端业务条线，false-不属于高端业务条线
     * @throws com.intelliquor.cloud.shop.common.exception.BusinessException 如果判断过程中发生异常
     */
    boolean isHighEndBusinessLine(Integer contractType);

    /**
     * 获取合同类型对应的业务线信息
     *
     * @param contractType 合同类型
     * @return 业务线枚举值，若发生异常则返回NOT_PARTICIPATE
     */
    BusinessLineEnum getBusinessLine(Integer contractType);
    
    /**
     * 根据业务条线查询所有记录
     * <p>
     * 查询指定业务条线对应的所有合同类型关系记录，按合同类型升序排序。
     * 如果业务条线参数为空，将返回空列表。
     * 
     * @param businessLine 业务条线枚举值
     * @return 符合条件的所有合同类型与业务条线关系记录列表，如果没有符合条件的记录则返回空列表
     * @see BusinessLineEnum
     */
    List<ContractTypeBusinessLineRelModel> getByBusinessLine(BusinessLineEnum businessLine);
} 