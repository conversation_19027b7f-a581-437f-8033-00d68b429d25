package com.intelliquor.cloud.shop.common.utils.tokenstore.dto;

import java.util.Date;
import java.util.List;
import lombok.Data;

@Data
public class SaleOrderDTO {

    private String id;
    private String code; // 单据编号
    private String ts; // 单据编号(可不传)

    private Date voucherDate; // 订单日期
    private String externalCode; // 终端订单单号
    private String customerCode; // 终端门店编码
    private String memo; // 订单备注
    private Double pubuserdefdecm1; // 费用
    private Double pubuserdefdecm2; // 积分
    private List<SaleOrderDetail> saleOrderDetails; // 订单明细


}
