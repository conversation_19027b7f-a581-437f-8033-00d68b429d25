package com.intelliquor.cloud.shop.wechat.dao;


import com.intelliquor.cloud.shop.wechat.model.WechatPackageInfoModel;

import java.util.List;
import java.util.Map;

/**
* 描述：包量套餐 Dao接口
* <AUTHOR>
* @date 2019/07/2
*/
public interface WechatPackageInfoDao {

    /**
    * 查询总数
    *
    * @param searchMap
    * @return
    */
    long selectListByCount(Map<String, Object> searchMap);

    /**
    * 分页查询
    *
    * @param searchMap
    * @return
    */
    List<WechatPackageInfoModel> selectListByPage(Map<String, Object> searchMap);

    /**
    * 保存
    *
    * @param model
    * @return
    */
    Integer insert(WechatPackageInfoModel model);

    /**
    * 更新
    *
    * @param model
    * @return
    */
    Integer update(WechatPackageInfoModel model);

    /**
    * 删除
    *
    * @param id
    * @return
    */
    Integer delete(Integer id);

    /**
    * 获取
    *
    * @param id
    * @return
    */
    WechatPackageInfoModel getById(Integer id);

}