<svg aria-roledescription="flowchart-v2" role="graphics-document document" viewBox="0 0 10368.0390625 26193.421875" style="max-width: 10368px; background-color: white;" class="flowchart" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns="http://www.w3.org/2000/svg" width="100%" id="my-svg"><style>#my-svg{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;fill:#333;}@keyframes edge-animation-frame{from{stroke-dashoffset:0;}}@keyframes dash{to{stroke-dashoffset:0;}}#my-svg .edge-animation-slow{stroke-dasharray:9,5!important;stroke-dashoffset:900;animation:dash 50s linear infinite;stroke-linecap:round;}#my-svg .edge-animation-fast{stroke-dasharray:9,5!important;stroke-dashoffset:900;animation:dash 20s linear infinite;stroke-linecap:round;}#my-svg .error-icon{fill:#552222;}#my-svg .error-text{fill:#552222;stroke:#552222;}#my-svg .edge-thickness-normal{stroke-width:1px;}#my-svg .edge-thickness-thick{stroke-width:3.5px;}#my-svg .edge-pattern-solid{stroke-dasharray:0;}#my-svg .edge-thickness-invisible{stroke-width:0;fill:none;}#my-svg .edge-pattern-dashed{stroke-dasharray:3;}#my-svg .edge-pattern-dotted{stroke-dasharray:2;}#my-svg .marker{fill:#333333;stroke:#333333;}#my-svg .marker.cross{stroke:#333333;}#my-svg svg{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;}#my-svg p{margin:0;}#my-svg .label{font-family:"trebuchet ms",verdana,arial,sans-serif;color:#333;}#my-svg .cluster-label text{fill:#333;}#my-svg .cluster-label span{color:#333;}#my-svg .cluster-label span p{background-color:transparent;}#my-svg .label text,#my-svg span{fill:#333;color:#333;}#my-svg .node rect,#my-svg .node circle,#my-svg .node ellipse,#my-svg .node polygon,#my-svg .node path{fill:#ECECFF;stroke:#9370DB;stroke-width:1px;}#my-svg .rough-node .label text,#my-svg .node .label text,#my-svg .image-shape .label,#my-svg .icon-shape .label{text-anchor:middle;}#my-svg .node .katex path{fill:#000;stroke:#000;stroke-width:1px;}#my-svg .rough-node .label,#my-svg .node .label,#my-svg .image-shape .label,#my-svg .icon-shape .label{text-align:center;}#my-svg .node.clickable{cursor:pointer;}#my-svg .root .anchor path{fill:#333333!important;stroke-width:0;stroke:#333333;}#my-svg .arrowheadPath{fill:#333333;}#my-svg .edgePath .path{stroke:#333333;stroke-width:2.0px;}#my-svg .flowchart-link{stroke:#333333;fill:none;}#my-svg .edgeLabel{background-color:rgba(232,232,232, 0.8);text-align:center;}#my-svg .edgeLabel p{background-color:rgba(232,232,232, 0.8);}#my-svg .edgeLabel rect{opacity:0.5;background-color:rgba(232,232,232, 0.8);fill:rgba(232,232,232, 0.8);}#my-svg .labelBkg{background-color:rgba(232, 232, 232, 0.5);}#my-svg .cluster rect{fill:#ffffde;stroke:#aaaa33;stroke-width:1px;}#my-svg .cluster text{fill:#333;}#my-svg .cluster span{color:#333;}#my-svg div.mermaidTooltip{position:absolute;text-align:center;max-width:200px;padding:2px;font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:12px;background:hsl(80, 100%, 96.2745098039%);border:1px solid #aaaa33;border-radius:2px;pointer-events:none;z-index:100;}#my-svg .flowchartTitleText{text-anchor:middle;font-size:18px;fill:#333;}#my-svg rect.text{fill:none;stroke-width:0;}#my-svg .icon-shape,#my-svg .image-shape{background-color:rgba(232,232,232, 0.8);text-align:center;}#my-svg .icon-shape p,#my-svg .image-shape p{background-color:rgba(232,232,232, 0.8);padding:2px;}#my-svg .icon-shape rect,#my-svg .image-shape rect{opacity:0.5;background-color:rgba(232,232,232, 0.8);fill:rgba(232,232,232, 0.8);}#my-svg .label-icon{display:inline-block;height:1em;overflow:visible;vertical-align:-0.125em;}#my-svg .node .label-icon path{fill:currentColor;stroke:revert;stroke-width:revert;}#my-svg :root{--mermaid-font-family:"trebuchet ms",verdana,arial,sans-serif;}</style><g><marker orient="auto" markerHeight="8" markerWidth="8" markerUnits="userSpaceOnUse" refY="5" refX="5" viewBox="0 0 10 10" class="marker flowchart-v2" id="my-svg_flowchart-v2-pointEnd"><path style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 0 0 L 10 5 L 0 10 z"/></marker><marker orient="auto" markerHeight="8" markerWidth="8" markerUnits="userSpaceOnUse" refY="5" refX="4.5" viewBox="0 0 10 10" class="marker flowchart-v2" id="my-svg_flowchart-v2-pointStart"><path style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 0 5 L 10 10 L 10 0 z"/></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5" refX="11" viewBox="0 0 10 10" class="marker flowchart-v2" id="my-svg_flowchart-v2-circleEnd"><circle style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" r="5" cy="5" cx="5"/></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5" refX="-1" viewBox="0 0 10 10" class="marker flowchart-v2" id="my-svg_flowchart-v2-circleStart"><circle style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" r="5" cy="5" cx="5"/></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5.2" refX="12" viewBox="0 0 11 11" class="marker cross flowchart-v2" id="my-svg_flowchart-v2-crossEnd"><path style="stroke-width: 2; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 1,1 l 9,9 M 10,1 l -9,9"/></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5.2" refX="-1" viewBox="0 0 11 11" class="marker cross flowchart-v2" id="my-svg_flowchart-v2-crossStart"><path style="stroke-width: 2; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 1,1 l 9,9 M 10,1 l -9,9"/></marker><g class="root"><g class="clusters"><g data-look="classic" id="数据库查询详情" class="cluster"><rect height="410.75" width="3003.59375" y="3472.78125" x="4612.6796875" style=""/><g transform="translate(6058.4765625, 3472.78125)" class="cluster-label"><foreignObject height="24" width="112"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>数据库查询详情</p></span></div></foreignObject></g></g><g data-look="classic" id="终端奖励处理" class="cluster"><rect height="17937.75" width="2764.15234375" y="719.046875" x="1828.52734375" style=""/><g transform="translate(3138.603515625, 719.046875)" class="cluster-label"><foreignObject height="24" width="144"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>处理终端奖励子流程</p></span></div></foreignObject></g></g><g data-look="classic" id="经销商奖励处理" class="cluster"><rect height="23211.484375" width="2723.765625" y="2973.9375" x="7636.2734375" style=""/><g transform="translate(8918.15625, 2973.9375)" class="cluster-label"><foreignObject height="24" width="160"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>处理经销商奖励子流程</p></span></div></foreignObject></g></g></g><g class="edgePaths"><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_A_B_0" d="M4190.211,86L4190.211,90.167C4190.211,94.333,4190.211,102.667,4190.281,110.417C4190.351,118.167,4190.492,125.334,4190.562,128.917L4190.633,132.501"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_B_C_0" d="M4134.204,285.04L4114.983,300.541C4095.762,316.042,4057.321,347.045,4038.1,372.046C4018.879,397.047,4018.879,416.047,4018.879,425.547L4018.879,435.047"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_B_D_0" d="M4247.218,285.04L4266.272,300.541C4285.326,316.042,4323.435,347.045,4342.489,368.046C4361.543,389.047,4361.543,400.047,4361.543,405.547L4361.543,411.047"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_D_E_0" d="M4154.879,475.339L3907.596,486.457C3660.314,497.575,3165.749,519.811,2918.466,534.429C2671.184,549.047,2671.184,556.047,2671.184,559.547L2671.184,563.047"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_E_F_0" d="M2885.52,622.667L3437.429,634.564C3989.339,646.46,5093.158,670.254,5645.067,686.317C6196.977,702.38,6196.977,710.714,6196.977,720.38C6196.977,730.047,6196.977,741.047,6196.977,746.547L6196.977,752.047"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_D1_D2_0" d="M7935.836,3237.859L7935.836,3270.846C7935.836,3303.833,7935.836,3369.807,7935.836,3408.961C7935.836,3448.115,7935.836,3460.448,7935.916,3484.507C7935.997,3508.565,7936.157,3544.349,7936.238,3562.241L7936.318,3580.133"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_D2_D3_0" d="M7898.653,3735.497L7882.159,3760.17C7865.664,3784.842,7832.674,3834.187,7816.179,3865.026C7799.684,3895.865,7799.684,3908.198,7799.684,3950.803C7799.684,3993.409,7799.684,4066.286,7799.684,4102.725L7799.684,4139.164"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_D2_D4_0" d="M7976.864,3732.651L7995.718,3757.798C8014.572,3782.945,8052.28,3833.238,8071.134,3864.551C8089.988,3895.865,8089.988,3908.198,8092.039,3942.805C8094.09,3977.412,8098.191,4034.293,8100.242,4062.734L8102.293,4091.174"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_D4_D5_0" d="M8107.988,4245.164L8107.988,4274.27C8107.988,4303.375,8107.988,4361.586,8116.985,4414.9C8125.981,4468.214,8143.973,4516.631,8152.97,4540.839L8161.966,4565.047"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_D5_D6_0" d="M8191.23,4718.797L8191.23,4743.63C8191.23,4768.464,8191.23,4818.13,8191.23,4876.798C8191.23,4935.466,8191.23,5003.135,8191.23,5036.97L8191.23,5070.805"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_D6_D7_0" d="M8191.23,5200.805L8191.23,5233.306C8191.23,5265.807,8191.23,5330.81,8191.23,5374.811C8191.23,5418.813,8191.23,5441.813,8191.23,5453.313L8191.23,5464.813"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_D7_D8_0" d="M8191.23,5522.813L8191.23,5536.979C8191.23,5551.146,8191.23,5579.479,8191.23,5605.146C8191.23,5630.813,8191.23,5653.813,8191.23,5665.313L8191.23,5676.813"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_D8_D9_0" d="M8191.23,5734.813L8191.23,5744.979C8191.23,5755.146,8191.23,5775.479,8191.301,5789.229C8191.371,5802.979,8191.512,5810.146,8191.582,5813.73L8191.652,5817.313"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_D9_D10_0" d="M8085.855,6137.39L8061.783,6161.119C8037.712,6184.848,7989.569,6232.307,7965.497,6287.833C7941.426,6343.359,7941.426,6406.953,7941.426,6438.75L7941.426,6470.547"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_D9_D11_0" d="M8277.055,6157.941L8290.816,6178.245C8304.577,6198.549,8332.099,6239.157,8345.935,6265.045C8359.77,6290.932,8359.919,6302.099,8359.993,6307.683L8360.068,6313.266"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_D11_D12_0" d="M8262.24,6588.947L8236.836,6611.344C8211.432,6633.741,8160.624,6678.535,8135.22,6735.252C8109.816,6791.969,8109.816,6860.609,8109.816,6894.93L8109.816,6929.25"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_D11_D13_0" d="M8441.868,6605.081L8457.487,6624.789C8473.106,6644.497,8504.344,6683.912,8520.038,6709.204C8535.731,6734.495,8535.88,6745.662,8535.954,6751.245L8536.029,6756.828"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_D13_D14_0" d="M8433.475,7058.065L8408.859,7081.25C8384.243,7104.434,8335.01,7150.803,8310.394,7205.05C8285.777,7259.297,8285.777,7321.422,8285.777,7352.484L8285.777,7383.547"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_D13_D15_0" d="M8618.503,7078.251L8632.38,7098.071C8646.258,7117.891,8674.014,7157.532,8687.966,7182.935C8701.918,7208.339,8702.067,7219.505,8702.142,7225.089L8702.216,7230.672"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_D15_D16_0" d="M8605.822,7498.974L8580.179,7521.132C8554.536,7543.29,8503.25,7587.606,8477.608,7643.721C8451.965,7699.836,8451.965,7767.75,8451.965,7801.707L8451.965,7835.664"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_D15_D17_0" d="M8782.684,7515.007L8798.344,7534.493C8814.003,7553.979,8845.322,7592.95,8869.922,7629.427C8894.521,7665.903,8912.402,7699.885,8921.342,7716.875L8930.282,7733.866"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_D17_D18_0" d="M8878.791,7943.569L8836.799,7969.875C8794.806,7996.181,8710.821,8048.794,8668.828,8113.524C8626.836,8178.255,8626.836,8255.104,8626.836,8293.529L8626.836,8331.953"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_D17_D19_0" d="M9088.139,7976.896L9104.766,7997.648C9121.393,8018.4,9154.648,8059.903,9171.35,8086.238C9188.051,8112.573,9188.2,8123.74,9188.275,8129.323L9188.349,8134.907"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_D19_D20_0" d="M9188.402,8588L9188.319,8592.083C9188.236,8596.167,9188.069,8604.333,9188.066,8628.667C9188.064,8653,9188.225,8693.5,9188.306,8713.75L9188.386,8734"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_D20_D21_0" d="M9140.591,8840.189L9086.344,8870.908C9032.097,8901.626,8923.603,8963.063,8869.356,9005.282C8815.109,9047.5,8815.109,9070.5,8815.109,9082L8815.109,9093.5"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_D20_D22_0" d="M9221.588,8854.814L9244.016,8883.095C9266.444,8911.376,9311.3,8967.938,9333.728,9001.719C9356.156,9035.5,9356.156,9046.5,9356.156,9052L9356.156,9057.5"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_D22_D23_0" d="M9356.156,9187.5L9356.156,9191.667C9356.156,9195.833,9356.156,9204.167,9356.239,9833.922C9356.323,10463.677,9356.489,11714.854,9356.572,12340.443L9356.656,12966.031"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_D23_D24_0" d="M9348.253,13159.628L9289.771,13789.117C9231.29,14418.606,9114.326,15677.584,9055.845,16327.24C8997.363,16976.896,8997.363,17017.229,8997.363,17037.396L8997.363,17057.563"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_D23_D25_0" d="M9360.471,13164.217L9385.585,13792.941C9410.699,14421.665,9460.928,15679.114,9486.042,16322.005C9511.156,16964.896,9511.156,16993.229,9511.156,17007.396L9511.156,17021.563"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_D25_D26_0" d="M9511.156,17199.563L9511.156,17214.396C9511.156,17229.229,9511.156,17258.896,9511.156,17279.229C9511.156,17299.563,9511.156,17310.563,9511.156,17316.063L9511.156,17321.563"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_D26_D27_0" d="M9511.156,17475.563L9511.156,17479.729C9511.156,17483.896,9511.156,17492.229,9517.696,17500.226C9524.235,17508.222,9537.314,17515.882,9543.854,17519.711L9550.393,17523.541"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_D27_D28_0" d="M9640.93,17627.563L9640.93,17631.729C9640.93,17635.896,9640.93,17644.229,9641,17651.979C9641.07,17659.729,9641.211,17666.896,9641.281,17670.48L9641.351,17674.063"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_D28_D29_0" d="M9554.329,17817.197L9485.002,17837.797C9415.674,17858.397,9277.019,17899.597,9207.691,17931.697C9138.363,17963.797,9138.363,17986.797,9138.363,17998.297L9138.363,18009.797"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_D28_D30_0" d="M9696.217,17849.51L9710.502,17864.724C9724.788,17879.939,9753.359,17910.368,9772.307,17931.235C9791.256,17952.102,9800.583,17963.407,9805.246,17969.059L9809.909,17974.711"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_D30_D31_0" d="M9864.43,18103.797L9864.43,18107.964C9864.43,18112.13,9864.43,18120.464,9864.43,18128.13C9864.43,18135.797,9864.43,18142.797,9864.43,18146.297L9864.43,18149.797"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_D31_D32_0" d="M9864.43,18231.797L9864.43,18235.964C9864.43,18240.13,9864.43,18248.464,9864.5,18256.214C9864.57,18263.964,9864.711,18271.131,9864.781,18274.714L9864.851,18278.298"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_D32_D33_0" d="M9738.44,18505.807L9672.521,18530.972C9606.603,18556.137,9474.766,18606.467,9408.848,18637.799C9342.93,18669.13,9342.93,18681.464,9342.93,18693.13C9342.93,18704.797,9342.93,18715.797,9342.93,18721.297L9342.93,18726.797"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_D32_D34_0" d="M9953.417,18543.809L9972.68,18562.641C9991.944,18581.472,10030.47,18619.134,10049.733,18644.132C10068.996,18669.13,10068.996,18681.464,10068.996,18695.13C10068.996,18708.797,10068.996,18723.797,10068.996,18731.297L10068.996,18738.797"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_D33_D35_0" d="M9428.869,18832.797L9435.89,18836.964C9442.911,18841.13,9456.954,18849.464,9549.425,19387.107C9641.896,19924.751,9812.795,20991.705,9898.245,21525.183L9983.695,22058.66"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_D34_D35_0" d="M10068.996,18820.797L10068.996,18826.964C10068.996,18833.13,10068.996,18845.464,10056.461,19385.099C10043.927,19924.735,10018.858,20991.673,10006.323,21525.142L9993.788,22058.61"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_D35_D36_0" d="M9992.496,22164.609L9992.496,22698.745C9992.496,23232.88,9992.496,24301.151,9992.566,24838.87C9992.637,25376.589,9992.777,25383.756,9992.847,25387.339L9992.918,25390.923"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_D36_D37_0" d="M10038.8,25627.118L10045.499,25640.835C10052.199,25654.553,10065.597,25681.987,10079.897,25701.468C10094.197,25720.95,10109.398,25732.477,10116.999,25738.241L10124.6,25744.005"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_D36_D38_0" d="M9883.992,25563.918L9795.482,25588.169C9706.972,25612.419,9529.951,25660.921,9441.44,25699.838C9352.93,25738.755,9352.93,25768.089,9352.93,25795.422C9352.93,25822.755,9352.93,25848.089,9459.33,25868.078C9565.73,25888.066,9778.53,25902.711,9884.93,25910.033L9991.33,25917.356"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_D37_D38_0" d="M10195.039,25848.422L10195.039,25852.589C10195.039,25856.755,10195.039,25865.089,10188.679,25873.078C10182.32,25881.068,10169.6,25888.715,10163.24,25892.538L10156.881,25896.361"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_D38_D39_0" d="M10108.539,25952.422L10108.539,25956.589C10108.539,25960.755,10108.539,25969.089,10108.539,25976.755C10108.539,25984.422,10108.539,25991.422,10108.539,25994.922L10108.539,25998.422"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_D39_D40_0" d="M10108.539,26056.422L10108.539,26060.589C10108.539,26064.755,10108.539,26073.089,10108.539,26080.755C10108.539,26088.422,10108.539,26095.422,10108.539,26098.922L10108.539,26102.422"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_E1_E2_0" d="M2155.488,822.047L2155.488,826.214C2155.488,830.38,2155.488,838.714,2155.559,846.464C2155.629,854.214,2155.769,861.381,2155.84,864.964L2155.91,868.548"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_E2_E3_0" d="M2103.597,1009.202L2084.987,1024.017C2066.377,1038.833,2029.157,1068.463,2010.547,1096.778C1991.938,1125.094,1991.938,1152.094,1991.938,1165.594L1991.938,1179.094"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_E2_E4_0" d="M2206.994,1010.588L2224.102,1025.173C2241.209,1039.757,2275.425,1068.925,2292.533,1089.01C2309.641,1109.094,2309.641,1120.094,2309.641,1125.594L2309.641,1131.094"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_E4_E5_0" d="M2309.641,1285.094L2309.641,1289.26C2309.641,1293.427,2309.641,1301.76,2309.641,1309.427C2309.641,1317.094,2309.641,1324.094,2309.641,1327.594L2309.641,1331.094"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_E5_E6_0" d="M2309.641,1485.094L2309.641,1489.26C2309.641,1493.427,2309.641,1501.76,2309.641,1509.427C2309.641,1517.094,2309.641,1524.094,2309.641,1527.594L2309.641,1531.094"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_E6_E7_0" d="M2309.641,1661.094L2309.641,1665.26C2309.641,1669.427,2309.641,1677.76,2309.641,1685.427C2309.641,1693.094,2309.641,1700.094,2309.641,1703.594L2309.641,1707.094"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_E7_E8_0" d="M2309.641,1789.094L2309.641,1793.26C2309.641,1797.427,2309.641,1805.76,2309.641,1813.427C2309.641,1821.094,2309.641,1828.094,2309.641,1831.594L2309.641,1835.094"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_E8_E9_0" d="M2309.641,1893.094L2309.641,1897.26C2309.641,1901.427,2309.641,1909.76,2309.641,1917.427C2309.641,1925.094,2309.641,1932.094,2309.641,1935.594L2309.641,1939.094"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_E9_E10_0" d="M2309.641,1997.094L2309.641,2001.26C2309.641,2005.427,2309.641,2013.76,2309.711,2021.511C2309.781,2029.261,2309.922,2036.428,2309.992,2040.011L2310.062,2043.595"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_E10_E11_0" d="M2223.913,2370.647L2208.874,2391.102C2193.835,2411.557,2163.757,2452.466,2148.719,2504.717C2133.68,2556.969,2133.68,2620.563,2133.68,2652.359L2133.68,2684.156"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_E10_E12_0" d="M2394.182,2372.834L2408.157,2392.924C2422.132,2413.014,2450.081,2453.195,2464.131,2478.868C2478.18,2504.542,2478.329,2515.709,2478.403,2521.292L2478.478,2526.875"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_E12_E13_0" d="M2396.784,2818.69L2380.998,2838.398C2365.213,2858.106,2333.642,2897.522,2317.856,2923.396C2302.07,2949.271,2302.07,2961.604,2302.07,3000.091C2302.07,3038.578,2302.07,3103.219,2302.07,3135.539L2302.07,3167.859"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_E12_E14_0" d="M2560.278,2818.69L2575.897,2838.398C2591.516,2858.106,2622.754,2897.522,2638.373,2923.396C2653.992,2949.271,2653.992,2961.604,2654.062,2971.354C2654.133,2981.104,2654.273,2988.271,2654.344,2991.855L2654.414,2995.438"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_E14_E15_0" d="M2569.29,3314.079L2554.08,3334.363C2538.871,3354.647,2508.451,3395.214,2493.241,3421.664C2478.031,3448.115,2478.031,3460.448,2478.031,3495.677C2478.031,3530.906,2478.031,3589.031,2478.031,3618.094L2478.031,3647.156"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_E14_E16_0" d="M2736.913,3316.861L2750.791,3336.681C2764.668,3356.501,2792.424,3396.141,2806.302,3422.128C2820.18,3448.115,2820.18,3460.448,2820.25,3470.198C2820.32,3479.948,2820.461,3487.115,2820.531,3490.699L2820.601,3494.282"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_E16_E17_0" d="M2737.449,3775.8L2721.91,3793.755C2706.372,3811.711,2675.295,3847.621,2659.757,3871.743C2644.219,3895.865,2644.219,3908.198,2644.219,3950.803C2644.219,3993.409,2644.219,4066.286,2644.219,4102.725L2644.219,4139.164"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_E16_E18_0" d="M2905.503,3774.208L2921.669,3792.428C2937.834,3810.649,2970.165,3847.09,2986.331,3871.477C3002.496,3895.865,3002.496,3908.198,3002.987,3921.831C3003.478,3935.465,3004.46,3950.398,3004.951,3957.865L3005.442,3965.332"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_E18_E19_0" d="M2925.111,4291.412L2908.682,4312.81C2892.253,4334.207,2859.394,4377.002,2842.965,4430.566C2826.535,4484.13,2826.535,4548.464,2826.535,4580.63L2826.535,4612.797"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_E18_E20_0" d="M3102.986,4297.307L3116.821,4317.722C3130.656,4338.137,3158.326,4378.967,3181.462,4416.144C3204.598,4453.321,3223.201,4486.844,3232.502,4503.606L3241.803,4520.368"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_E20_E21_0" d="M3201.203,4722.188L3167.009,4746.456C3132.814,4770.724,3064.425,4819.261,3030.23,4883.363C2996.035,4947.466,2996.035,5027.135,2996.035,5066.97L2996.035,5106.805"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_E20_E22_0" d="M3396.742,4744.867L3414.338,4765.356C3431.933,4785.844,3467.125,4826.82,3484.795,4852.892C3502.465,4878.964,3502.614,4890.13,3502.689,4895.714L3502.763,4901.297"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_E22_E23_0" d="M3502.816,5371.313L3502.733,5375.396C3502.65,5379.479,3502.483,5387.646,3502.47,5395.313C3502.457,5402.979,3502.597,5410.146,3502.668,5413.73L3502.738,5417.313"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_E23_E24_0" d="M3447.545,5516.041L3404.377,5531.336C3361.21,5546.632,3274.874,5577.222,3231.707,5604.017C3188.539,5630.813,3188.539,5653.813,3188.539,5665.313L3188.539,5676.813"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_E23_E25_0" d="M3547.844,5526.285L3568.298,5539.873C3588.752,5553.461,3629.661,5580.637,3650.116,5599.725C3670.57,5618.813,3670.57,5629.813,3670.57,5635.313L3670.57,5640.813"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_E25_E26_0" d="M3670.57,5770.813L3670.57,5774.979C3670.57,5779.146,3670.57,5787.479,3670.651,5813.892C3670.732,5840.305,3670.894,5884.797,3670.975,5907.043L3671.056,5929.289"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_E26_E27_0" d="M3616.889,6077.108L3575.873,6110.884C3534.857,6144.66,3452.825,6212.213,3411.809,6273.786C3370.793,6335.359,3370.793,6390.953,3370.793,6418.75L3370.793,6446.547"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_E26_E28_0" d="M3709.149,6093.21L3728.553,6124.303C3747.956,6155.395,3786.763,6217.58,3806.167,6270.47C3825.57,6323.359,3825.57,6366.953,3825.57,6388.75L3825.57,6410.547"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_E28_E29_0" d="M3825.57,6588.547L3825.57,6611.01C3825.57,6633.474,3825.57,6678.401,3842.768,6731.271C3859.967,6784.142,3894.363,6844.955,3911.561,6875.362L3928.759,6905.768"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_E29_E30_0" d="M3959.574,7011.25L3959.574,7042.237C3959.574,7073.224,3959.574,7135.198,3959.654,7182.978C3959.735,7230.758,3959.895,7264.344,3959.975,7281.137L3960.055,7297.93"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_E30_E31_0" d="M3883.924,7452.014L3821.902,7481.999C3759.88,7511.983,3635.837,7571.953,3573.815,7635.894C3511.793,7699.836,3511.793,7767.75,3511.793,7801.707L3511.793,7835.664"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_E30_E32_0" d="M4007.44,7480.798L4025.562,7505.985C4043.683,7531.173,4079.925,7581.547,4107.183,7632.729C4134.44,7683.911,4152.711,7735.901,4161.847,7761.896L4170.983,7787.89"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_E32_E33_0" d="M4198.668,7941.664L4198.668,7968.288C4198.668,7994.911,4198.668,8048.159,4198.668,8111.207C4198.668,8174.255,4198.668,8247.104,4198.668,8283.529L4198.668,8319.953"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_E33_E34_0" d="M4198.668,8401.953L4198.668,8437.044C4198.668,8472.135,4198.668,8542.318,4198.738,8580.992C4198.808,8619.667,4198.949,8626.834,4199.019,8630.417L4199.09,8634.001"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_E34_E35_0" d="M4078.357,8867.189L4019.634,8893.407C3960.912,8919.626,3843.468,8972.063,3784.746,9005.781C3726.023,9039.5,3726.023,9054.5,3726.023,9062L3726.023,9069.5"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_E34_E36_0" d="M4283.82,8903.347L4302.735,8923.54C4321.649,8943.732,4359.477,8984.116,4378.391,9013.808C4397.305,9043.5,4397.305,9062.5,4397.305,9072L4397.305,9081.5"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_E35_E37_0" d="M3791.012,9175.5L3798.87,9181.667C3806.728,9187.833,3822.444,9200.167,3907.708,9837.843C3992.972,10475.52,4147.784,11738.541,4225.19,12370.051L4302.596,13001.561"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_E36_E37_0" d="M4397.305,9163.5L4397.305,9171.667C4397.305,9179.833,4397.305,9196.167,4383.139,9835.839C4368.972,10475.511,4340.64,11738.522,4326.474,12370.027L4312.308,13001.532"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_E37_E38_0" d="M4310.805,13131.531L4310.805,13765.703C4310.805,14399.875,4310.805,15668.219,4310.879,16307.974C4310.954,16947.729,4311.102,16958.896,4311.177,16964.479L4311.251,16970.063"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_E38_E39_0" d="M4357.108,17206.259L4363.808,17219.976C4370.507,17233.693,4383.906,17261.128,4390.605,17284.345C4397.305,17307.563,4397.305,17326.563,4397.305,17336.063L4397.305,17345.563"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_E38_E40_0" d="M4204.461,17145.219L4124.721,17169.11C4044.982,17193,3885.503,17240.781,3805.763,17283.339C3726.023,17325.896,3726.023,17363.229,3726.023,17398.563C3726.023,17433.896,3726.023,17467.229,3803.956,17494.024C3881.889,17520.819,4037.754,17541.076,4115.687,17551.204L4193.619,17561.333"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_E39_E40_0" d="M4397.305,17451.563L4397.305,17459.729C4397.305,17467.896,4397.305,17484.229,4388.511,17500.122C4379.716,17516.016,4362.128,17531.469,4353.334,17539.196L4344.54,17546.922"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_E40_E41_0" d="M4310.805,17603.563L4310.805,17611.729C4310.805,17619.896,4310.805,17636.229,4310.805,17660.249C4310.805,17684.268,4310.805,17715.974,4310.805,17731.827L4310.805,17747.68"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_E41_E42_0" d="M4310.805,17829.68L4310.805,17848.199C4310.805,17866.719,4310.805,17903.758,4310.805,17929.777C4310.805,17955.797,4310.805,17970.797,4310.805,17978.297L4310.805,17985.797"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_E42_E43_0" d="M4310.805,18091.797L4310.805,18097.964C4310.805,18104.13,4310.805,18116.464,4310.805,18128.13C4310.805,18139.797,4310.805,18150.797,4310.805,18156.297L4310.805,18161.797"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_E43_E44_0" d="M4310.805,18219.797L4310.805,18225.964C4310.805,18232.13,4310.805,18244.464,4310.805,18278.797C4310.805,18313.13,4310.805,18369.464,4310.805,18397.63L4310.805,18425.797"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-dotted edge-thickness-normal edge-pattern-solid flowchart-link" id="L_DB1_D4_0" d="M5880.99,3765.156L5897.988,3784.885C5914.987,3804.615,5948.984,3844.073,6582.651,3869.969C7216.318,3895.865,8449.655,3908.198,8826.395,3952.392C9203.136,3996.587,8723.28,4072.642,8483.351,4110.67L8243.423,4148.698"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-dotted edge-thickness-normal edge-pattern-solid flowchart-link" id="L_DB2_D5_0" d="M6377.491,3765.156L6359.358,3784.885C6341.224,3804.615,6304.958,3844.073,6871.415,3869.969C7437.872,3895.865,8607.053,3908.198,9191.644,3955.97C9776.234,4003.742,9776.234,4086.953,9776.234,4170.164C9776.234,4253.375,9776.234,4336.586,9538.981,4411.721C9301.728,4486.856,8827.221,4553.916,8589.968,4587.445L8352.715,4620.975"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-dotted edge-thickness-normal edge-pattern-solid flowchart-link" id="L_DB3_D27_0" d="M6228.551,3741.156L6269.464,3764.885C6310.377,3788.615,6392.202,3836.073,7028.943,3865.969C7665.684,3895.865,8857.34,3908.198,9453.168,3955.97C10048.996,4003.742,10048.996,4086.953,10048.996,4170.164C10048.996,4253.375,10048.996,4336.586,10048.996,4415.525C10048.996,4494.464,10048.996,4569.13,10048.996,4643.797C10048.996,4718.464,10048.996,4793.13,10048.996,4875.465C10048.996,4957.799,10048.996,5047.802,10048.996,5135.805C10048.996,5223.807,10048.996,5309.81,10048.996,5369.478C10048.996,5429.146,10048.996,5462.479,10048.996,5497.813C10048.996,5533.146,10048.996,5570.479,10048.996,5605.813C10048.996,5641.146,10048.996,5674.479,10048.996,5705.813C10048.996,5737.146,10048.996,5766.479,10048.996,5820.475C10048.996,5874.471,10048.996,5953.13,10048.996,6033.789C10048.996,6114.448,10048.996,6197.107,10048.996,6275.4C10048.996,6353.693,10048.996,6427.62,10048.996,6501.547C10048.996,6575.474,10048.996,6649.401,10048.996,6725.852C10048.996,6802.302,10048.996,6881.276,10048.996,6960.25C10048.996,7039.224,10048.996,7118.198,10048.996,7193.914C10048.996,7269.63,10048.996,7342.089,10048.996,7414.547C10048.996,7487.005,10048.996,7559.464,10048.996,7634.816C10048.996,7710.169,10048.996,7788.417,10048.996,7866.664C10048.996,7944.911,10048.996,8023.159,10048.996,8105.874C10048.996,8188.589,10048.996,8275.771,10048.996,8360.953C10048.996,8446.135,10048.996,8529.318,10048.996,8604.242C10048.996,8679.167,10048.996,8745.833,10048.996,8814.5C10048.996,8883.167,10048.996,8953.833,10048.996,9005.833C10048.996,9057.833,10048.996,9091.167,10048.996,9122.5C10048.996,9153.833,10048.996,9183.167,10048.996,9840.505C10048.996,10497.844,10048.996,11783.188,10048.996,13070.531C10048.996,14357.875,10048.996,15647.219,10048.996,16321.224C10048.996,16995.229,10048.996,17053.896,10048.996,17112.563C10048.996,17171.229,10048.996,17229.896,10048.996,17277.896C10048.996,17325.896,10048.996,17363.229,10048.996,17398.563C10048.996,17433.896,10048.996,17467.229,10003.307,17492.405C9957.618,17517.581,9866.24,17534.6,9820.551,17543.109L9774.862,17551.618"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-dotted edge-thickness-normal edge-pattern-solid flowchart-link" id="L_DB3_E29_0" d="M6015.788,3741.156L5976.563,3764.885C5937.338,3788.615,5858.888,3836.073,5585.807,3865.969C5312.727,3895.865,4845.016,3908.198,4611.16,3955.97C4377.305,4003.742,4377.305,4086.953,4377.305,4170.164C4377.305,4253.375,4377.305,4336.586,4377.305,4415.525C4377.305,4494.464,4377.305,4569.13,4377.305,4643.797C4377.305,4718.464,4377.305,4793.13,4377.305,4875.465C4377.305,4957.799,4377.305,5047.802,4377.305,5135.805C4377.305,5223.807,4377.305,5309.81,4377.305,5369.478C4377.305,5429.146,4377.305,5462.479,4377.305,5497.813C4377.305,5533.146,4377.305,5570.479,4377.305,5605.813C4377.305,5641.146,4377.305,5674.479,4377.305,5705.813C4377.305,5737.146,4377.305,5766.479,4377.305,5820.475C4377.305,5874.471,4377.305,5953.13,4377.305,6033.789C4377.305,6114.448,4377.305,6197.107,4377.305,6275.4C4377.305,6353.693,4377.305,6427.62,4377.305,6501.547C4377.305,6575.474,4377.305,6649.401,4323.25,6717.023C4269.195,6784.644,4161.085,6845.96,4107.03,6876.619L4052.975,6907.277"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-dotted edge-thickness-normal edge-pattern-solid flowchart-link" id="L_DB4_D30_0" d="M7136.273,3753.156L7136.273,3774.885C7136.273,3796.615,7136.273,3840.073,7637.144,3867.969C8138.014,3895.865,9139.755,3908.198,9640.626,3955.97C10141.496,4003.742,10141.496,4086.953,10141.496,4170.164C10141.496,4253.375,10141.496,4336.586,10141.496,4415.525C10141.496,4494.464,10141.496,4569.13,10141.496,4643.797C10141.496,4718.464,10141.496,4793.13,10141.496,4875.465C10141.496,4957.799,10141.496,5047.802,10141.496,5135.805C10141.496,5223.807,10141.496,5309.81,10141.496,5369.478C10141.496,5429.146,10141.496,5462.479,10141.496,5497.813C10141.496,5533.146,10141.496,5570.479,10141.496,5605.813C10141.496,5641.146,10141.496,5674.479,10141.496,5705.813C10141.496,5737.146,10141.496,5766.479,10141.496,5820.475C10141.496,5874.471,10141.496,5953.13,10141.496,6033.789C10141.496,6114.448,10141.496,6197.107,10141.496,6275.4C10141.496,6353.693,10141.496,6427.62,10141.496,6501.547C10141.496,6575.474,10141.496,6649.401,10141.496,6725.852C10141.496,6802.302,10141.496,6881.276,10141.496,6960.25C10141.496,7039.224,10141.496,7118.198,10141.496,7193.914C10141.496,7269.63,10141.496,7342.089,10141.496,7414.547C10141.496,7487.005,10141.496,7559.464,10141.496,7634.816C10141.496,7710.169,10141.496,7788.417,10141.496,7866.664C10141.496,7944.911,10141.496,8023.159,10141.496,8105.874C10141.496,8188.589,10141.496,8275.771,10141.496,8360.953C10141.496,8446.135,10141.496,8529.318,10141.496,8604.242C10141.496,8679.167,10141.496,8745.833,10141.496,8814.5C10141.496,8883.167,10141.496,8953.833,10141.496,9005.833C10141.496,9057.833,10141.496,9091.167,10141.496,9122.5C10141.496,9153.833,10141.496,9183.167,10141.496,9840.505C10141.496,10497.844,10141.496,11783.188,10141.496,13070.531C10141.496,14357.875,10141.496,15647.219,10141.496,16321.224C10141.496,16995.229,10141.496,17053.896,10141.496,17112.563C10141.496,17171.229,10141.496,17229.896,10141.496,17277.896C10141.496,17325.896,10141.496,17363.229,10141.496,17398.563C10141.496,17433.896,10141.496,17467.229,10141.496,17496.563C10141.496,17525.896,10141.496,17551.229,10141.496,17576.563C10141.496,17601.896,10141.496,17627.229,10141.496,17662.915C10141.496,17698.602,10141.496,17744.641,10141.496,17792.68C10141.496,17840.719,10141.496,17890.758,10117.612,17924.398C10093.728,17958.038,10045.96,17975.278,10022.076,17983.898L9998.192,17992.519"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-dotted edge-thickness-normal edge-pattern-solid flowchart-link" id="L_DB5_E32_0" d="M5604.759,3765.156L5637.372,3784.885C5669.985,3804.615,5735.211,3844.073,5546.052,3869.969C5356.893,3895.865,4913.349,3908.198,4691.577,3955.97C4469.805,4003.742,4469.805,4086.953,4469.805,4170.164C4469.805,4253.375,4469.805,4336.586,4469.805,4415.525C4469.805,4494.464,4469.805,4569.13,4469.805,4643.797C4469.805,4718.464,4469.805,4793.13,4469.805,4875.465C4469.805,4957.799,4469.805,5047.802,4469.805,5135.805C4469.805,5223.807,4469.805,5309.81,4469.805,5369.478C4469.805,5429.146,4469.805,5462.479,4469.805,5497.813C4469.805,5533.146,4469.805,5570.479,4469.805,5605.813C4469.805,5641.146,4469.805,5674.479,4469.805,5705.813C4469.805,5737.146,4469.805,5766.479,4469.805,5820.475C4469.805,5874.471,4469.805,5953.13,4469.805,6033.789C4469.805,6114.448,4469.805,6197.107,4469.805,6275.4C4469.805,6353.693,4469.805,6427.62,4469.805,6501.547C4469.805,6575.474,4469.805,6649.401,4469.805,6725.852C4469.805,6802.302,4469.805,6881.276,4469.805,6960.25C4469.805,7039.224,4469.805,7118.198,4469.805,7193.914C4469.805,7269.63,4469.805,7342.089,4469.805,7414.547C4469.805,7487.005,4469.805,7559.464,4439.557,7621.88C4409.31,7684.297,4348.815,7736.671,4318.568,7762.859L4288.32,7789.046"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-dotted edge-thickness-normal edge-pattern-solid flowchart-link" id="L_DB6_D17_0" d="M6664.977,3761.182L6629.818,3781.573C6594.66,3801.965,6524.344,3842.748,7065.051,3869.306C7605.759,3895.865,8757.491,3908.198,9333.357,3955.97C9909.223,4003.742,9909.223,4086.953,9909.223,4170.164C9909.223,4253.375,9909.223,4336.586,9909.223,4415.525C9909.223,4494.464,9909.223,4569.13,9909.223,4643.797C9909.223,4718.464,9909.223,4793.13,9909.223,4875.465C9909.223,4957.799,9909.223,5047.802,9909.223,5135.805C9909.223,5223.807,9909.223,5309.81,9909.223,5369.478C9909.223,5429.146,9909.223,5462.479,9909.223,5497.813C9909.223,5533.146,9909.223,5570.479,9909.223,5605.813C9909.223,5641.146,9909.223,5674.479,9909.223,5705.813C9909.223,5737.146,9909.223,5766.479,9909.223,5820.475C9909.223,5874.471,9909.223,5953.13,9909.223,6033.789C9909.223,6114.448,9909.223,6197.107,9909.223,6275.4C9909.223,6353.693,9909.223,6427.62,9909.223,6501.547C9909.223,6575.474,9909.223,6649.401,9909.223,6725.852C9909.223,6802.302,9909.223,6881.276,9909.223,6960.25C9909.223,7039.224,9909.223,7118.198,9909.223,7193.914C9909.223,7269.63,9909.223,7342.089,9909.223,7414.547C9909.223,7487.005,9909.223,7559.464,9784.549,7627.972C9659.875,7696.481,9410.527,7761.039,9285.854,7793.319L9161.18,7825.598"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-dotted edge-thickness-normal edge-pattern-solid flowchart-link" id="L_DB7_E18_0" d="M4793.719,3777.156L4793.719,3794.885C4793.719,3812.615,4793.719,3848.073,4678.013,3871.969C4562.307,3895.865,4330.896,3908.198,4064.225,3949.265C3797.554,3990.331,3495.625,4060.132,3344.66,4095.032L3193.695,4129.932"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-dotted edge-thickness-normal edge-pattern-solid flowchart-link" id="L_DB8_E20_0" d="M5119.758,3777.156L5119.758,3794.885C5119.758,3812.615,5119.758,3848.073,4972.015,3871.969C4824.272,3895.865,4528.786,3908.198,4381.044,3955.97C4233.301,4003.742,4233.301,4086.953,4233.301,4170.164C4233.301,4253.375,4233.301,4336.586,4105.2,4409.366C3977.099,4482.147,3720.897,4544.497,3592.796,4575.672L3464.695,4606.847"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-dotted edge-thickness-normal edge-pattern-solid flowchart-link" id="L_DB9_D37_0" d="M7451.273,3741.156L7451.273,3764.885C7451.273,3788.615,7451.273,3836.073,7920.651,3865.969C8390.029,3895.865,9328.784,3908.198,9798.161,3955.97C10267.539,4003.742,10267.539,4086.953,10267.539,4170.164C10267.539,4253.375,10267.539,4336.586,10267.539,4415.525C10267.539,4494.464,10267.539,4569.13,10267.539,4643.797C10267.539,4718.464,10267.539,4793.13,10267.539,4875.465C10267.539,4957.799,10267.539,5047.802,10267.539,5135.805C10267.539,5223.807,10267.539,5309.81,10267.539,5369.478C10267.539,5429.146,10267.539,5462.479,10267.539,5497.813C10267.539,5533.146,10267.539,5570.479,10267.539,5605.813C10267.539,5641.146,10267.539,5674.479,10267.539,5705.813C10267.539,5737.146,10267.539,5766.479,10267.539,5820.475C10267.539,5874.471,10267.539,5953.13,10267.539,6033.789C10267.539,6114.448,10267.539,6197.107,10267.539,6275.4C10267.539,6353.693,10267.539,6427.62,10267.539,6501.547C10267.539,6575.474,10267.539,6649.401,10267.539,6725.852C10267.539,6802.302,10267.539,6881.276,10267.539,6960.25C10267.539,7039.224,10267.539,7118.198,10267.539,7193.914C10267.539,7269.63,10267.539,7342.089,10267.539,7414.547C10267.539,7487.005,10267.539,7559.464,10267.539,7634.816C10267.539,7710.169,10267.539,7788.417,10267.539,7866.664C10267.539,7944.911,10267.539,8023.159,10267.539,8105.874C10267.539,8188.589,10267.539,8275.771,10267.539,8360.953C10267.539,8446.135,10267.539,8529.318,10267.539,8604.242C10267.539,8679.167,10267.539,8745.833,10267.539,8814.5C10267.539,8883.167,10267.539,8953.833,10267.539,9005.833C10267.539,9057.833,10267.539,9091.167,10267.539,9122.5C10267.539,9153.833,10267.539,9183.167,10267.539,9840.505C10267.539,10497.844,10267.539,11783.188,10267.539,13070.531C10267.539,14357.875,10267.539,15647.219,10267.539,16321.224C10267.539,16995.229,10267.539,17053.896,10267.539,17112.563C10267.539,17171.229,10267.539,17229.896,10267.539,17277.896C10267.539,17325.896,10267.539,17363.229,10267.539,17398.563C10267.539,17433.896,10267.539,17467.229,10267.539,17496.563C10267.539,17525.896,10267.539,17551.229,10267.539,17576.563C10267.539,17601.896,10267.539,17627.229,10267.539,17662.915C10267.539,17698.602,10267.539,17744.641,10267.539,17792.68C10267.539,17840.719,10267.539,17890.758,10267.539,17932.444C10267.539,17974.13,10267.539,18007.464,10267.539,18038.797C10267.539,18070.13,10267.539,18099.464,10267.539,18124.797C10267.539,18150.13,10267.539,18171.464,10267.539,18192.797C10267.539,18214.13,10267.539,18235.464,10267.539,18279.464C10267.539,18323.464,10267.539,18390.13,10267.539,18456.797C10267.539,18523.464,10267.539,18590.13,10267.539,18629.63C10267.539,18669.13,10267.539,18681.464,10267.539,18702.297C10267.539,18723.13,10267.539,18752.464,10267.539,18779.797C10267.539,18807.13,10267.539,18832.464,10267.539,19387.766C10267.539,19943.068,10267.539,21028.339,10267.539,22113.609C10267.539,23198.88,10267.539,24284.151,10267.539,24854.12C10267.539,25424.089,10267.539,25478.755,10267.539,25535.422C10267.539,25592.089,10267.539,25650.755,10262.882,25685.741C10258.226,25720.726,10248.913,25732.03,10244.256,25737.683L10239.6,25743.335"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_D_经销商奖励处理_0" d="M4568.207,470.441L5129.479,482.375C5690.75,494.31,6813.293,518.178,7374.564,542.779C7935.836,567.38,7935.836,592.714,7935.836,618.047C7935.836,643.38,7935.836,668.714,7935.836,685.547C7935.836,702.38,7935.836,710.714,7935.836,725.547C7935.836,740.38,7935.836,761.714,7935.836,783.047C7935.836,804.38,7935.836,825.714,7935.836,856.301C7935.836,886.888,7935.836,926.729,7935.836,968.57C7935.836,1010.411,7935.836,1054.253,7935.836,1094.84C7935.836,1135.427,7935.836,1172.76,7935.836,1208.094C7935.836,1243.427,7935.836,1276.76,7935.836,1310.094C7935.836,1343.427,7935.836,1376.76,7935.836,1410.094C7935.836,1443.427,7935.836,1476.76,7935.836,1508.094C7935.836,1539.427,7935.836,1568.76,7935.836,1598.094C7935.836,1627.427,7935.836,1656.76,7935.836,1682.094C7935.836,1707.427,7935.836,1728.76,7935.836,1750.094C7935.836,1771.427,7935.836,1792.76,7935.836,1812.094C7935.836,1831.427,7935.836,1848.76,7935.836,1866.094C7935.836,1883.427,7935.836,1900.76,7935.836,1918.094C7935.836,1935.427,7935.836,1952.76,7935.836,1970.094C7935.836,1987.427,7935.836,2004.76,7935.836,2051.701C7935.836,2098.641,7935.836,2175.188,7935.836,2253.734C7935.836,2332.281,7935.836,2412.828,7935.836,2490.065C7935.836,2567.302,7935.836,2641.229,7935.836,2715.156C7935.836,2789.083,7935.836,2863.01,7935.836,2905.474C7935.836,2947.938,7935.836,2958.938,7935.836,2964.438L7935.836,2969.938"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_E_终端奖励处理_0" d="M2456.848,649.634L2406.621,657.036C2356.395,664.439,2255.941,679.243,2205.715,690.145C2155.488,701.047,2155.488,708.047,2155.488,711.547L2155.488,715.047"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_D33_经销商奖励详细处理_0" d="M9261.017,18832.797L9254.324,18836.964C9247.632,18841.13,9234.248,18849.464,8958.02,19219.118C8681.791,19588.772,8142.719,20319.747,7873.183,20685.235L7603.648,21050.722"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_E35_终端奖励详细处理_0" d="M3658.717,9175.5L3650.579,9181.667C3642.44,9187.833,3626.163,9200.167,3315.682,9636.674C3005.2,10073.181,2400.513,10933.862,2098.17,11364.202L1795.827,11794.543"/></g><g class="edgeLabels"><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g transform="translate(4018.87890625, 378.046875)" class="edgeLabel"><g transform="translate(-8, -12)" class="label"><foreignObject height="24" width="16"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>是</p></span></div></foreignObject></g></g><g transform="translate(4361.54296875, 378.046875)" class="edgeLabel"><g transform="translate(-8, -12)" class="label"><foreignObject height="24" width="16"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>否</p></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g transform="translate(7799.68359375, 3920.53125)" class="edgeLabel"><g transform="translate(-16, -12)" class="label"><foreignObject height="24" width="32"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>无效</p></span></div></foreignObject></g></g><g transform="translate(8089.98828125, 3920.53125)" class="edgeLabel"><g transform="translate(-16, -12)" class="label"><foreignObject height="24" width="32"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>有效</p></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g transform="translate(7941.42578125, 6279.765625)" class="edgeLabel"><g transform="translate(-8, -12)" class="label"><foreignObject height="24" width="16"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>是</p></span></div></foreignObject></g></g><g transform="translate(8359.62109375, 6279.765625)" class="edgeLabel"><g transform="translate(-8, -12)" class="label"><foreignObject height="24" width="16"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>否</p></span></div></foreignObject></g></g><g transform="translate(8109.81640625, 6723.328125)" class="edgeLabel"><g transform="translate(-8, -12)" class="label"><foreignObject height="24" width="16"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>是</p></span></div></foreignObject></g></g><g transform="translate(8535.58203125, 6723.328125)" class="edgeLabel"><g transform="translate(-8, -12)" class="label"><foreignObject height="24" width="16"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>否</p></span></div></foreignObject></g></g><g transform="translate(8285.77734375, 7197.171875)" class="edgeLabel"><g transform="translate(-8, -12)" class="label"><foreignObject height="24" width="16"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>是</p></span></div></foreignObject></g></g><g transform="translate(8701.76953125, 7197.171875)" class="edgeLabel"><g transform="translate(-8, -12)" class="label"><foreignObject height="24" width="16"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>否</p></span></div></foreignObject></g></g><g transform="translate(8451.96484375, 7631.921875)" class="edgeLabel"><g transform="translate(-8, -12)" class="label"><foreignObject height="24" width="16"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>是</p></span></div></foreignObject></g></g><g transform="translate(8876.640625, 7631.921875)" class="edgeLabel"><g transform="translate(-8, -12)" class="label"><foreignObject height="24" width="16"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>否</p></span></div></foreignObject></g></g><g transform="translate(8626.8359375, 8101.40625)" class="edgeLabel"><g transform="translate(-8, -12)" class="label"><foreignObject height="24" width="16"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>是</p></span></div></foreignObject></g></g><g transform="translate(9187.90234375, 8101.40625)" class="edgeLabel"><g transform="translate(-8, -12)" class="label"><foreignObject height="24" width="16"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>否</p></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g transform="translate(8815.109375, 9024.5)" class="edgeLabel"><g transform="translate(-8, -12)" class="label"><foreignObject height="24" width="16"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>否</p></span></div></foreignObject></g></g><g transform="translate(9356.15625, 9024.5)" class="edgeLabel"><g transform="translate(-8, -12)" class="label"><foreignObject height="24" width="16"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>是</p></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g transform="translate(8997.36328125, 16936.5625)" class="edgeLabel"><g transform="translate(-8, -12)" class="label"><foreignObject height="24" width="16"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>否</p></span></div></foreignObject></g></g><g transform="translate(9511.15625, 16936.5625)" class="edgeLabel"><g transform="translate(-8, -12)" class="label"><foreignObject height="24" width="16"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>是</p></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g transform="translate(9138.36328125, 17940.796875)" class="edgeLabel"><g transform="translate(-8, -12)" class="label"><foreignObject height="24" width="16"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>否</p></span></div></foreignObject></g></g><g transform="translate(9781.9296875, 17940.796875)" class="edgeLabel"><g transform="translate(-8, -12)" class="label"><foreignObject height="24" width="16"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>是</p></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g transform="translate(9342.9296875, 18693.796875)" class="edgeLabel"><g transform="translate(-32, -12)" class="label"><foreignObject height="24" width="64"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>符合条件</p></span></div></foreignObject></g></g><g transform="translate(10068.99609375, 18693.796875)" class="edgeLabel"><g transform="translate(-40, -12)" class="label"><foreignObject height="24" width="80"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>不符合条件</p></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g transform="translate(10078.99609375, 25709.421875)" class="edgeLabel"><g transform="translate(-8, -12)" class="label"><foreignObject height="24" width="16"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>是</p></span></div></foreignObject></g></g><g transform="translate(9352.9296875, 25797.421875)" class="edgeLabel"><g transform="translate(-8, -12)" class="label"><foreignObject height="24" width="16"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>否</p></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g transform="translate(1991.9375, 1098.09375)" class="edgeLabel"><g transform="translate(-16, -12)" class="label"><foreignObject height="24" width="32"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>无效</p></span></div></foreignObject></g></g><g transform="translate(2309.640625, 1098.09375)" class="edgeLabel"><g transform="translate(-16, -12)" class="label"><foreignObject height="24" width="32"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>有效</p></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g transform="translate(2133.6796875, 2493.375)" class="edgeLabel"><g transform="translate(-8, -12)" class="label"><foreignObject height="24" width="16"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>是</p></span></div></foreignObject></g></g><g transform="translate(2478.03125, 2493.375)" class="edgeLabel"><g transform="translate(-8, -12)" class="label"><foreignObject height="24" width="16"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>否</p></span></div></foreignObject></g></g><g transform="translate(2302.0703125, 2936.9375)" class="edgeLabel"><g transform="translate(-8, -12)" class="label"><foreignObject height="24" width="16"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>是</p></span></div></foreignObject></g></g><g transform="translate(2653.9921875, 2936.9375)" class="edgeLabel"><g transform="translate(-8, -12)" class="label"><foreignObject height="24" width="16"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>否</p></span></div></foreignObject></g></g><g transform="translate(2478.03125, 3435.78125)" class="edgeLabel"><g transform="translate(-8, -12)" class="label"><foreignObject height="24" width="16"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>是</p></span></div></foreignObject></g></g><g transform="translate(2820.1796875, 3435.78125)" class="edgeLabel"><g transform="translate(-8, -12)" class="label"><foreignObject height="24" width="16"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>否</p></span></div></foreignObject></g></g><g transform="translate(2644.21875, 3920.53125)" class="edgeLabel"><g transform="translate(-8, -12)" class="label"><foreignObject height="24" width="16"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>是</p></span></div></foreignObject></g></g><g transform="translate(3002.49609375, 3920.53125)" class="edgeLabel"><g transform="translate(-8, -12)" class="label"><foreignObject height="24" width="16"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>否</p></span></div></foreignObject></g></g><g transform="translate(2826.53515625, 4419.796875)" class="edgeLabel"><g transform="translate(-8, -12)" class="label"><foreignObject height="24" width="16"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>是</p></span></div></foreignObject></g></g><g transform="translate(3185.99609375, 4419.796875)" class="edgeLabel"><g transform="translate(-8, -12)" class="label"><foreignObject height="24" width="16"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>否</p></span></div></foreignObject></g></g><g transform="translate(2996.03515625, 4867.796875)" class="edgeLabel"><g transform="translate(-8, -12)" class="label"><foreignObject height="24" width="16"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>是</p></span></div></foreignObject></g></g><g transform="translate(3502.31640625, 4867.796875)" class="edgeLabel"><g transform="translate(-8, -12)" class="label"><foreignObject height="24" width="16"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>否</p></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g transform="translate(3188.5390625, 5607.8125)" class="edgeLabel"><g transform="translate(-8, -12)" class="label"><foreignObject height="24" width="16"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>否</p></span></div></foreignObject></g></g><g transform="translate(3670.5703125, 5607.8125)" class="edgeLabel"><g transform="translate(-8, -12)" class="label"><foreignObject height="24" width="16"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>是</p></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g transform="translate(3370.79296875, 6279.765625)" class="edgeLabel"><g transform="translate(-8, -12)" class="label"><foreignObject height="24" width="16"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>否</p></span></div></foreignObject></g></g><g transform="translate(3825.5703125, 6279.765625)" class="edgeLabel"><g transform="translate(-8, -12)" class="label"><foreignObject height="24" width="16"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>是</p></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g transform="translate(3511.79296875, 7631.921875)" class="edgeLabel"><g transform="translate(-8, -12)" class="label"><foreignObject height="24" width="16"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>否</p></span></div></foreignObject></g></g><g transform="translate(4116.16796875, 7631.921875)" class="edgeLabel"><g transform="translate(-8, -12)" class="label"><foreignObject height="24" width="16"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>是</p></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g transform="translate(3726.0234375, 9024.5)" class="edgeLabel"><g transform="translate(-32, -12)" class="label"><foreignObject height="24" width="64"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>符合条件</p></span></div></foreignObject></g></g><g transform="translate(4397.3046875, 9024.5)" class="edgeLabel"><g transform="translate(-40, -12)" class="label"><foreignObject height="24" width="80"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>不符合条件</p></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g transform="translate(4397.3046875, 17288.5625)" class="edgeLabel"><g transform="translate(-8, -12)" class="label"><foreignObject height="24" width="16"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>是</p></span></div></foreignObject></g></g><g transform="translate(3726.0234375, 17400.5625)" class="edgeLabel"><g transform="translate(-8, -12)" class="label"><foreignObject height="24" width="16"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>否</p></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g></g><g class="nodes"><g transform="translate(0, 9229.5)" class="root"><g class="clusters"><g data-look="classic" id="终端奖励详细处理" class="cluster"><rect height="7662.0625" width="1785.52734375" y="8" x="8" style=""/><g transform="translate(715.904296875, 8)" class="cluster-label"><foreignObject height="48" width="369.71875"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>terminalRewardRecordService.computeShopReward详细处理</p></span></div></foreignObject></g></g></g><g class="edgePaths"><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_G1_G2_0" d="M386.961,123.5L386.961,129.75C386.961,136,386.961,148.5,386.961,160.333C386.961,172.167,386.961,183.333,386.961,188.917L386.961,194.5"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_G2_G3_0" d="M386.961,300.5L386.961,306.75C386.961,313,386.961,325.5,386.961,337.333C386.961,349.167,386.961,360.333,386.961,365.917L386.961,371.5"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_G3_G4_0" d="M386.961,501.5L386.961,507.75C386.961,514,386.961,526.5,386.961,538.333C386.961,550.167,386.961,561.333,386.961,566.917L386.961,572.5"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_G4_G5_0" d="M386.961,678.5L386.961,684.75C386.961,691,386.961,703.5,386.961,715.333C386.961,727.167,386.961,738.333,386.961,743.917L386.961,749.5"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_G5_G6_0" d="M386.961,855.5L386.961,861.75C386.961,868,386.961,880.5,387.035,892.417C387.11,904.333,387.259,915.667,387.334,921.334L387.408,927"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_G6_G7_0" d="M325.477,1099.016L303.67,1117.514C281.864,1136.011,238.25,1173.005,216.443,1199.086C194.637,1225.167,194.637,1240.333,194.637,1247.917L194.637,1255.5"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_G6_G8_0" d="M435.733,1112.728L447.437,1128.94C459.142,1145.152,482.551,1177.576,494.256,1201.371C505.961,1225.167,505.961,1240.333,505.961,1247.917L505.961,1255.5"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_G8_G9_0" d="M505.961,1313.5L505.961,1319.75C505.961,1326,505.961,1338.5,505.961,1350.333C505.961,1362.167,505.961,1373.333,505.961,1378.917L505.961,1384.5"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_G9_G10_0" d="M505.961,1442.5L505.961,1448.75C505.961,1455,505.961,1467.5,505.961,1479.333C505.961,1491.167,505.961,1502.333,505.961,1507.917L505.961,1513.5"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_G10_G11_0" d="M505.961,1739.5L505.961,1745.75C505.961,1752,505.961,1764.5,505.961,1776.333C505.961,1788.167,505.961,1799.333,505.961,1804.917L505.961,1810.5"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_G11_G12_0" d="M505.961,1868.5L505.961,1874.75C505.961,1881,505.961,1893.5,505.961,1905.333C505.961,1917.167,505.961,1928.333,505.961,1933.917L505.961,1939.5"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_G12_G13_0" d="M505.961,1997.5L505.961,2003.75C505.961,2010,505.961,2022.5,505.961,2034.333C505.961,2046.167,505.961,2057.333,505.961,2062.917L505.961,2068.5"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_G13_G14_0" d="M505.961,2126.5L505.961,2132.75C505.961,2139,505.961,2151.5,506.035,2163.417C506.11,2175.333,506.259,2186.667,506.334,2192.334L506.408,2198"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_G14_G15_0" d="M453.876,2331.415L430.503,2348.346C407.13,2365.277,360.383,2399.138,337.01,2423.653C313.637,2448.167,313.637,2463.333,313.637,2470.917L313.637,2478.5"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_G14_G16_0" d="M545.802,2344.659L556.995,2359.382C568.189,2374.106,590.575,2403.553,601.768,2425.86C612.961,2448.167,612.961,2463.333,612.961,2470.917L612.961,2478.5"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_G16_G17_0" d="M612.961,2536.5L612.961,2542.75C612.961,2549,612.961,2561.5,612.961,2573.333C612.961,2585.167,612.961,2596.333,612.961,2601.917L612.961,2607.5"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_G17_G18_0" d="M612.961,2665.5L612.961,2671.75C612.961,2678,612.961,2690.5,612.961,2702.333C612.961,2714.167,612.961,2725.333,612.961,2730.917L612.961,2736.5"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_G18_G19_0" d="M612.961,2794.5L612.961,2800.75C612.961,2807,612.961,2819.5,613.035,2831.417C613.11,2843.333,613.259,2854.667,613.334,2860.334L613.408,2866"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_G19_G20_0" d="M560.876,2999.415L537.503,3016.346C514.13,3033.277,467.383,3067.138,444.01,3102.319C420.637,3137.5,420.637,3174,420.637,3192.25L420.637,3210.5"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_G19_G21_0" d="M654.124,3011.337L666.18,3026.281C678.237,3041.224,702.349,3071.112,714.482,3093.723C726.614,3116.333,726.768,3131.667,726.844,3139.333L726.921,3147"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_G21_G22_0" d="M726.961,3333L726.878,3339.167C726.794,3345.333,726.628,3357.667,726.619,3369.5C726.61,3381.333,726.759,3392.667,726.834,3398.334L726.908,3404"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_G22_G23_0" d="M674.376,3537.415L651.003,3554.346C627.63,3571.277,580.883,3605.138,557.51,3642.986C534.137,3680.833,534.137,3722.667,534.137,3743.583L534.137,3764.5"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_G22_G24_0" d="M769.161,3547.8L782.295,3563C795.428,3578.2,821.694,3608.6,834.904,3631.467C848.114,3654.333,848.268,3669.667,848.344,3677.333L848.421,3685"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_G24_G25_0" d="M848.461,3903L848.378,3909.167C848.294,3915.333,848.128,3927.667,848.119,3939.5C848.11,3951.333,848.259,3962.667,848.334,3968.334L848.408,3974"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_G25_G26_0" d="M802.933,4082.472L778.384,4098.227C753.834,4113.981,704.736,4145.491,680.186,4176.829C655.637,4208.167,655.637,4239.333,655.637,4254.917L655.637,4270.5"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_G25_G27_0" d="M882.863,4093.598L894.629,4107.498C906.396,4121.399,929.928,4149.199,941.771,4170.766C953.614,4192.333,953.768,4207.667,953.844,4215.333L953.921,4223"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_G27_G28_0" d="M953.961,4377L953.878,4383.167C953.794,4389.333,953.628,4401.667,953.619,4413.5C953.61,4425.333,953.759,4436.667,953.834,4442.334L953.908,4448"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_G28_G29_0" d="M908.433,4556.472L883.884,4572.227C859.334,4587.981,810.236,4619.491,785.686,4642.829C761.137,4666.167,761.137,4681.333,761.137,4688.917L761.137,4696.5"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_G28_G30_0" d="M988.626,4567.335L1000.599,4581.279C1012.571,4595.223,1036.516,4623.112,1048.488,4644.639C1060.461,4666.167,1060.461,4681.333,1060.461,4688.917L1060.461,4696.5"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_G30_G31_0" d="M1060.461,4754.5L1060.461,4760.75C1060.461,4767,1060.461,4779.5,1060.535,4791.417C1060.61,4803.333,1060.759,4814.667,1060.834,4820.334L1060.908,4826"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_G31_G32_0" d="M1008.376,4959.415L985.003,4976.346C961.63,4993.277,914.883,5027.138,891.51,5064.352C868.137,5101.565,868.137,5142.13,868.137,5162.413L868.137,5182.695"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_G31_G33_0" d="M1102.804,4970.157L1115.68,4985.297C1128.556,5000.438,1154.307,5030.719,1167.26,5053.526C1180.212,5076.333,1180.365,5091.667,1180.442,5099.333L1180.519,5107"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_G33_G34_0" d="M1180.559,5317.391L1180.475,5323.557C1180.392,5329.724,1180.225,5342.057,1180.216,5353.891C1180.208,5365.724,1180.357,5377.058,1180.431,5382.724L1180.506,5388.391"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_G34_G35_0" d="M1129.59,5515.812L1105.947,5532.474C1082.305,5549.135,1035.019,5582.458,1011.377,5606.703C987.734,5630.948,987.734,5646.115,987.734,5653.698L987.734,5661.281"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_G34_G36_0" d="M1218.844,5528.496L1230.213,5543.044C1241.582,5557.591,1264.32,5586.686,1275.689,5608.817C1287.059,5630.948,1287.059,5646.115,1287.059,5653.698L1287.059,5661.281"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_G36_G37_0" d="M1287.059,5719.281L1287.059,5725.531C1287.059,5731.781,1287.059,5744.281,1287.133,5756.198C1287.208,5768.115,1287.357,5779.448,1287.431,5785.115L1287.506,5790.782"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_G37_G38_0" d="M1287.559,5944.781L1287.475,5950.948C1287.392,5957.115,1287.225,5969.448,1287.216,5981.281C1287.208,5993.115,1287.357,6004.448,1287.431,6010.115L1287.506,6015.782"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_G38_G39_0" d="M1287.559,6523.063L1287.475,6529.229C1287.392,6535.396,1287.225,6547.729,1287.216,6559.563C1287.208,6571.396,1287.357,6582.729,1287.431,6588.396L1287.506,6594.063"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_G39_G40_0" d="M1237.209,6713.713L1211.463,6730.271C1185.718,6746.83,1134.226,6779.946,1108.48,6804.088C1082.734,6828.229,1082.734,6843.396,1082.734,6850.979L1082.734,6858.563"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_G39_G41_0" d="M1322.218,6729.403L1332.191,6743.346C1342.165,6757.29,1362.112,6785.176,1372.085,6806.703C1382.059,6828.229,1382.059,6843.396,1382.059,6850.979L1382.059,6858.563"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_G41_G42_0" d="M1382.059,6916.563L1382.059,6922.813C1382.059,6929.063,1382.059,6941.563,1382.133,6953.479C1382.208,6965.396,1382.357,6976.729,1382.431,6982.396L1382.506,6988.063"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_G42_G43_0" d="M1332.693,7124.197L1312.533,7140.674C1292.373,7157.152,1252.054,7190.107,1231.894,7214.168C1211.734,7238.229,1211.734,7253.396,1211.734,7260.979L1211.734,7268.563"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_G42_G44_0" d="M1432.424,7124.197L1452.417,7140.674C1472.411,7157.152,1512.397,7190.107,1532.39,7214.168C1552.383,7238.229,1552.383,7253.396,1552.383,7260.979L1552.383,7268.563"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_G43_G45_0" d="M1211.734,7374.563L1211.734,7380.813C1211.734,7387.063,1211.734,7399.563,1227.615,7411.826C1243.496,7424.09,1275.258,7436.118,1291.138,7442.132L1307.019,7448.146"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_G44_G45_0" d="M1552.383,7374.563L1552.383,7380.813C1552.383,7387.063,1552.383,7399.563,1536.502,7411.826C1520.621,7424.09,1488.86,7436.118,1472.979,7442.132L1457.098,7448.146"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_G45_G46_0" d="M1382.059,7503.563L1382.059,7509.813C1382.059,7516.063,1382.059,7528.563,1382.059,7540.396C1382.059,7552.229,1382.059,7563.396,1382.059,7568.979L1382.059,7574.563"/></g><g class="edgeLabels"><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g transform="translate(194.63671875, 1210)" class="edgeLabel"><g transform="translate(-8, -12)" class="label"><foreignObject height="24" width="16"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>是</p></span></div></foreignObject></g></g><g transform="translate(505.9609375, 1210)" class="edgeLabel"><g transform="translate(-8, -12)" class="label"><foreignObject height="24" width="16"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>否</p></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g transform="translate(313.63671875, 2433)" class="edgeLabel"><g transform="translate(-8, -12)" class="label"><foreignObject height="24" width="16"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>是</p></span></div></foreignObject></g></g><g transform="translate(612.9609375, 2433)" class="edgeLabel"><g transform="translate(-8, -12)" class="label"><foreignObject height="24" width="16"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>否</p></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g transform="translate(420.63671875, 3101)" class="edgeLabel"><g transform="translate(-8, -12)" class="label"><foreignObject height="24" width="16"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>否</p></span></div></foreignObject></g></g><g transform="translate(726.4609375, 3101)" class="edgeLabel"><g transform="translate(-8, -12)" class="label"><foreignObject height="24" width="16"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>是</p></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g transform="translate(534.13671875, 3639)" class="edgeLabel"><g transform="translate(-8, -12)" class="label"><foreignObject height="24" width="16"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>否</p></span></div></foreignObject></g></g><g transform="translate(847.9609375, 3639)" class="edgeLabel"><g transform="translate(-8, -12)" class="label"><foreignObject height="24" width="16"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>是</p></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g transform="translate(655.63671875, 4177)" class="edgeLabel"><g transform="translate(-8, -12)" class="label"><foreignObject height="24" width="16"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>否</p></span></div></foreignObject></g></g><g transform="translate(953.4609375, 4177)" class="edgeLabel"><g transform="translate(-8, -12)" class="label"><foreignObject height="24" width="16"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>是</p></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g transform="translate(761.13671875, 4651)" class="edgeLabel"><g transform="translate(-8, -12)" class="label"><foreignObject height="24" width="16"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>否</p></span></div></foreignObject></g></g><g transform="translate(1060.4609375, 4651)" class="edgeLabel"><g transform="translate(-8, -12)" class="label"><foreignObject height="24" width="16"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>是</p></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g transform="translate(868.13671875, 5061)" class="edgeLabel"><g transform="translate(-8, -12)" class="label"><foreignObject height="24" width="16"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>否</p></span></div></foreignObject></g></g><g transform="translate(1180.05859375, 5061)" class="edgeLabel"><g transform="translate(-8, -12)" class="label"><foreignObject height="24" width="16"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>是</p></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g transform="translate(987.734375, 5615.78125)" class="edgeLabel"><g transform="translate(-8, -12)" class="label"><foreignObject height="24" width="16"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>是</p></span></div></foreignObject></g></g><g transform="translate(1287.05859375, 5615.78125)" class="edgeLabel"><g transform="translate(-8, -12)" class="label"><foreignObject height="24" width="16"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>否</p></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g transform="translate(1082.734375, 6813.0625)" class="edgeLabel"><g transform="translate(-8, -12)" class="label"><foreignObject height="24" width="16"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>是</p></span></div></foreignObject></g></g><g transform="translate(1382.05859375, 6813.0625)" class="edgeLabel"><g transform="translate(-8, -12)" class="label"><foreignObject height="24" width="16"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>否</p></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g transform="translate(1211.734375, 7223.0625)" class="edgeLabel"><g transform="translate(-8, -12)" class="label"><foreignObject height="24" width="16"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>是</p></span></div></foreignObject></g></g><g transform="translate(1552.3828125, 7223.0625)" class="edgeLabel"><g transform="translate(-8, -12)" class="label"><foreignObject height="24" width="16"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>否</p></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g></g><g class="nodes"><g transform="translate(386.9609375, 84.5)" id="flowchart-G1-258" class="node default"><rect height="78" width="434.546875" y="-39" x="-217.2734375" style="" class="basic label-container"/><g transform="translate(-187.2734375, -24)" style="" class="label"><rect/><foreignObject height="48" width="374.546875"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>terminalRewardRecordService.computeShopReward 计算终端奖励</p></span></div></foreignObject></g></g><g transform="translate(386.9609375, 249.5)" id="flowchart-G2-259" class="node default"><rect height="102" width="260" y="-51" x="-130" style="" class="basic label-container"/><g transform="translate(-100, -36)" style="" class="label"><rect/><foreignObject height="72" width="200"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>获取dealerOutTime<br />从码信息中获取经销商出库时间</p></span></div></foreignObject></g></g><g transform="translate(386.9609375, 438.5)" id="flowchart-G3-261" class="node default"><rect height="126" width="260" y="-63" x="-130" style="" class="basic label-container"/><g transform="translate(-100, -48)" style="" class="label"><rect/><foreignObject height="96" width="200"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>获取收货类型名称<br />根据receiptType获取收货类型:扫码收货/出库收货/一键收货/经理代收</p></span></div></foreignObject></g></g><g transform="translate(386.9609375, 627.5)" id="flowchart-G4-263" class="node default"><rect height="102" width="260" y="-51" x="-130" style="" class="basic label-container"/><g transform="translate(-100, -36)" style="" class="label"><rect/><foreignObject height="72" width="200"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>获取订单信息<br />从TerminalScanDetailModel中获取订单相关信息</p></span></div></foreignObject></g></g><g transform="translate(386.9609375, 804.5)" id="flowchart-G5-265" class="node default"><rect height="102" width="260" y="-51" x="-130" style="" class="basic label-container"/><g transform="translate(-100, -36)" style="" class="label"><rect/><foreignObject height="72" width="200"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>初始化ExtendDataBean<br />封装扩展数据:商品名称、数量、终端名称等</p></span></div></foreignObject></g></g><g transform="translate(386.9609375, 1045.5)" id="flowchart-G6-267" class="node default"><polygon transform="translate(-115,115)" class="label-container" points="115,0 230,-115 115,-230 0,-115"/><g transform="translate(-88, -12)" style="" class="label"><rect/><foreignObject height="24" width="176"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>检查码信息列表是否为空</p></span></div></foreignObject></g></g><g transform="translate(194.63671875, 1286.5)" id="flowchart-G7-269" class="node default"><rect height="54" width="172" y="-27" x="-86" style="" class="basic label-container"/><g transform="translate(-56, -12)" style="" class="label"><rect/><foreignObject height="24" width="112"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>记录日志并返回</p></span></div></foreignObject></g></g><g transform="translate(505.9609375, 1286.5)" id="flowchart-G8-271" class="node default"><rect height="54" width="204" y="-27" x="-102" style="" class="basic label-container"/><g transform="translate(-72, -12)" style="" class="label"><rect/><foreignObject height="24" width="144"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>获取商品编码和数量</p></span></div></foreignObject></g></g><g transform="translate(505.9609375, 1415.5)" id="flowchart-G9-273" class="node default"><rect height="54" width="172" y="-27" x="-86" style="" class="basic label-container"/><g transform="translate(-56, -12)" style="" class="label"><rect/><foreignObject height="24" width="112"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>查询终端店类型</p></span></div></foreignObject></g></g><g transform="translate(505.9609375, 1628.5)" id="flowchart-G10-275" class="node default"><rect height="222" width="261" y="-111" x="-130.5" style="" class="basic label-container"/><g transform="translate(-100.5, -96)" style="" class="label"><rect/><foreignObject height="192" width="201"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>查询可参与的活动<br />SELECT * FROM t_reward_activity_common WHERE goodsCode=? AND accountType=? AND activityTime&lt;=? AND companyId=? AND rewardType=1</p></span></div></foreignObject></g></g><g transform="translate(505.9609375, 1841.5)" id="flowchart-G11-277" class="node default"><rect height="54" width="188" y="-27" x="-94" style="" class="basic label-container"/><g transform="translate(-64, -12)" style="" class="label"><rect/><foreignObject height="24" width="128"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>按活动子类型分组</p></span></div></foreignObject></g></g><g transform="translate(505.9609375, 1970.5)" id="flowchart-G12-279" class="node default"><rect height="54" width="172" y="-27" x="-86" style="" class="basic label-container"/><g transform="translate(-56, -12)" style="" class="label"><rect/><foreignObject height="24" width="112"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>遍历活动子类型</p></span></div></foreignObject></g></g><g transform="translate(505.9609375, 2099.5)" id="flowchart-G13-281" class="node default"><rect height="54" width="156" y="-27" x="-78" style="" class="basic label-container"/><g transform="translate(-48, -12)" style="" class="label"><rect/><foreignObject height="24" width="96"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>筛选活动列表</p></span></div></foreignObject></g></g><g transform="translate(505.9609375, 2292.5)" id="flowchart-G14-283" class="node default"><polygon transform="translate(-91,91)" class="label-container" points="91,0 182,-91 91,-182 0,-91"/><g transform="translate(-64, -12)" style="" class="label"><rect/><foreignObject height="24" width="128"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>活动列表是否为空</p></span></div></foreignObject></g></g><g transform="translate(313.63671875, 2509.5)" id="flowchart-G15-285" class="node default"><rect height="54" width="172" y="-27" x="-86" style="" class="basic label-container"/><g transform="translate(-56, -12)" style="" class="label"><rect/><foreignObject height="24" width="112"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>记录日志并继续</p></span></div></foreignObject></g></g><g transform="translate(612.9609375, 2509.5)" id="flowchart-G16-287" class="node default"><rect height="54" width="156" y="-27" x="-78" style="" class="basic label-container"/><g transform="translate(-48, -12)" style="" class="label"><rect/><foreignObject height="24" width="96"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>遍历活动列表</p></span></div></foreignObject></g></g><g transform="translate(612.9609375, 2638.5)" id="flowchart-G17-289" class="node default"><rect height="54" width="156" y="-27" x="-78" style="" class="basic label-container"/><g transform="translate(-48, -12)" style="" class="label"><rect/><foreignObject height="24" width="96"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>设置查询参数</p></span></div></foreignObject></g></g><g transform="translate(612.9609375, 2767.5)" id="flowchart-G18-291" class="node default"><rect height="54" width="156" y="-27" x="-78" style="" class="basic label-container"/><g transform="translate(-48, -12)" style="" class="label"><rect/><foreignObject height="24" width="96"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>查询活动范围</p></span></div></foreignObject></g></g><g transform="translate(612.9609375, 2960.5)" id="flowchart-G19-293" class="node default"><polygon transform="translate(-91,91)" class="label-container" points="91,0 182,-91 91,-182 0,-91"/><g transform="translate(-64, -12)" style="" class="label"><rect/><foreignObject height="24" width="128"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>活动范围是否匹配</p></span></div></foreignObject></g></g><g transform="translate(420.63671875, 3241.5)" id="flowchart-G20-295" class="node default"><rect height="54" width="172" y="-27" x="-86" style="" class="basic label-container"/><g transform="translate(-56, -12)" style="" class="label"><rect/><foreignObject height="24" width="112"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>继续下一个活动</p></span></div></foreignObject></g></g><g transform="translate(726.4609375, 3241.5)" id="flowchart-G21-297" class="node default"><polygon transform="translate(-91,91)" class="label-container" points="91,0 182,-91 91,-182 0,-91"/><g transform="translate(-64, -12)" style="" class="label"><rect/><foreignObject height="24" width="128"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>检查出库时间限制</p></span></div></foreignObject></g></g><g transform="translate(726.4609375, 3498.5)" id="flowchart-G22-299" class="node default"><polygon transform="translate(-91,91)" class="label-container" points="91,0 182,-91 91,-182 0,-91"/><g transform="translate(-64, -12)" style="" class="label"><rect/><foreignObject height="24" width="128"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>是否开启会员奖励</p></span></div></foreignObject></g></g><g transform="translate(534.13671875, 3795.5)" id="flowchart-G23-301" class="node default"><rect height="54" width="172" y="-27" x="-86" style="" class="basic label-container"/><g transform="translate(-56, -12)" style="" class="label"><rect/><foreignObject height="24" width="112"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>继续下一个活动</p></span></div></foreignObject></g></g><g transform="translate(847.9609375, 3795.5)" id="flowchart-G24-303" class="node default"><polygon transform="translate(-107,107)" class="label-container" points="107,0 214,-107 107,-214 0,-107"/><g transform="translate(-80, -12)" style="" class="label"><rect/><foreignObject height="24" width="160"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>检查终端类型是否匹配</p></span></div></foreignObject></g></g><g transform="translate(847.9609375, 4052.5)" id="flowchart-G25-305" class="node default"><polygon transform="translate(-75,75)" class="label-container" points="75,0 150,-75 75,-150 0,-75"/><g transform="translate(-48, -12)" style="" class="label"><rect/><foreignObject height="24" width="96"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>是否满足条件</p></span></div></foreignObject></g></g><g transform="translate(655.63671875, 4301.5)" id="flowchart-G26-307" class="node default"><rect height="54" width="172" y="-27" x="-86" style="" class="basic label-container"/><g transform="translate(-56, -12)" style="" class="label"><rect/><foreignObject height="24" width="112"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>继续下一个活动</p></span></div></foreignObject></g></g><g transform="translate(953.4609375, 4301.5)" id="flowchart-G27-309" class="node default"><polygon transform="translate(-75,75)" class="label-container" points="75,0 150,-75 75,-150 0,-75"/><g transform="translate(-48, -12)" style="" class="label"><rect/><foreignObject height="24" width="96"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>检查活动限制</p></span></div></foreignObject></g></g><g transform="translate(953.4609375, 4526.5)" id="flowchart-G28-311" class="node default"><polygon transform="translate(-75,75)" class="label-container" points="75,0 150,-75 75,-150 0,-75"/><g transform="translate(-48, -12)" style="" class="label"><rect/><foreignObject height="24" width="96"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>是否满足限制</p></span></div></foreignObject></g></g><g transform="translate(761.13671875, 4727.5)" id="flowchart-G29-313" class="node default"><rect height="54" width="172" y="-27" x="-86" style="" class="basic label-container"/><g transform="translate(-56, -12)" style="" class="label"><rect/><foreignObject height="24" width="112"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>继续下一个活动</p></span></div></foreignObject></g></g><g transform="translate(1060.4609375, 4727.5)" id="flowchart-G30-315" class="node default"><rect height="54" width="156" y="-27" x="-78" style="" class="basic label-container"/><g transform="translate(-48, -12)" style="" class="label"><rect/><foreignObject height="24" width="96"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>检查额外条件</p></span></div></foreignObject></g></g><g transform="translate(1060.4609375, 4920.5)" id="flowchart-G31-317" class="node default"><polygon transform="translate(-91,91)" class="label-container" points="91,0 182,-91 91,-182 0,-91"/><g transform="translate(-64, -12)" style="" class="label"><rect/><foreignObject height="24" width="128"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>是否满足额外条件</p></span></div></foreignObject></g></g><g transform="translate(868.13671875, 5213.6953125)" id="flowchart-G32-319" class="node default"><rect height="54" width="172" y="-27" x="-86" style="" class="basic label-container"/><g transform="translate(-56, -12)" style="" class="label"><rect/><foreignObject height="24" width="112"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>继续下一个活动</p></span></div></foreignObject></g></g><g transform="translate(1180.05859375, 5213.6953125)" id="flowchart-G33-321" class="node default"><polygon transform="translate(-103.1953125,103.1953125)" class="label-container" points="103.1953125,0 206.390625,-103.1953125 103.1953125,-206.390625 0,-103.1953125"/><g transform="translate(-76.1953125, -12)" style="" class="label"><rect/><foreignObject height="24" width="152.390625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>检查奖励金额是否为0</p></span></div></foreignObject></g></g><g transform="translate(1180.05859375, 5479.0859375)" id="flowchart-G34-323" class="node default"><polygon transform="translate(-87.1953125,87.1953125)" class="label-container" points="87.1953125,0 174.390625,-87.1953125 87.1953125,-174.390625 0,-87.1953125"/><g transform="translate(-60.1953125, -12)" style="" class="label"><rect/><foreignObject height="24" width="120.390625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>奖励金额是否为0</p></span></div></foreignObject></g></g><g transform="translate(987.734375, 5692.28125)" id="flowchart-G35-325" class="node default"><rect height="54" width="172" y="-27" x="-86" style="" class="basic label-container"/><g transform="translate(-56, -12)" style="" class="label"><rect/><foreignObject height="24" width="112"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>继续下一个活动</p></span></div></foreignObject></g></g><g transform="translate(1287.05859375, 5692.28125)" id="flowchart-G36-327" class="node default"><rect height="54" width="156" y="-27" x="-78" style="" class="basic label-container"/><g transform="translate(-48, -12)" style="" class="label"><rect/><foreignObject height="24" width="96"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>设置备注信息</p></span></div></foreignObject></g></g><g transform="translate(1287.05859375, 5869.28125)" id="flowchart-G37-329" class="node default"><polygon transform="translate(-75,75)" class="label-container" points="75,0 150,-75 75,-150 0,-75"/><g transform="translate(-48, -12)" style="" class="label"><rect/><foreignObject height="24" width="96"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>检查重复执行</p></span></div></foreignObject></g></g><g transform="translate(1287.05859375, 6270.921875)" id="flowchart-G38-331" class="node default"><polygon transform="translate(-251.640625,251.640625)" class="label-container" points="251.640625,0 503.28125,-251.640625 251.640625,-503.28125 0,-251.640625"/><g transform="translate(-128.640625, -108)" style="" class="label"><rect/><foreignObject height="216" width="257.28125"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>检查是否已发放奖励<br />SELECT * FROM t_activity_reward_record WHERE originId=detailId AND activityType=1 AND eventType=? AND originTable=t_terminal_scan_detail AND isDelete=0 AND JSON查询extend_data字段</p></span></div></foreignObject></g></g><g transform="translate(1287.05859375, 6680.5625)" id="flowchart-G39-333" class="node default"><polygon transform="translate(-83,83)" class="label-container" points="83,0 166,-83 83,-166 0,-83"/><g transform="translate(-56, -12)" style="" class="label"><rect/><foreignObject height="24" width="112"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>是否已发放奖励</p></span></div></foreignObject></g></g><g transform="translate(1082.734375, 6889.5625)" id="flowchart-G40-335" class="node default"><rect height="54" width="124" y="-27" x="-62" style="" class="basic label-container"/><g transform="translate(-32, -12)" style="" class="label"><rect/><foreignObject height="24" width="64"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>跳出循环</p></span></div></foreignObject></g></g><g transform="translate(1382.05859375, 6889.5625)" id="flowchart-G41-337" class="node default"><rect height="54" width="156" y="-27" x="-78" style="" class="basic label-container"/><g transform="translate(-48, -12)" style="" class="label"><rect/><foreignObject height="24" width="96"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>判断其他规则</p></span></div></foreignObject></g></g><g transform="translate(1382.05859375, 7082.5625)" id="flowchart-G42-339" class="node default"><polygon transform="translate(-91,91)" class="label-container" points="91,0 182,-91 91,-182 0,-91"/><g transform="translate(-64, -12)" style="" class="label"><rect/><foreignObject height="24" width="128"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>是否满足其他规则</p></span></div></foreignObject></g></g><g transform="translate(1211.734375, 7323.5625)" id="flowchart-G43-341" class="node default"><rect height="102" width="260" y="-51" x="-130" style="" class="basic label-container"/><g transform="translate(-100, -36)" style="" class="label"><rect/><foreignObject height="72" width="200"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>插入活动奖励记录<br />INSERT INTO t_activity_reward_record</p></span></div></foreignObject></g></g><g transform="translate(1552.3828125, 7323.5625)" id="flowchart-G44-343" class="node default"><rect height="102" width="321.296875" y="-51" x="-160.6484375" style="" class="basic label-container"/><g transform="translate(-130.6484375, -36)" style="" class="label"><rect/><foreignObject height="72" width="261.296875"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>插入异常奖励记录<br />INSERT INTO t_activity_reward_exception_record</p></span></div></foreignObject></g></g><g transform="translate(1382.05859375, 7476.5625)" id="flowchart-G45-345" class="node default"><rect height="54" width="201.484375" y="-27" x="-100.7421875" style="" class="basic label-container"/><g transform="translate(-70.7421875, -12)" style="" class="label"><rect/><foreignObject height="24" width="141.484375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>更新扫码明细活动id</p></span></div></foreignObject></g></g><g transform="translate(1382.05859375, 7605.5625)" id="flowchart-G46-349" class="node default"><rect height="54" width="92" y="-27" x="-46" style="" class="basic label-container"/><g transform="translate(-16, -12)" style="" class="label"><rect/><foreignObject height="24" width="32"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>结束</p></span></div></foreignObject></g></g></g></g><g transform="translate(6030.328125, 18874.796875)" class="root"><g class="clusters"><g data-look="classic" id="经销商奖励详细处理" class="cluster"><rect height="6461.625" width="1562.9453125" y="8" x="8" style=""/><g transform="translate(592.74609375, 8)" class="cluster-label"><foreignObject height="48" width="393.453125"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>sendAwardDealerService.sendAwardDealerByOrderNew详细处理</p></span></div></foreignObject></g></g></g><g class="edgePaths"><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_F1_F2_0" d="M392.891,123.5L392.891,129.75C392.891,136,392.891,148.5,392.891,160.333C392.891,172.167,392.891,183.333,392.891,188.917L392.891,194.5"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_F2_F3_0" d="M392.891,300.5L392.891,306.75C392.891,313,392.891,325.5,392.891,337.333C392.891,349.167,392.891,360.333,392.891,365.917L392.891,371.5"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_F3_F4_0" d="M392.891,501.5L392.891,507.75C392.891,514,392.891,526.5,392.891,538.333C392.891,550.167,392.891,561.333,392.891,566.917L392.891,572.5"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_F4_F5_0" d="M392.891,678.5L392.891,684.75C392.891,691,392.891,703.5,392.891,715.333C392.891,727.167,392.891,738.333,392.891,743.917L392.891,749.5"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_F5_F6_0" d="M392.891,855.5L392.891,861.75C392.891,868,392.891,880.5,392.965,892.417C393.04,904.333,393.189,915.667,393.263,921.334L393.338,927"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_F6_F7_0" d="M331.407,1099.016L309.6,1117.514C287.793,1136.011,244.18,1173.005,222.373,1201.086C200.566,1229.167,200.566,1248.333,200.566,1257.917L200.566,1267.5"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_F6_F8_0" d="M444.454,1109.937L457.757,1126.614C471.059,1143.291,497.664,1176.646,510.967,1200.906C524.27,1225.167,524.27,1240.333,524.27,1247.917L524.27,1255.5"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_F8_F9_0" d="M524.27,1337.5L524.27,1343.75C524.27,1350,524.27,1362.5,524.344,1374.417C524.419,1386.333,524.568,1397.667,524.642,1403.334L524.717,1409"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_F9_F10_0" d="M468.905,1555.135L446.078,1572.613C423.252,1590.09,377.598,1625.045,354.772,1658.106C331.945,1691.167,331.945,1722.333,331.945,1737.917L331.945,1753.5"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_F9_F11_0" d="M571.544,1564.226L585.832,1580.188C600.119,1596.15,628.694,1628.075,642.982,1651.621C657.27,1675.167,657.27,1690.333,657.27,1697.917L657.27,1705.5"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_F11_F12_0" d="M657.27,1859.5L657.27,1865.75C657.27,1872,657.27,1884.5,657.27,1896.333C657.27,1908.167,657.27,1919.333,657.27,1924.917L657.27,1930.5"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_F12_F13_0" d="M657.27,2036.5L657.27,2042.75C657.27,2049,657.27,2061.5,657.27,2073.333C657.27,2085.167,657.27,2096.333,657.27,2101.917L657.27,2107.5"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_F13_F14_0" d="M657.27,2165.5L657.27,2171.75C657.27,2178,657.27,2190.5,657.27,2202.333C657.27,2214.167,657.27,2225.333,657.27,2230.917L657.27,2236.5"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_F14_F15_0" d="M657.27,2318.5L657.27,2324.75C657.27,2331,657.27,2343.5,657.27,2355.333C657.27,2367.167,657.27,2378.333,657.27,2383.917L657.27,2389.5"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_F15_F16_0" d="M657.27,2543.5L657.27,2549.75C657.27,2556,657.27,2568.5,657.344,2580.417C657.419,2592.333,657.568,2603.667,657.642,2609.334L657.717,2615"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_F16_F17_0" d="M612.242,2723.472L587.692,2739.227C563.143,2754.981,514.044,2786.491,489.495,2817.829C464.945,2849.167,464.945,2880.333,464.945,2895.917L464.945,2911.5"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_F16_F18_0" d="M696.541,2730.229L712.202,2744.857C727.863,2759.486,759.186,2788.743,774.847,2810.955C790.508,2833.167,790.508,2848.333,790.508,2855.917L790.508,2863.5"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_F18_F19_0" d="M790.508,3017.5L790.508,3023.75C790.508,3030,790.508,3042.5,790.508,3054.333C790.508,3066.167,790.508,3077.333,790.508,3082.917L790.508,3088.5"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_F19_F20_0" d="M790.508,3194.5L790.508,3200.75C790.508,3207,790.508,3219.5,790.582,3231.417C790.657,3243.333,790.806,3254.667,790.881,3260.334L790.955,3266"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_F20_F21_0" d="M791.008,3529.344L790.924,3535.51C790.841,3541.677,790.674,3554.01,790.666,3565.844C790.657,3577.677,790.806,3589.011,790.881,3594.677L790.955,3600.344"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_F21_F22_0" d="M791.008,4083.625L790.924,4089.792C790.841,4095.958,790.674,4108.292,790.666,4120.125C790.657,4131.958,790.806,4143.292,790.881,4148.959L790.955,4154.625"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_F22_F23_0" d="M741.865,4275.482L717.918,4291.839C693.971,4308.196,646.077,4340.911,622.13,4376.851C598.184,4412.792,598.184,4451.958,598.184,4471.542L598.184,4491.125"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_F22_F24_0" d="M830.018,4285.615L843.016,4300.283C856.014,4314.952,882.011,4344.288,895.086,4366.623C908.161,4388.958,908.314,4404.292,908.391,4411.958L908.468,4419.625"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_F24_F25_0" d="M855.619,4568.737L835.963,4585.718C816.307,4602.699,776.996,4636.662,757.34,4661.227C737.684,4685.792,737.684,4700.958,737.684,4708.542L737.684,4716.125"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_F24_F26_0" d="M955.282,4574.851L969.57,4590.813C983.857,4606.775,1012.433,4638.7,1026.72,4664.246C1041.008,4689.792,1041.008,4708.958,1041.008,4718.542L1041.008,4728.125"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_F26_F27_0" d="M1041.008,4786.125L1041.008,4794.375C1041.008,4802.625,1041.008,4819.125,1041.008,4832.958C1041.008,4846.792,1041.008,4857.958,1041.008,4863.542L1041.008,4869.125"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_F27_F28_0" d="M1041.008,4927.125L1041.008,4933.375C1041.008,4939.625,1041.008,4952.125,1041.008,4963.958C1041.008,4975.792,1041.008,4986.958,1041.008,4992.542L1041.008,4998.125"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_F28_F29_0" d="M1041.008,5056.125L1041.008,5062.375C1041.008,5068.625,1041.008,5081.125,1041.008,5092.958C1041.008,5104.792,1041.008,5115.958,1041.008,5121.542L1041.008,5127.125"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_F29_F30_0" d="M1041.008,5185.125L1041.008,5191.375C1041.008,5197.625,1041.008,5210.125,1041.082,5222.042C1041.157,5233.958,1041.306,5245.292,1041.381,5250.959L1041.455,5256.625"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_F30_F31_0" d="M995.98,5365.097L971.431,5380.852C946.881,5396.606,897.782,5428.116,873.233,5453.454C848.684,5478.792,848.684,5497.958,848.684,5507.542L848.684,5517.125"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_F30_F32_0" d="M1079.003,5373.13L1093.415,5387.546C1107.828,5401.961,1136.652,5430.793,1151.064,5452.792C1165.477,5474.792,1165.477,5489.958,1165.477,5497.542L1165.477,5505.125"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_F32_F33_0" d="M1165.477,5587.125L1165.477,5593.375C1165.477,5599.625,1165.477,5612.125,1165.477,5623.958C1165.477,5635.792,1165.477,5646.958,1165.477,5652.542L1165.477,5658.125"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_F33_F34_0" d="M1165.477,5716.125L1165.477,5722.375C1165.477,5728.625,1165.477,5741.125,1165.551,5753.042C1165.626,5764.958,1165.775,5776.292,1165.849,5781.959L1165.924,5787.625"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_F34_F35_0" d="M1116.111,5923.759L1095.951,5940.237C1075.791,5956.714,1035.472,5989.67,1015.312,6013.731C995.152,6037.792,995.152,6052.958,995.152,6060.542L995.152,6068.125"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_F34_F36_0" d="M1215.842,5923.759L1235.835,5940.237C1255.829,5956.714,1295.815,5989.67,1315.808,6013.731C1335.801,6037.792,1335.801,6052.958,1335.801,6060.542L1335.801,6068.125"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_F35_F37_0" d="M995.152,6174.125L995.152,6180.375C995.152,6186.625,995.152,6199.125,1011.033,6211.389C1026.914,6223.653,1058.676,6235.681,1074.556,6241.695L1090.437,6247.708"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_F36_F37_0" d="M1335.801,6174.125L1335.801,6180.375C1335.801,6186.625,1335.801,6199.125,1319.92,6211.389C1304.039,6223.653,1272.277,6235.681,1256.397,6241.695L1240.516,6247.708"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_F37_F38_0" d="M1165.477,6303.125L1165.477,6309.375C1165.477,6315.625,1165.477,6328.125,1165.477,6339.958C1165.477,6351.792,1165.477,6362.958,1165.477,6368.542L1165.477,6374.125"/></g><g class="edgeLabels"><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g transform="translate(200.56640625, 1210)" class="edgeLabel"><g transform="translate(-8, -12)" class="label"><foreignObject height="24" width="16"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>是</p></span></div></foreignObject></g></g><g transform="translate(524.26953125, 1210)" class="edgeLabel"><g transform="translate(-8, -12)" class="label"><foreignObject height="24" width="16"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>否</p></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g transform="translate(331.9453125, 1660)" class="edgeLabel"><g transform="translate(-8, -12)" class="label"><foreignObject height="24" width="16"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>是</p></span></div></foreignObject></g></g><g transform="translate(657.26953125, 1660)" class="edgeLabel"><g transform="translate(-8, -12)" class="label"><foreignObject height="24" width="16"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>否</p></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g transform="translate(464.9453125, 2818)" class="edgeLabel"><g transform="translate(-8, -12)" class="label"><foreignObject height="24" width="16"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>否</p></span></div></foreignObject></g></g><g transform="translate(790.5078125, 2818)" class="edgeLabel"><g transform="translate(-8, -12)" class="label"><foreignObject height="24" width="16"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>是</p></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g transform="translate(598.18359375, 4373.625)" class="edgeLabel"><g transform="translate(-8, -12)" class="label"><foreignObject height="24" width="16"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>是</p></span></div></foreignObject></g></g><g transform="translate(908.0078125, 4373.625)" class="edgeLabel"><g transform="translate(-8, -12)" class="label"><foreignObject height="24" width="16"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>否</p></span></div></foreignObject></g></g><g transform="translate(737.68359375, 4670.625)" class="edgeLabel"><g transform="translate(-8, -12)" class="label"><foreignObject height="24" width="16"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>是</p></span></div></foreignObject></g></g><g transform="translate(1041.0078125, 4670.625)" class="edgeLabel"><g transform="translate(-8, -12)" class="label"><foreignObject height="24" width="16"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>否</p></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g transform="translate(848.68359375, 5459.625)" class="edgeLabel"><g transform="translate(-8, -12)" class="label"><foreignObject height="24" width="16"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>否</p></span></div></foreignObject></g></g><g transform="translate(1165.4765625, 5459.625)" class="edgeLabel"><g transform="translate(-8, -12)" class="label"><foreignObject height="24" width="16"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>是</p></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g transform="translate(995.15234375, 6022.625)" class="edgeLabel"><g transform="translate(-8, -12)" class="label"><foreignObject height="24" width="16"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>是</p></span></div></foreignObject></g></g><g transform="translate(1335.80078125, 6022.625)" class="edgeLabel"><g transform="translate(-8, -12)" class="label"><foreignObject height="24" width="16"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>否</p></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g></g><g class="nodes"><g transform="translate(392.890625, 84.5)" id="flowchart-F1-182" class="node default"><rect height="78" width="458.265625" y="-39" x="-229.1328125" style="" class="basic label-container"/><g transform="translate(-199.1328125, -24)" style="" class="label"><rect/><foreignObject height="48" width="398.265625"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>sendAwardDealerService.sendAwardDealerByOrderNew 发放经销商奖励</p></span></div></foreignObject></g></g><g transform="translate(392.890625, 249.5)" id="flowchart-F2-183" class="node default"><rect height="102" width="260" y="-51" x="-130" style="" class="basic label-container"/><g transform="translate(-100, -36)" style="" class="label"><rect/><foreignObject height="72" width="200"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>获取dealerOutTime<br />从码信息中获取经销商出库时间</p></span></div></foreignObject></g></g><g transform="translate(392.890625, 438.5)" id="flowchart-F3-185" class="node default"><rect height="126" width="260" y="-63" x="-130" style="" class="basic label-container"/><g transform="translate(-100, -48)" style="" class="label"><rect/><foreignObject height="96" width="200"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>获取收货类型名称<br />根据receiptType获取收货类型:扫码收货/出库收货/一键收货/经理代收</p></span></div></foreignObject></g></g><g transform="translate(392.890625, 627.5)" id="flowchart-F4-187" class="node default"><rect height="102" width="260" y="-51" x="-130" style="" class="basic label-container"/><g transform="translate(-100, -36)" style="" class="label"><rect/><foreignObject height="72" width="200"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>获取订单信息<br />从TerminalScanDetailModel中获取订单相关信息</p></span></div></foreignObject></g></g><g transform="translate(392.890625, 804.5)" id="flowchart-F5-189" class="node default"><rect height="102" width="260" y="-51" x="-130" style="" class="basic label-container"/><g transform="translate(-100, -36)" style="" class="label"><rect/><foreignObject height="72" width="200"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>初始化ExtendDataBean<br />封装扩展数据:商品名称、数量、终端名称等</p></span></div></foreignObject></g></g><g transform="translate(392.890625, 1045.5)" id="flowchart-F6-191" class="node default"><polygon transform="translate(-115,115)" class="label-container" points="115,0 230,-115 115,-230 0,-115"/><g transform="translate(-88, -12)" style="" class="label"><rect/><foreignObject height="24" width="176"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>检查码信息列表是否为空</p></span></div></foreignObject></g></g><g transform="translate(200.56640625, 1298.5)" id="flowchart-F7-193" class="node default"><rect height="54" width="172" y="-27" x="-86" style="" class="basic label-container"/><g transform="translate(-56, -12)" style="" class="label"><rect/><foreignObject height="24" width="112"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>记录日志并返回</p></span></div></foreignObject></g></g><g transform="translate(524.26953125, 1298.5)" id="flowchart-F8-195" class="node default"><rect height="78" width="253.515625" y="-39" x="-126.7578125" style="" class="basic label-container"/><g transform="translate(-96.7578125, -24)" style="" class="label"><rect/><foreignObject height="48" width="193.515625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>获取经销商编码<br />从码信息中获取dealerCode</p></span></div></foreignObject></g></g><g transform="translate(524.26953125, 1511.5)" id="flowchart-F9-197" class="node default"><polygon transform="translate(-99,99)" class="label-container" points="99,0 198,-99 99,-198 0,-99"/><g transform="translate(-72, -12)" style="" class="label"><rect/><foreignObject height="24" width="144"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>经销商编码是否为空</p></span></div></foreignObject></g></g><g transform="translate(331.9453125, 1784.5)" id="flowchart-F10-199" class="node default"><rect height="54" width="172" y="-27" x="-86" style="" class="basic label-container"/><g transform="translate(-56, -12)" style="" class="label"><rect/><foreignObject height="24" width="112"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>记录日志并返回</p></span></div></foreignObject></g></g><g transform="translate(657.26953125, 1784.5)" id="flowchart-F11-201" class="node default"><rect height="150" width="260" y="-75" x="-130" style="" class="basic label-container"/><g transform="translate(-100, -60)" style="" class="label"><rect/><foreignObject height="120" width="200"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>查询经销商信息<br />SELECT * FROM t_cloud_dealer_info WHERE dealerCode=dealerCode</p></span></div></foreignObject></g></g><g transform="translate(657.26953125, 1985.5)" id="flowchart-F12-203" class="node default"><rect height="102" width="260" y="-51" x="-130" style="" class="basic label-container"/><g transform="translate(-100, -36)" style="" class="label"><rect/><foreignObject height="72" width="200"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>按商品分组码信息<br />将码信息按商品编码进行分组</p></span></div></foreignObject></g></g><g transform="translate(657.26953125, 2138.5)" id="flowchart-F13-205" class="node default"><rect height="54" width="156" y="-27" x="-78" style="" class="basic label-container"/><g transform="translate(-48, -12)" style="" class="label"><rect/><foreignObject height="24" width="96"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>遍历商品分组</p></span></div></foreignObject></g></g><g transform="translate(657.26953125, 2279.5)" id="flowchart-F14-207" class="node default"><rect height="78" width="220" y="-39" x="-110" style="" class="basic label-container"/><g transform="translate(-80, -24)" style="" class="label"><rect/><foreignObject height="48" width="160"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>计算商品数量<br />统计当前商品的总数量</p></span></div></foreignObject></g></g><g transform="translate(657.26953125, 2468.5)" id="flowchart-F15-209" class="node default"><rect height="150" width="319.328125" y="-75" x="-159.6640625" style="" class="basic label-container"/><g transform="translate(-129.6640625, -60)" style="" class="label"><rect/><foreignObject height="120" width="259.328125"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>查询终端合同<br />SELECT * FROM t_terminal_shop_contract_common WHERE memberShopId=shopId ORDER BY id DESC LIMIT 1</p></span></div></foreignObject></g></g><g transform="translate(657.26953125, 2693.5)" id="flowchart-F16-211" class="node default"><polygon transform="translate(-75,75)" class="label-container" points="75,0 150,-75 75,-150 0,-75"/><g transform="translate(-48, -12)" style="" class="label"><rect/><foreignObject height="24" width="96"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>合同是否存在</p></span></div></foreignObject></g></g><g transform="translate(464.9453125, 2942.5)" id="flowchart-F17-213" class="node default"><rect height="54" width="172" y="-27" x="-86" style="" class="basic label-container"/><g transform="translate(-56, -12)" style="" class="label"><rect/><foreignObject height="24" width="112"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>记录日志并返回</p></span></div></foreignObject></g></g><g transform="translate(790.5078125, 2942.5)" id="flowchart-F18-215" class="node default"><rect height="150" width="260.953125" y="-75" x="-130.4765625" style="" class="basic label-container"/><g transform="translate(-100.4765625, -60)" style="" class="label"><rect/><foreignObject height="120" width="200.953125"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>查询合同信息<br />SELECT * FROM t_dealer_contract_rel WHERE contractCode=contractCode</p></span></div></foreignObject></g></g><g transform="translate(790.5078125, 3143.5)" id="flowchart-F19-217" class="node default"><rect height="102" width="260" y="-51" x="-130" style="" class="basic label-container"/><g transform="translate(-100, -36)" style="" class="label"><rect/><foreignObject height="72" width="200"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>设置经销商类型<br />从合同信息中获取经销商类型</p></span></div></foreignObject></g></g><g transform="translate(790.5078125, 3399.171875)" id="flowchart-F20-219" class="node default"><polygon transform="translate(-129.671875,129.671875)" class="label-container" points="129.671875,0 259.34375,-129.671875 129.671875,-259.34375 0,-129.671875"/><g transform="translate(-90.671875, -24)" style="" class="label"><rect/><foreignObject height="48" width="181.34375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>检查重复处理<br />使用Redis锁防止重复处理</p></span></div></foreignObject></g></g><g transform="translate(790.5078125, 3843.484375)" id="flowchart-F21-221" class="node default"><polygon transform="translate(-239.640625,239.640625)" class="label-container" points="239.640625,0 479.28125,-239.640625 239.640625,-479.28125 0,-239.640625"/><g transform="translate(-128.640625, -96)" style="" class="label"><rect/><foreignObject height="192" width="257.28125"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>检查是否已发放奖励<br />SELECT * FROM t_activity_reward_record WHERE originId=detailId AND activityType=1 AND eventType=4 AND originTable=t_terminal_scan_detail AND isDelete=0 LIMIT 1</p></span></div></foreignObject></g></g><g transform="translate(790.5078125, 4241.125)" id="flowchart-F22-223" class="node default"><polygon transform="translate(-83,83)" class="label-container" points="83,0 166,-83 83,-166 0,-83"/><g transform="translate(-56, -12)" style="" class="label"><rect/><foreignObject height="24" width="112"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>是否已发放奖励</p></span></div></foreignObject></g></g><g transform="translate(598.18359375, 4522.125)" id="flowchart-F23-225" class="node default"><rect height="54" width="172" y="-27" x="-86" style="" class="basic label-container"/><g transform="translate(-56, -12)" style="" class="label"><rect/><foreignObject height="24" width="112"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>记录日志并返回</p></span></div></foreignObject></g></g><g transform="translate(908.0078125, 4522.125)" id="flowchart-F24-227" class="node default"><polygon transform="translate(-99,99)" class="label-container" points="99,0 198,-99 99,-198 0,-99"/><g transform="translate(-72, -12)" style="" class="label"><rect/><foreignObject height="24" width="144"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>检查是否为餐饮终端</p></span></div></foreignObject></g></g><g transform="translate(737.68359375, 4759.125)" id="flowchart-F25-229" class="node default"><rect height="78" width="260" y="-39" x="-130" style="" class="basic label-container"/><g transform="translate(-100, -24)" style="" class="label"><rect/><foreignObject height="48" width="200"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>记录日志并返回 禁用餐饮终端动销奖励</p></span></div></foreignObject></g></g><g transform="translate(1041.0078125, 4759.125)" id="flowchart-F26-231" class="node default"><rect height="54" width="172" y="-27" x="-86" style="" class="basic label-container"/><g transform="translate(-56, -12)" style="" class="label"><rect/><foreignObject height="24" width="112"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>查询经销商活动</p></span></div></foreignObject></g></g><g transform="translate(1041.0078125, 4900.125)" id="flowchart-F27-233" class="node default"><rect height="54" width="172" y="-27" x="-86" style="" class="basic label-container"/><g transform="translate(-56, -12)" style="" class="label"><rect/><foreignObject height="24" width="112"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>筛选匹配的活动</p></span></div></foreignObject></g></g><g transform="translate(1041.0078125, 5029.125)" id="flowchart-F28-235" class="node default"><rect height="54" width="156" y="-27" x="-78" style="" class="basic label-container"/><g transform="translate(-48, -12)" style="" class="label"><rect/><foreignObject height="24" width="96"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>遍历活动列表</p></span></div></foreignObject></g></g><g transform="translate(1041.0078125, 5158.125)" id="flowchart-F29-237" class="node default"><rect height="54" width="156" y="-27" x="-78" style="" class="basic label-container"/><g transform="translate(-48, -12)" style="" class="label"><rect/><foreignObject height="24" width="96"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>检查活动条件</p></span></div></foreignObject></g></g><g transform="translate(1041.0078125, 5335.125)" id="flowchart-F30-239" class="node default"><polygon transform="translate(-75,75)" class="label-container" points="75,0 150,-75 75,-150 0,-75"/><g transform="translate(-48, -12)" style="" class="label"><rect/><foreignObject height="24" width="96"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>条件是否满足</p></span></div></foreignObject></g></g><g transform="translate(848.68359375, 5548.125)" id="flowchart-F31-241" class="node default"><rect height="54" width="172" y="-27" x="-86" style="" class="basic label-container"/><g transform="translate(-56, -12)" style="" class="label"><rect/><foreignObject height="24" width="112"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>继续下一个活动</p></span></div></foreignObject></g></g><g transform="translate(1165.4765625, 5548.125)" id="flowchart-F32-243" class="node default"><rect height="78" width="225.875" y="-39" x="-112.9375" style="" class="basic label-container"/><g transform="translate(-82.9375, -24)" style="" class="label"><rect/><foreignObject height="48" width="165.875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>计算奖励金额<br />活动奖励金额*商品数量</p></span></div></foreignObject></g></g><g transform="translate(1165.4765625, 5689.125)" id="flowchart-F33-245" class="node default"><rect height="54" width="156" y="-27" x="-78" style="" class="basic label-container"/><g transform="translate(-48, -12)" style="" class="label"><rect/><foreignObject height="24" width="96"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>判断其他规则</p></span></div></foreignObject></g></g><g transform="translate(1165.4765625, 5882.125)" id="flowchart-F34-247" class="node default"><polygon transform="translate(-91,91)" class="label-container" points="91,0 182,-91 91,-182 0,-91"/><g transform="translate(-64, -12)" style="" class="label"><rect/><foreignObject height="24" width="128"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>是否满足其他规则</p></span></div></foreignObject></g></g><g transform="translate(995.15234375, 6123.125)" id="flowchart-F35-249" class="node default"><rect height="102" width="260" y="-51" x="-130" style="" class="basic label-container"/><g transform="translate(-100, -36)" style="" class="label"><rect/><foreignObject height="72" width="200"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>插入活动奖励记录<br />INSERT INTO t_activity_reward_record</p></span></div></foreignObject></g></g><g transform="translate(1335.80078125, 6123.125)" id="flowchart-F36-251" class="node default"><rect height="102" width="321.296875" y="-51" x="-160.6484375" style="" class="basic label-container"/><g transform="translate(-130.6484375, -36)" style="" class="label"><rect/><foreignObject height="72" width="261.296875"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>插入异常奖励记录<br />INSERT INTO t_activity_reward_exception_record</p></span></div></foreignObject></g></g><g transform="translate(1165.4765625, 6276.125)" id="flowchart-F37-253" class="node default"><rect height="54" width="201.484375" y="-27" x="-100.7421875" style="" class="basic label-container"/><g transform="translate(-70.7421875, -12)" style="" class="label"><rect/><foreignObject height="24" width="141.484375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>更新扫码明细活动id</p></span></div></foreignObject></g></g><g transform="translate(1165.4765625, 6405.125)" id="flowchart-F38-257" class="node default"><rect height="54" width="92" y="-27" x="-46" style="" class="basic label-container"/><g transform="translate(-16, -12)" style="" class="label"><rect/><foreignObject height="24" width="32"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>结束</p></span></div></foreignObject></g></g></g></g><g transform="translate(4190.2109375, 47)" id="flowchart-A-0" class="node default"><rect height="78" width="400.578125" y="-39" x="-200.2890625" style="" class="basic label-container"/><g transform="translate(-170.2890625, -24)" style="" class="label"><rect/><foreignObject height="48" width="340.578125"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>ConsumerCheckListener.rewardDetailReceiving 处理detailId级别的奖励接收</p></span></div></foreignObject></g></g><g transform="translate(4190.2109375, 238.5234375)" id="flowchart-B-1" class="node default"><polygon transform="translate(-102.5234375,102.5234375)" class="label-container" points="102.5234375,0 205.046875,-102.5234375 102.5234375,-205.046875 0,-102.5234375"/><g transform="translate(-75.5234375, -12)" style="" class="label"><rect/><foreignObject height="24" width="151.046875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>检查detailId是否为空</p></span></div></foreignObject></g></g><g transform="translate(4018.87890625, 466.046875)" id="flowchart-C-3" class="node default"><rect height="54" width="172" y="-27" x="-86" style="" class="basic label-container"/><g transform="translate(-56, -12)" style="" class="label"><rect/><foreignObject height="24" width="112"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>记录日志并返回</p></span></div></foreignObject></g></g><g transform="translate(4361.54296875, 466.046875)" id="flowchart-D-5" class="node default"><rect height="102" width="413.328125" y="-51" x="-206.6640625" style="" class="basic label-container"/><g transform="translate(-176.6640625, -36)" style="" class="label"><rect/><foreignObject height="72" width="353.328125"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>调用rewardReceivingService.dealDetailDealerReward 处理经销商奖励</p></span></div></foreignObject></g></g><g transform="translate(2671.18359375, 618.046875)" id="flowchart-E-7" class="node default"><rect height="102" width="428.671875" y="-51" x="-214.3359375" style="" class="basic label-container"/><g transform="translate(-184.3359375, -36)" style="" class="label"><rect/><foreignObject height="72" width="368.671875"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>调用rewardReceivingService.dealDetailTerminalReward 处理终端奖励</p></span></div></foreignObject></g></g><g transform="translate(6196.9765625, 783.046875)" id="flowchart-F-9" class="node default"><rect height="54" width="124" y="-27" x="-62" style="" class="basic label-container"/><g transform="translate(-32, -12)" style="" class="label"><rect/><foreignObject height="24" width="64"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>结束处理</p></span></div></foreignObject></g></g><g transform="translate(7935.8359375, 3198.859375)" id="flowchart-D1-10" class="node default"><rect height="78" width="260" y="-39" x="-130" style="" class="basic label-container"/><g transform="translate(-100, -24)" style="" class="label"><rect/><foreignObject height="48" width="200"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>dealDetailDealerReward detailId</p></span></div></foreignObject></g></g><g transform="translate(7935.8359375, 3678.15625)" id="flowchart-D2-11" class="node default"><polygon transform="translate(-94.5234375,94.5234375)" class="label-container" points="94.5234375,0 189.046875,-94.5234375 94.5234375,-189.046875 0,-94.5234375"/><g transform="translate(-67.5234375, -12)" style="" class="label"><rect/><foreignObject height="24" width="135.046875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>检查detailId有效性</p></span></div></foreignObject></g></g><g transform="translate(7799.68359375, 4170.1640625)" id="flowchart-D3-13" class="node default"><rect height="54" width="253.640625" y="-27" x="-126.8203125" style="" class="basic label-container"/><g transform="translate(-96.8203125, -12)" style="" class="label"><rect/><foreignObject height="24" width="193.640625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>抛出BusinessException异常</p></span></div></foreignObject></g></g><g transform="translate(8107.98828125, 4170.1640625)" id="flowchart-D4-15" class="node default"><rect height="150" width="262.96875" y="-75" x="-131.484375" style="" class="basic label-container"/><g transform="translate(-101.484375, -60)" style="" class="label"><rect/><foreignObject height="120" width="202.96875"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>查询TerminalScanDetailModel<br />根据detailId查询t_terminal_scan_detail_plus表</p></span></div></foreignObject></g></g><g transform="translate(8191.23046875, 4643.796875)" id="flowchart-D5-17" class="node default"><rect height="150" width="315.046875" y="-75" x="-157.5234375" style="" class="basic label-container"/><g transform="translate(-127.5234375, -60)" style="" class="label"><rect/><foreignObject height="120" width="255.046875"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>查询TerminalScanBalanceCommonModel<br />根据balanceId查询t_terminal_scan_balance_common表</p></span></div></foreignObject></g></g><g transform="translate(8191.23046875, 5137.8046875)" id="flowchart-D6-19" class="node default"><rect height="126" width="315.046875" y="-63" x="-157.5234375" style="" class="basic label-container"/><g transform="translate(-127.5234375, -48)" style="" class="label"><rect/><foreignObject height="96" width="255.046875"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>获取receiptType<br />从TerminalScanBalanceCommonModel中获取isOneFlag字段</p></span></div></foreignObject></g></g><g transform="translate(8191.23046875, 5495.8125)" id="flowchart-D7-21" class="node default"><rect height="54" width="243.953125" y="-27" x="-121.9765625" style="" class="basic label-container"/><g transform="translate(-91.9765625, -12)" style="" class="label"><rect/><foreignObject height="24" width="183.953125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>调用dealDetailDealer方法</p></span></div></foreignObject></g></g><g transform="translate(8191.23046875, 5707.8125)" id="flowchart-D8-23" class="node default"><rect height="54" width="211.953125" y="-27" x="-105.9765625" style="" class="basic label-container"/><g transform="translate(-75.9765625, -12)" style="" class="label"><rect/><foreignObject height="24" width="151.953125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>dealDetailDealer方法</p></span></div></foreignObject></g></g><g transform="translate(8191.23046875, 6031.7890625)" id="flowchart-D9-25" class="node default"><polygon transform="translate(-210.9765625,210.9765625)" class="label-container" points="210.9765625,0 421.953125,-210.9765625 210.9765625,-421.953125 0,-210.9765625"/><g transform="translate(-147.9765625, -48)" style="" class="label"><rect/><foreignObject height="96" width="295.953125"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>检查是否已处理经销商奖励<br />判断TerminalScanDetailModel.isDealerReward是否等于1</p></span></div></foreignObject></g></g><g transform="translate(7941.42578125, 6501.546875)" id="flowchart-D10-27" class="node default"><rect height="54" width="204" y="-27" x="-102" style="" class="basic label-container"/><g transform="translate(-72, -12)" style="" class="label"><rect/><foreignObject height="24" width="144"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>记录日志并抛出异常</p></span></div></foreignObject></g></g><g transform="translate(8359.62109375, 6501.546875)" id="flowchart-D11-29" class="node default"><polygon transform="translate(-184.78125,184.78125)" class="label-container" points="184.78125,0 369.5625,-184.78125 184.78125,-369.5625 0,-184.78125"/><g transform="translate(-121.78125, -48)" style="" class="label"><rect/><foreignObject height="96" width="243.5625"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>检查是否已删除<br />判断TerminalScanDetailModel.isDelete是否等于1</p></span></div></foreignObject></g></g><g transform="translate(8109.81640625, 6960.25)" id="flowchart-D12-31" class="node default"><rect height="54" width="204" y="-27" x="-102" style="" class="basic label-container"/><g transform="translate(-72, -12)" style="" class="label"><rect/><foreignObject height="24" width="144"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>记录日志并抛出异常</p></span></div></foreignObject></g></g><g transform="translate(8535.58203125, 6960.25)" id="flowchart-D13-33" class="node default"><polygon transform="translate(-199.921875,199.921875)" class="label-container" points="199.921875,0 399.84375,-199.921875 199.921875,-399.84375 0,-199.921875"/><g transform="translate(-136.921875, -48)" style="" class="label"><rect/><foreignObject height="96" width="273.84375"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>检查是否已退货<br />判断TerminalScanDetailModel.returnGoods是否等于1</p></span></div></foreignObject></g></g><g transform="translate(8285.77734375, 7414.546875)" id="flowchart-D14-35" class="node default"><rect height="54" width="204" y="-27" x="-102" style="" class="basic label-container"/><g transform="translate(-72, -12)" style="" class="label"><rect/><foreignObject height="24" width="144"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>记录日志并抛出异常</p></span></div></foreignObject></g></g><g transform="translate(8701.76953125, 7414.546875)" id="flowchart-D15-37" class="node default"><polygon transform="translate(-180.375,180.375)" class="label-container" points="180.375,0 360.75,-180.375 180.375,-360.75 0,-180.375"/><g transform="translate(-117.375, -48)" style="" class="label"><rect/><foreignObject height="96" width="234.75"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>检查qrcode是否为空<br />判断TerminalScanDetailModel.qrcode是否为空</p></span></div></foreignObject></g></g><g transform="translate(8451.96484375, 7866.6640625)" id="flowchart-D16-39" class="node default"><rect height="54" width="204" y="-27" x="-102" style="" class="basic label-container"/><g transform="translate(-72, -12)" style="" class="label"><rect/><foreignObject height="24" width="144"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>记录日志并抛出异常</p></span></div></foreignObject></g></g><g transform="translate(8999.62890625, 7866.6640625)" id="flowchart-D17-41" class="node default"><polygon transform="translate(-197.7421875,197.7421875)" class="label-container" points="197.7421875,0 395.484375,-197.7421875 197.7421875,-395.484375 0,-197.7421875"/><g transform="translate(-110.7421875, -72)" style="" class="label"><rect/><foreignObject height="144" width="221.484375"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>检查是否已发放经销商奖励<br />查询t_cloud_dealer_reward_record表<br />条件:detailId=id AND isDelete=0 AND originType=1</p></span></div></foreignObject></g></g><g transform="translate(8626.8359375, 8362.953125)" id="flowchart-D18-43" class="node default"><rect height="54" width="204" y="-27" x="-102" style="" class="basic label-container"/><g transform="translate(-72, -12)" style="" class="label"><rect/><foreignObject height="24" width="144"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>记录日志并抛出异常</p></span></div></foreignObject></g></g><g transform="translate(9187.90234375, 8362.953125)" id="flowchart-D19-45" class="node default"><polygon transform="translate(-224.546875,224.546875)" class="label-container" points="224.546875,0 449.09375,-224.546875 224.546875,-449.09375 0,-224.546875"/><g transform="translate(-173.546875, -36)" style="" class="label"><rect/><foreignObject height="72" width="347.09375"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>设置Redis锁防止重复处理<br />Key: REDIS_AWARD_DEALER_TERMINAL_SCAN+detailId</p></span></div></foreignObject></g></g><g transform="translate(9187.90234375, 8812.5)" id="flowchart-D20-47" class="node default"><polygon transform="translate(-75,75)" class="label-container" points="75,0 150,-75 75,-150 0,-75"/><g transform="translate(-48, -12)" style="" class="label"><rect/><foreignObject height="24" width="96"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>是否获取到锁</p></span></div></foreignObject></g></g><g transform="translate(8815.109375, 9124.5)" id="flowchart-D21-49" class="node default"><rect height="54" width="204" y="-27" x="-102" style="" class="basic label-container"/><g transform="translate(-72, -12)" style="" class="label"><rect/><foreignObject height="24" width="144"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>记录日志并抛出异常</p></span></div></foreignObject></g></g><g transform="translate(9356.15625, 9124.5)" id="flowchart-D22-51" class="node default"><rect height="126" width="369.015625" y="-63" x="-184.5078125" style="" class="basic label-container"/><g transform="translate(-154.5078125, -48)" style="" class="label"><rect/><foreignObject height="96" width="309.015625"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>调用webCodeCommonComponent.dealCodeInfo获取码信息<br />根据qrcode获取码的详细信息</p></span></div></foreignObject></g></g><g transform="translate(9356.15625, 13068.53125)" id="flowchart-D23-53" class="node default"><polygon transform="translate(-99,99)" class="label-container" points="99,0 198,-99 99,-198 0,-99"/><g transform="translate(-72, -12)" style="" class="label"><rect/><foreignObject height="24" width="144"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>获取码信息是否成功</p></span></div></foreignObject></g></g><g transform="translate(8997.36328125, 17112.5625)" id="flowchart-D24-55" class="node default"><rect height="102" width="260" y="-51" x="-130" style="" class="basic label-container"/><g transform="translate(-100, -36)" style="" class="label"><rect/><foreignObject height="72" width="200"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>记录TerminalScanRecordModel并返回</p></span></div></foreignObject></g></g><g transform="translate(9511.15625, 17112.5625)" id="flowchart-D25-57" class="node default"><rect height="174" width="260" y="-87" x="-130" style="" class="basic label-container"/><g transform="translate(-100, -72)" style="" class="label"><rect/><foreignObject height="144" width="200"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>更新TerminalScanDetailModel的码信息<br />将码信息更新到TerminalScanDetailModel对象中</p></span></div></foreignObject></g></g><g transform="translate(9511.15625, 17400.5625)" id="flowchart-D26-59" class="node default"><rect height="150" width="275.390625" y="-75" x="-137.6953125" style="" class="basic label-container"/><g transform="translate(-107.6953125, -60)" style="" class="label"><rect/><foreignObject height="120" width="215.390625"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>更新TerminalScanDetailCodeModel<br />更新t_terminal_scan_detail_code表中detail_id对应的记录</p></span></div></foreignObject></g></g><g transform="translate(9640.9296875, 17576.5625)" id="flowchart-D27-61" class="node default"><rect height="102" width="260" y="-51" x="-130" style="" class="basic label-container"/><g transform="translate(-100, -36)" style="" class="label"><rect/><foreignObject height="72" width="200"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>查询ShopModel<br />根据shopId查询t_shop表获取终端信息</p></span></div></foreignObject></g></g><g transform="translate(9640.9296875, 17790.6796875)" id="flowchart-D28-63" class="node default"><polygon transform="translate(-113.1171875,113.1171875)" class="label-container" points="113.1171875,0 226.234375,-113.1171875 113.1171875,-226.234375 0,-113.1171875"/><g transform="translate(-86.1171875, -12)" style="" class="label"><rect/><foreignObject height="24" width="172.234375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>查询ShopModel是否成功</p></span></div></foreignObject></g></g><g transform="translate(9138.36328125, 18040.796875)" id="flowchart-D29-65" class="node default"><rect height="54" width="204" y="-27" x="-102" style="" class="basic label-container"/><g transform="translate(-72, -12)" style="" class="label"><rect/><foreignObject height="24" width="144"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>记录日志并抛出异常</p></span></div></foreignObject></g></g><g transform="translate(9864.4296875, 18040.796875)" id="flowchart-D30-67" class="node default"><rect height="126" width="260" y="-63" x="-130" style="" class="basic label-container"/><g transform="translate(-100, -48)" style="" class="label"><rect/><foreignObject height="96" width="200"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>查询CloudDealerInfoModel<br />根据dealerCode查询t_cloud_dealer_info表获取经销商信息</p></span></div></foreignObject></g></g><g transform="translate(9864.4296875, 18192.796875)" id="flowchart-D31-69" class="node default"><rect height="78" width="252" y="-39" x="-126" style="" class="basic label-container"/><g transform="translate(-96, -24)" style="" class="label"><rect/><foreignObject height="48" width="192"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>初始化RewardReason<br />创建奖励发放原因记录对象</p></span></div></foreignObject></g></g><g transform="translate(9864.4296875, 18456.796875)" id="flowchart-D32-71" class="node default"><polygon transform="translate(-175,175)" class="label-container" points="175,0 350,-175 175,-350 0,-175"/><g transform="translate(-100, -60)" style="" class="label"><rect/><foreignObject height="120" width="200"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>判断dealerInfo.getAccountType和shopInfo.getIsMember<br />判断经销商类型和终端是否为会员</p></span></div></foreignObject></g></g><g transform="translate(9342.9296875, 18781.796875)" id="flowchart-D33-73" class="node default"><rect height="102" width="458.265625" y="-51" x="-229.1328125" style="" class="basic label-container"/><g transform="translate(-199.1328125, -36)" style="" class="label"><rect/><foreignObject height="72" width="398.265625"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>调用sendAwardDealerService.sendAwardDealerByOrderNew 发放经销商奖励</p></span></div></foreignObject></g></g><g transform="translate(10068.99609375, 18781.796875)" id="flowchart-D34-75" class="node default"><rect height="78" width="260" y="-39" x="-130" style="" class="basic label-container"/><g transform="translate(-100, -24)" style="" class="label"><rect/><foreignObject height="48" width="200"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>设置rewardReason.isSave=true</p></span></div></foreignObject></g></g><g transform="translate(9992.49609375, 22113.609375)" id="flowchart-D35-77" class="node default"><rect height="102" width="394.171875" y="-51" x="-197.0859375" style="" class="basic label-container"/><g transform="translate(-167.0859375, -36)" style="" class="label"><rect/><foreignObject height="72" width="334.171875"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>更新TerminalScanDetailModel.setIsDealerReward=1<br />标记经销商奖励已处理</p></span></div></foreignObject></g></g><g transform="translate(9992.49609375, 25533.421875)" id="flowchart-D36-81" class="node default"><polygon transform="translate(-139,139)" class="label-container" points="139,0 278,-139 139,-278 0,-139"/><g transform="translate(-100, -24)" style="" class="label"><rect/><foreignObject height="48" width="200"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>rewardReason.isSave是否为true</p></span></div></foreignObject></g></g><g transform="translate(10195.0390625, 25797.421875)" id="flowchart-D37-83" class="node default"><rect height="102" width="260" y="-51" x="-130" style="" class="basic label-container"/><g transform="translate(-100, -36)" style="" class="label"><rect/><foreignObject height="72" width="200"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>保存或更新RewardReason<br />INSERT/UPDATE t_reward_reason表</p></span></div></foreignObject></g></g><g transform="translate(10108.5390625, 25925.421875)" id="flowchart-D38-85" class="node default"><rect height="54" width="226.4375" y="-27" x="-113.21875" style="" class="basic label-container"/><g transform="translate(-83.21875, -12)" style="" class="label"><rect/><foreignObject height="24" width="166.4375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>跳过保存RewardReason</p></span></div></foreignObject></g></g><g transform="translate(10108.5390625, 26029.421875)" id="flowchart-D39-89" class="node default"><rect height="54" width="145.34375" y="-27" x="-72.671875" style="" class="basic label-container"/><g transform="translate(-42.671875, -12)" style="" class="label"><rect/><foreignObject height="24" width="85.34375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>删除Redis锁</p></span></div></foreignObject></g></g><g transform="translate(10108.5390625, 26133.421875)" id="flowchart-D40-91" class="node default"><rect height="54" width="124" y="-27" x="-62" style="" class="basic label-container"/><g transform="translate(-32, -12)" style="" class="label"><rect/><foreignObject height="24" width="64"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>记录日志</p></span></div></foreignObject></g></g><g transform="translate(2155.48828125, 783.046875)" id="flowchart-E1-92" class="node default"><rect height="78" width="260" y="-39" x="-130" style="" class="basic label-container"/><g transform="translate(-100, -24)" style="" class="label"><rect/><foreignObject height="48" width="200"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>dealDetailTerminalReward detailId</p></span></div></foreignObject></g></g><g transform="translate(2155.48828125, 966.5703125)" id="flowchart-E2-93" class="node default"><polygon transform="translate(-94.5234375,94.5234375)" class="label-container" points="94.5234375,0 189.046875,-94.5234375 94.5234375,-189.046875 0,-94.5234375"/><g transform="translate(-67.5234375, -12)" style="" class="label"><rect/><foreignObject height="24" width="135.046875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>检查detailId有效性</p></span></div></foreignObject></g></g><g transform="translate(1991.9375, 1210.09375)" id="flowchart-E3-95" class="node default"><rect height="54" width="253.640625" y="-27" x="-126.8203125" style="" class="basic label-container"/><g transform="translate(-96.8203125, -12)" style="" class="label"><rect/><foreignObject height="24" width="193.640625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>抛出BusinessException异常</p></span></div></foreignObject></g></g><g transform="translate(2309.640625, 1210.09375)" id="flowchart-E4-97" class="node default"><rect height="150" width="262.96875" y="-75" x="-131.484375" style="" class="basic label-container"/><g transform="translate(-101.484375, -60)" style="" class="label"><rect/><foreignObject height="120" width="202.96875"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>查询TerminalScanDetailModel<br />根据detailId查询t_terminal_scan_detail_plus表</p></span></div></foreignObject></g></g><g transform="translate(2309.640625, 1410.09375)" id="flowchart-E5-99" class="node default"><rect height="150" width="315.046875" y="-75" x="-157.5234375" style="" class="basic label-container"/><g transform="translate(-127.5234375, -60)" style="" class="label"><rect/><foreignObject height="120" width="255.046875"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>查询TerminalScanBalanceCommonModel<br />根据balanceId查询t_terminal_scan_balance_common表</p></span></div></foreignObject></g></g><g transform="translate(2309.640625, 1598.09375)" id="flowchart-E6-101" class="node default"><rect height="126" width="315.046875" y="-63" x="-157.5234375" style="" class="basic label-container"/><g transform="translate(-127.5234375, -48)" style="" class="label"><rect/><foreignObject height="96" width="255.046875"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>获取receiptType<br />从TerminalScanBalanceCommonModel中获取isOneFlag字段</p></span></div></foreignObject></g></g><g transform="translate(2309.640625, 1750.09375)" id="flowchart-E7-103" class="node default"><rect height="78" width="260" y="-39" x="-130" style="" class="basic label-container"/><g transform="translate(-100, -24)" style="" class="label"><rect/><foreignObject height="48" width="200"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>初始化virtualAmountTwo为0</p></span></div></foreignObject></g></g><g transform="translate(2309.640625, 1866.09375)" id="flowchart-E8-105" class="node default"><rect height="54" width="259.296875" y="-27" x="-129.6484375" style="" class="basic label-container"/><g transform="translate(-99.6484375, -12)" style="" class="label"><rect/><foreignObject height="24" width="199.296875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>调用dealDetailTerminal方法</p></span></div></foreignObject></g></g><g transform="translate(2309.640625, 1970.09375)" id="flowchart-E9-107" class="node default"><rect height="54" width="227.296875" y="-27" x="-113.6484375" style="" class="basic label-container"/><g transform="translate(-83.6484375, -12)" style="" class="label"><rect/><foreignObject height="24" width="167.296875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>dealDetailTerminal方法</p></span></div></foreignObject></g></g><g transform="translate(2309.640625, 2251.734375)" id="flowchart-E10-109" class="node default"><polygon transform="translate(-204.640625,204.640625)" class="label-container" points="204.640625,0 409.28125,-204.640625 204.640625,-409.28125 0,-204.640625"/><g transform="translate(-141.640625, -48)" style="" class="label"><rect/><foreignObject height="96" width="283.28125"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>检查是否已处理终端奖励<br />判断TerminalScanDetailModel.isShopReward是否等于1</p></span></div></foreignObject></g></g><g transform="translate(2133.6796875, 2715.15625)" id="flowchart-E11-111" class="node default"><rect height="54" width="204" y="-27" x="-102" style="" class="basic label-container"/><g transform="translate(-72, -12)" style="" class="label"><rect/><foreignObject height="24" width="144"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>记录日志并抛出异常</p></span></div></foreignObject></g></g><g transform="translate(2478.03125, 2715.15625)" id="flowchart-E12-113" class="node default"><polygon transform="translate(-184.78125,184.78125)" class="label-container" points="184.78125,0 369.5625,-184.78125 184.78125,-369.5625 0,-184.78125"/><g transform="translate(-121.78125, -48)" style="" class="label"><rect/><foreignObject height="96" width="243.5625"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>检查是否已删除<br />判断TerminalScanDetailModel.isDelete是否等于1</p></span></div></foreignObject></g></g><g transform="translate(2302.0703125, 3198.859375)" id="flowchart-E13-115" class="node default"><rect height="54" width="204" y="-27" x="-102" style="" class="basic label-container"/><g transform="translate(-72, -12)" style="" class="label"><rect/><foreignObject height="24" width="144"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>记录日志并抛出异常</p></span></div></foreignObject></g></g><g transform="translate(2653.9921875, 3198.859375)" id="flowchart-E14-117" class="node default"><polygon transform="translate(-199.921875,199.921875)" class="label-container" points="199.921875,0 399.84375,-199.921875 199.921875,-399.84375 0,-199.921875"/><g transform="translate(-136.921875, -48)" style="" class="label"><rect/><foreignObject height="96" width="273.84375"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>检查是否已退货<br />判断TerminalScanDetailModel.returnGoods是否等于1</p></span></div></foreignObject></g></g><g transform="translate(2478.03125, 3678.15625)" id="flowchart-E15-119" class="node default"><rect height="54" width="204" y="-27" x="-102" style="" class="basic label-container"/><g transform="translate(-72, -12)" style="" class="label"><rect/><foreignObject height="24" width="144"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>记录日志并抛出异常</p></span></div></foreignObject></g></g><g transform="translate(2820.1796875, 3678.15625)" id="flowchart-E16-121" class="node default"><polygon transform="translate(-180.375,180.375)" class="label-container" points="180.375,0 360.75,-180.375 180.375,-360.75 0,-180.375"/><g transform="translate(-117.375, -48)" style="" class="label"><rect/><foreignObject height="96" width="234.75"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>检查qrcode是否为空<br />判断TerminalScanDetailModel.qrcode是否为空</p></span></div></foreignObject></g></g><g transform="translate(2644.21875, 4170.1640625)" id="flowchart-E17-123" class="node default"><rect height="54" width="204" y="-27" x="-102" style="" class="basic label-container"/><g transform="translate(-72, -12)" style="" class="label"><rect/><foreignObject height="24" width="144"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>记录日志并抛出异常</p></span></div></foreignObject></g></g><g transform="translate(3016.49609375, 4170.1640625)" id="flowchart-E18-125" class="node default"><polygon transform="translate(-212.6328125,212.6328125)" class="label-container" points="212.6328125,0 425.265625,-212.6328125 212.6328125,-425.265625 0,-212.6328125"/><g transform="translate(-113.6328125, -84)" style="" class="label"><rect/><foreignObject height="168" width="227.265625"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>检查是否已发放终端奖励<br />查询t_terminal_reward_record_new表<br />条件:detailId=id AND isDelete=0 AND source=1 AND shopId=shopId</p></span></div></foreignObject></g></g><g transform="translate(2826.53515625, 4643.796875)" id="flowchart-E19-127" class="node default"><rect height="54" width="204" y="-27" x="-102" style="" class="basic label-container"/><g transform="translate(-72, -12)" style="" class="label"><rect/><foreignObject height="24" width="144"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>记录日志并抛出异常</p></span></div></foreignObject></g></g><g transform="translate(3309.8125, 4643.796875)" id="flowchart-E20-129" class="node default"><polygon transform="translate(-187,187)" class="label-container" points="187,0 374,-187 187,-374 0,-187"/><g transform="translate(-100, -72)" style="" class="label"><rect/><foreignObject height="144" width="200"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>检查是否已计算终端积分<br />查询t_activity_reward_record表<br />条件:originId=id AND isDelete=0 AND activityType=1</p></span></div></foreignObject></g></g><g transform="translate(2996.03515625, 5137.8046875)" id="flowchart-E21-131" class="node default"><rect height="54" width="204" y="-27" x="-102" style="" class="basic label-container"/><g transform="translate(-72, -12)" style="" class="label"><rect/><foreignObject height="24" width="144"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>记录日志并抛出异常</p></span></div></foreignObject></g></g><g transform="translate(3502.31640625, 5137.8046875)" id="flowchart-E22-133" class="node default"><polygon transform="translate(-233.0078125,233.0078125)" class="label-container" points="233.0078125,0 466.015625,-233.0078125 233.0078125,-466.015625 0,-233.0078125"/><g transform="translate(-182.0078125, -36)" style="" class="label"><rect/><foreignObject height="72" width="364.015625"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>设置Redis锁防止重复处理<br />Key: REDIS_AWARD_TERMINAL_TERMINAL_SCAN+detailId</p></span></div></foreignObject></g></g><g transform="translate(3502.31640625, 5495.8125)" id="flowchart-E23-135" class="node default"><polygon transform="translate(-75,75)" class="label-container" points="75,0 150,-75 75,-150 0,-75"/><g transform="translate(-48, -12)" style="" class="label"><rect/><foreignObject height="24" width="96"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>是否获取到锁</p></span></div></foreignObject></g></g><g transform="translate(3188.5390625, 5707.8125)" id="flowchart-E24-137" class="node default"><rect height="54" width="204" y="-27" x="-102" style="" class="basic label-container"/><g transform="translate(-72, -12)" style="" class="label"><rect/><foreignObject height="24" width="144"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>记录日志并抛出异常</p></span></div></foreignObject></g></g><g transform="translate(3670.5703125, 5707.8125)" id="flowchart-E25-139" class="node default"><rect height="126" width="369.015625" y="-63" x="-184.5078125" style="" class="basic label-container"/><g transform="translate(-154.5078125, -48)" style="" class="label"><rect/><foreignObject height="96" width="309.015625"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>调用webCodeCommonComponent.dealCodeInfo获取码信息<br />根据qrcode获取码的详细信息</p></span></div></foreignObject></g></g><g transform="translate(3670.5703125, 6031.7890625)" id="flowchart-E26-141" class="node default"><polygon transform="translate(-99,99)" class="label-container" points="99,0 198,-99 99,-198 0,-99"/><g transform="translate(-72, -12)" style="" class="label"><rect/><foreignObject height="24" width="144"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>获取码信息是否成功</p></span></div></foreignObject></g></g><g transform="translate(3370.79296875, 6501.546875)" id="flowchart-E27-143" class="node default"><rect height="102" width="260" y="-51" x="-130" style="" class="basic label-container"/><g transform="translate(-100, -36)" style="" class="label"><rect/><foreignObject height="72" width="200"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>记录TerminalScanRecordModel并抛出异常</p></span></div></foreignObject></g></g><g transform="translate(3825.5703125, 6501.546875)" id="flowchart-E28-145" class="node default"><rect height="174" width="260" y="-87" x="-130" style="" class="basic label-container"/><g transform="translate(-100, -72)" style="" class="label"><rect/><foreignObject height="144" width="200"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>更新TerminalScanDetailModel的码信息<br />将码信息更新到TerminalScanDetailModel对象中</p></span></div></foreignObject></g></g><g transform="translate(3959.57421875, 6960.25)" id="flowchart-E29-147" class="node default"><rect height="102" width="260" y="-51" x="-130" style="" class="basic label-container"/><g transform="translate(-100, -36)" style="" class="label"><rect/><foreignObject height="72" width="200"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>查询ShopModel<br />根据shopId查询t_shop表获取终端信息</p></span></div></foreignObject></g></g><g transform="translate(3959.57421875, 7414.546875)" id="flowchart-E30-149" class="node default"><polygon transform="translate(-113.1171875,113.1171875)" class="label-container" points="113.1171875,0 226.234375,-113.1171875 113.1171875,-226.234375 0,-113.1171875"/><g transform="translate(-86.1171875, -12)" style="" class="label"><rect/><foreignObject height="24" width="172.234375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>查询ShopModel是否成功</p></span></div></foreignObject></g></g><g transform="translate(3511.79296875, 7866.6640625)" id="flowchart-E31-151" class="node default"><rect height="54" width="204" y="-27" x="-102" style="" class="basic label-container"/><g transform="translate(-72, -12)" style="" class="label"><rect/><foreignObject height="24" width="144"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>记录日志并抛出异常</p></span></div></foreignObject></g></g><g transform="translate(4198.66796875, 7866.6640625)" id="flowchart-E32-153" class="node default"><rect height="150" width="322.375" y="-75" x="-161.1875" style="" class="basic label-container"/><g transform="translate(-131.1875, -60)" style="" class="label"><rect/><foreignObject height="120" width="262.375"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>查询TerminalShopContractCommonModel<br />根据memberShopId查询t_terminal_shop_contract_common表获取终端合同</p></span></div></foreignObject></g></g><g transform="translate(4198.66796875, 8362.953125)" id="flowchart-E33-155" class="node default"><rect height="78" width="252" y="-39" x="-126" style="" class="basic label-container"/><g transform="translate(-96, -24)" style="" class="label"><rect/><foreignObject height="48" width="192"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>初始化RewardReason<br />创建奖励发放原因记录对象</p></span></div></foreignObject></g></g><g transform="translate(4198.66796875, 8812.5)" id="flowchart-E34-157" class="node default"><polygon transform="translate(-175,175)" class="label-container" points="175,0 350,-175 175,-350 0,-175"/><g transform="translate(-100, -60)" style="" class="label"><rect/><foreignObject height="120" width="200"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>判断dealerInfo.getAccountType和shopInfo.getIsMember<br />判断经销商类型和终端是否为会员</p></span></div></foreignObject></g></g><g transform="translate(3726.0234375, 9124.5)" id="flowchart-E35-159" class="node default"><rect height="102" width="434.546875" y="-51" x="-217.2734375" style="" class="basic label-container"/><g transform="translate(-187.2734375, -36)" style="" class="label"><rect/><foreignObject height="72" width="374.546875"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>调用terminalRewardRecordService.computeShopReward 计算终端奖励</p></span></div></foreignObject></g></g><g transform="translate(4397.3046875, 9124.5)" id="flowchart-E36-161" class="node default"><rect height="78" width="260" y="-39" x="-130" style="" class="basic label-container"/><g transform="translate(-100, -24)" style="" class="label"><rect/><foreignObject height="48" width="200"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>设置rewardReason.isSave=true</p></span></div></foreignObject></g></g><g transform="translate(4310.8046875, 13068.53125)" id="flowchart-E37-163" class="node default"><rect height="126" width="381.5" y="-63" x="-190.75" style="" class="basic label-container"/><g transform="translate(-160.75, -48)" style="" class="label"><rect/><foreignObject height="96" width="321.5"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>更新TerminalScanDetailModel.setIsShopReward=1和virtualAmount<br />标记终端奖励已处理并设置虚拟金额</p></span></div></foreignObject></g></g><g transform="translate(4310.8046875, 17112.5625)" id="flowchart-E38-167" class="node default"><polygon transform="translate(-139,139)" class="label-container" points="139,0 278,-139 139,-278 0,-139"/><g transform="translate(-100, -24)" style="" class="label"><rect/><foreignObject height="48" width="200"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>rewardReason.isSave是否为true</p></span></div></foreignObject></g></g><g transform="translate(4397.3046875, 17400.5625)" id="flowchart-E39-169" class="node default"><rect height="102" width="260" y="-51" x="-130" style="" class="basic label-container"/><g transform="translate(-100, -36)" style="" class="label"><rect/><foreignObject height="72" width="200"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>保存或更新RewardReason<br />INSERT/UPDATE t_reward_reason表</p></span></div></foreignObject></g></g><g transform="translate(4310.8046875, 17576.5625)" id="flowchart-E40-171" class="node default"><rect height="54" width="226.4375" y="-27" x="-113.21875" style="" class="basic label-container"/><g transform="translate(-83.21875, -12)" style="" class="label"><rect/><foreignObject height="24" width="166.4375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>跳过保存RewardReason</p></span></div></foreignObject></g></g><g transform="translate(4310.8046875, 17790.6796875)" id="flowchart-E41-175" class="node default"><rect height="78" width="260" y="-39" x="-130" style="" class="basic label-container"/><g transform="translate(-100, -24)" style="" class="label"><rect/><foreignObject height="48" width="200"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>更新TerminalScanDetailModel</p></span></div></foreignObject></g></g><g transform="translate(4310.8046875, 18040.796875)" id="flowchart-E42-177" class="node default"><rect height="102" width="260" y="-51" x="-130" style="" class="basic label-container"/><g transform="translate(-100, -36)" style="" class="label"><rect/><foreignObject height="72" width="200"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>调用integralSyncService.send同步积分</p></span></div></foreignObject></g></g><g transform="translate(4310.8046875, 18192.796875)" id="flowchart-E43-179" class="node default"><rect height="54" width="145.34375" y="-27" x="-72.671875" style="" class="basic label-container"/><g transform="translate(-42.671875, -12)" style="" class="label"><rect/><foreignObject height="24" width="85.34375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>删除Redis锁</p></span></div></foreignObject></g></g><g transform="translate(4310.8046875, 18456.796875)" id="flowchart-E44-181" class="node default"><rect height="54" width="124" y="-27" x="-62" style="" class="basic label-container"/><g transform="translate(-32, -12)" style="" class="label"><rect/><foreignObject height="24" width="64"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>记录日志</p></span></div></foreignObject></g></g><g transform="translate(5806.03125, 3678.15625)" id="flowchart-DB1-350" class="node default"><rect height="174" width="267.796875" y="-87" x="-133.8984375" style="" class="basic label-container"/><g transform="translate(-103.8984375, -72)" style="" class="label"><rect/><foreignObject height="144" width="207.796875"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>查询TerminalScanDetailModel<br />SELECT * FROM t_terminal_scan_detail_plus WHERE id=detailId<br />获取扫码收货详情信息</p></span></div></foreignObject></g></g><g transform="translate(6457.453125, 3678.15625)" id="flowchart-DB2-351" class="node default"><rect height="174" width="315.046875" y="-87" x="-157.5234375" style="" class="basic label-container"/><g transform="translate(-127.5234375, -72)" style="" class="label"><rect/><foreignObject height="144" width="255.046875"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>查询TerminalScanBalanceCommonModel<br />SELECT * FROM t_terminal_scan_balance_common WHERE id=balanceId<br />获取扫码收货主表信息</p></span></div></foreignObject></g></g><g transform="translate(6119.9296875, 3678.15625)" id="flowchart-DB3-352" class="node default"><rect height="126" width="260" y="-63" x="-130" style="" class="basic label-container"/><g transform="translate(-100, -48)" style="" class="label"><rect/><foreignObject height="96" width="200"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>查询ShopModel<br />SELECT * FROM t_shop WHERE id=shopId<br />获取终端店铺信息</p></span></div></foreignObject></g></g><g transform="translate(7136.2734375, 3678.15625)" id="flowchart-DB4-353" class="node default"><rect height="150" width="270" y="-75" x="-135" style="" class="basic label-container"/><g transform="translate(-105, -60)" style="" class="label"><rect/><foreignObject height="120" width="210"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>查询CloudDealerInfoModel<br />SELECT * FROM t_cloud_dealer_info WHERE dealerCode=shop.dealerCode<br />获取经销商信息</p></span></div></foreignObject></g></g><g transform="translate(5460.9453125, 3678.15625)" id="flowchart-DB5-354" class="node default"><rect height="174" width="322.375" y="-87" x="-161.1875" style="" class="basic label-container"/><g transform="translate(-131.1875, -72)" style="" class="label"><rect/><foreignObject height="144" width="262.375"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>查询TerminalShopContractCommonModel<br />SELECT * FROM t_terminal_shop_contract_common WHERE memberShopId=shopId<br />获取终端合同信息</p></span></div></foreignObject></g></g><g transform="translate(6808.125, 3678.15625)" id="flowchart-DB6-355" class="node default"><rect height="198" width="286.296875" y="-99" x="-143.1484375" style="" class="basic label-container"/><g transform="translate(-113.1484375, -84)" style="" class="label"><rect/><foreignObject height="168" width="226.296875"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>检查是否已发放经销商奖励<br />SELECT * FROM t_cloud_dealer_reward_record WHERE detailId=id AND isDelete=0 AND originType=1 LIMIT 1<br />检查经销商奖励是否已发放</p></span></div></foreignObject></g></g><g transform="translate(4793.71875, 3678.15625)" id="flowchart-DB7-356" class="node default"><rect height="198" width="292.078125" y="-99" x="-146.0390625" style="" class="basic label-container"/><g transform="translate(-116.0390625, -84)" style="" class="label"><rect/><foreignObject height="168" width="232.078125"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>检查是否已发放终端奖励<br />SELECT * FROM t_terminal_reward_record_new WHERE detailId=id AND isDelete=0 AND source=1 AND shopId=shopId LIMIT 1<br />检查终端奖励是否已发放</p></span></div></foreignObject></g></g><g transform="translate(5119.7578125, 3678.15625)" id="flowchart-DB8-357" class="node default"><rect height="198" width="260" y="-99" x="-130" style="" class="basic label-container"/><g transform="translate(-100, -84)" style="" class="label"><rect/><foreignObject height="168" width="200"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>检查是否已计算终端积分<br />SELECT * FROM t_activity_reward_record WHERE originId=id AND isDelete=0 AND activityType=1 LIMIT 1<br />检查终端积分是否已计算</p></span></div></foreignObject></g></g><g transform="translate(7451.2734375, 3678.15625)" id="flowchart-DB9-358" class="node default"><rect height="126" width="260" y="-63" x="-130" style="" class="basic label-container"/><g transform="translate(-100, -48)" style="" class="label"><rect/><foreignObject height="96" width="200"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>保存或更新RewardReason<br />INSERT/UPDATE t_reward_reason<br />保存奖励发放原因记录</p></span></div></foreignObject></g></g></g></g></g></svg>