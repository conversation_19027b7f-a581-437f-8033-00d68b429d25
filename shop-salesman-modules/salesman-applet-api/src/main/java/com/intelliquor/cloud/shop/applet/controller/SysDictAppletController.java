package com.intelliquor.cloud.shop.applet.controller;

import com.intelliquor.cloud.shop.common.exception.RestResponse;
import com.intelliquor.cloud.shop.common.model.SystemDictModel;
import com.intelliquor.cloud.shop.common.model.UserContext;
import com.intelliquor.cloud.shop.common.service.SystemDictService;
import com.intelliquor.cloud.shop.salesmanManage.util.MapUtils;
import com.intelliquor.cloud.shop.salesmanManage.util.constants.ColumnConstant;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 云销小程序-基础字典内容获取
 *
 * @Auther: tianms
 * @Date: 2021/03/26 15:31
 */
@RestController
@RequestMapping("/applet/system/dict")
public class SysDictAppletController {

    @Autowired
    private SystemDictService systemDictService;

    @Autowired
    private UserContext userContext;

    /**
     * 根据字典编码查询数据
     *
     * @param code 字典编码
     * @return com.intelliquor.cloud.shop.common.exception.RestResponse<java.util.List < com.intelliquor.cloud.shop.common.model.SystemDictModel>>
     * @auther: tms
     * @date: 2021/03/26 15:34
     */
    @GetMapping("/queryListByDictCode")
    public RestResponse<List<SystemDictModel>> queryListByCode(@RequestParam("code") String code) {
        MapUtils mapUtils = new MapUtils(ColumnConstant.CODE, code);
        mapUtils.put(ColumnConstant.COMPANY_ID,userContext.getUserInfo().getIntCompanyId());
        List<SystemDictModel> systemDictList = systemDictService.queryNoParentDictList(mapUtils);
        return RestResponse.success(systemDictList);
    }

}
