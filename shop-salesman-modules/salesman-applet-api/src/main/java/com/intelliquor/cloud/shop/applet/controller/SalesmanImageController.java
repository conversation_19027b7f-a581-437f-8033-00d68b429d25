package com.intelliquor.cloud.shop.applet.controller;

import com.intelliquor.cloud.shop.common.entity.Response;
import com.intelliquor.cloud.shop.common.utils.OssUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

/**
 * 云销图片上传
 * <AUTHOR>
 */
@RestController
@RequestMapping("/salesman/image")
public class SalesmanImageController {
    @Autowired
    private OssUtil ossUtil;

    /**
     * 云销图片上传接口
     * <AUTHOR>
     */
    @RequestMapping(value = "/upload",method = RequestMethod.POST)
    public Response<String> uploadImg(@RequestParam("file") MultipartFile file){
        String fileName = file.getOriginalFilename();
        String fileSuffix = fileName.substring(fileName.lastIndexOf(".") + 1);
//        String fileSuffix = file.getOriginalFilename().split("[.]")[1];
        if(!"jpg".equalsIgnoreCase(fileSuffix) && !"jpeg".equalsIgnoreCase(fileSuffix) && !"png".equalsIgnoreCase(fileSuffix) &&
                !"bmp".equalsIgnoreCase(fileSuffix) && !"gif".equalsIgnoreCase(fileSuffix)){
            return Response.fail("请上传jpg，jpeg，png,bmp,gif格式的图片");
        }
        try {
            byte[] bs = file.getBytes();
            String url = ossUtil.byteUpload(bs,fileSuffix);
            return Response.ok(url);
        }catch (Exception e){
            e.printStackTrace();
            return Response.fail("系统异常");

        }



    }


}
