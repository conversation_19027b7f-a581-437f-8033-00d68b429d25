package tasly.tasting.adapter.api.tasting.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import tasly.tasting.adapter.common.RestResponse;
import tasly.tasting.client.tasting.api.TastingInvitationAppService;
import tasly.tasting.domain.tasting.enums.InvitationTypeEnum;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/7/12 15:37
 */
@Slf4j
@RestController
@RequestMapping("/v1/tastingInvitation")
@Tag(name = "品鉴会邀请函")
public class TastingInvitationController {

    @Resource
    private TastingInvitationAppService tastingInvitationAppService;

    @GetMapping("/queryTastingInvitation")
    @Operation(summary = "获取品鉴会邀请函", description = "获取品鉴会邀请函")
    public RestResponse queryTastingInvitation(String tastingId,String width,String channelCode){
        return RestResponse.success(tastingInvitationAppService.queryTastingInvitation(tastingId,channelCode,width, InvitationTypeEnum.INVITE.name()));
    }

    @GetMapping("/queryTastingSign")
    @Operation(summary = "获取品鉴会签到二维码", description = "获取品鉴会签到二维码")
    public RestResponse queryTastingSign(String tastingId,String width){
        return RestResponse.success(tastingInvitationAppService.queryTastingInvitation(tastingId,null,width, InvitationTypeEnum.SIGN.name()));
    }
}
