package tasly.tasting.adapter.injector;

import com.baomidou.mybatisplus.core.injector.AbstractMethod;
import com.baomidou.mybatisplus.core.injector.DefaultSqlInjector;
import com.baomidou.mybatisplus.core.metadata.TableInfo;
import com.baomidou.mybatisplus.extension.injector.methods.AlwaysUpdateSomeColumnById;
import com.baomidou.mybatisplus.extension.injector.methods.InsertBatchSomeColumn;
import com.baomidou.mybatisplus.extension.injector.methods.LogicDeleteByIdWithFill;
import tasly.tasting.adapter.injector.method.SelectIgnoreLogicDelete;
import tasly.tasting.adapter.injector.method.SelectIgnoreLogicDeleteByMap;

import java.util.List;

/**
 * Description:
 *
 * <AUTHOR>
 * @since 2023/4/20 09:42
 */
public class MySqlInjector extends DefaultSqlInjector {

    @Override
    public List<AbstractMethod> getMethodList(Class<?> mapperClass, TableInfo tableInfo) {
        List<AbstractMethod> methodList = super.getMethodList(mapperClass, tableInfo);
        // 添加InsertBatchSomeColumn方法
        methodList.add(new InsertBatchSomeColumn().setPredicate(t -> !t.isLogicDelete() || !"reversion".equals(t.getProperty())));
        methodList.add(new LogicDeleteByIdWithFill());
        methodList.add(new AlwaysUpdateSomeColumnById());
        methodList.add(new SelectIgnoreLogicDeleteByMap());
        methodList.add(new SelectIgnoreLogicDelete());
        return methodList;
    }
}
