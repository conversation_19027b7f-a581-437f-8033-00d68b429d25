package tasly.tasting.infra.tasting.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.springframework.stereotype.Repository;
import tasly.tasting.domain.tasting.TastingEntity;
import tasly.tasting.domain.tasting.TastingPrizeWhiteListEntity;
import tasly.tasting.domain.tasting.repository.TastingPrizeWhiteListRepository;
import tasly.tasting.infra.tasting.repository.mapper.TastingPrizeWhiteListMapper;

import javax.annotation.Resource;
import java.util.List;

@Repository
public class TastingPrizeWhiteListRepositoryImpl implements TastingPrizeWhiteListRepository {

    @Resource
    private TastingPrizeWhiteListMapper tastingPrizeWhiteListMapper;
    @Override
    public List<TastingPrizeWhiteListEntity> queryTastingPrizeWhiteListEntityList(Long tastingId, Long prizeItemId) {

        return tastingPrizeWhiteListMapper.selectList(Wrappers.lambdaQuery(TastingPrizeWhiteListEntity.class)
                .eq(TastingPrizeWhiteListEntity::getTastingId,tastingId)
                .eq(TastingPrizeWhiteListEntity::getPrizeItemId,prizeItemId));
    }
}
