package tasly.tasting.infra.tasting.repository;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.springframework.stereotype.Repository;
import tasly.tasting.domain.tasting.TastingSpiritTaskEntity;
import tasly.tasting.domain.tasting.enums.TastingSpiritTaskStatusEnum;
import tasly.tasting.domain.tasting.repository.TastingSpiritTaskRepository;
import tasly.tasting.infra.tasting.repository.mapper.TastingSpiritTaskMapper;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/7/6 17:10
 */
@Repository
public class TastingSpiritTaskRepositoryImpl implements TastingSpiritTaskRepository {

    @Resource
    private TastingSpiritTaskMapper tastingSpiritTaskMapper;

    @Override
    public void submit(Long id) {
        TastingSpiritTaskEntity entity = new TastingSpiritTaskEntity();
        entity.setId(id);
        entity.setStatus(TastingSpiritTaskStatusEnum.COMMIT.getValue());
        tastingSpiritTaskMapper.updateById(entity);
    }

    @Override
    public void save(TastingSpiritTaskEntity taskEntity) {
        if (Objects.isNull(taskEntity.getId())) {
            tastingSpiritTaskMapper.insert(taskEntity);
        } else {
            tastingSpiritTaskMapper.updateById(taskEntity);
        }
    }

    @Override
    public TastingSpiritTaskEntity getById(Long id) {
        return tastingSpiritTaskMapper.selectById(id);
    }

    @Override
    public TastingSpiritTaskEntity getByTastingId(Long tastingId) {
        return tastingSpiritTaskMapper.selectOne(
                Wrappers.lambdaQuery(TastingSpiritTaskEntity.class)
                        .eq(TastingSpiritTaskEntity::getTastingId, tastingId)
        );
    }
}
