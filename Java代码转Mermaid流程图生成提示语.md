# Java代码转Mermaid流程图生成提示语

## 任务目标
根据提供的Java代码生成详细的Mermaid流程图，完整展示方法调用链条和业务逻辑流程。

## 分析要求

### 1. 调用链追踪
- 从指定的起始方法开始分析
- 深度递归追踪该方法的所有调用链条
- 识别并包含所有被调用方法的完整实现内容
- 标记方法调用的层级关系和依赖关系
- 追踪跨类和跨包的方法调用

### 2. 代码内容分析
- 仔细阅读每个方法的具体实现逻辑
- 识别关键的业务处理步骤
- 分析条件判断、循环结构、异常处理等控制流程
- 识别数据库操作、外部服务调用等重要操作
- 分析方法参数传递和返回值处理

### 3. 复杂度判断标准
以下情况需要创建独立的子流程图：
- 方法内部逻辑超过10行有效代码
- 包含多个条件分支或嵌套结构
- 存在循环处理逻辑
- 包含异常处理机制
- 涉及复杂的数据转换或计算

## 流程图生成规范

### 4. 节点信息要求
每个流程图节点必须包含：
- **方法标识**：完整的方法名（保持原始大小写）
- **中文说明**：该方法或步骤的功能描述
- **参数信息**：关键参数的说明（如有必要）
- **返回值说明**：重要返回值的描述（如有必要）

### 5. 节点命名规范
- 使用英文字母和数字组合作为节点ID
- 避免使用特殊符号如 `-`、`_`、`@`、`#` 等
- 避免使用中文符号如 `（）`、`。`、`，` 等
- 节点ID格式：`nodeA1`、`processB2`、`checkC3` 等
- 保持节点ID简洁且具有辨识度

### 6. 流程图结构设计

#### 主流程图
```mermaid
flowchart TD
    start[开始] --> mainMethod[主方法名称 - 主要功能说明]
    mainMethod --> subProcess1[子流程1 - 功能说明]
    subProcess1 --> decision1{条件判断 - 判断内容}
    decision1 -->|条件1| process1[处理流程1 - 处理说明]
    decision1 -->|条件2| process2[处理流程2 - 处理说明]
    process1 --> callMethod1[调用方法1 - 方法功能]
    process2 --> callMethod2[调用方法2 - 方法功能]
    callMethod1 --> result[返回结果 - 结果说明]
    callMethod2 --> result
    result --> endNode[结束]
```

#### 子流程图链接
- 复杂方法使用 `click` 语法链接到独立的子流程图
- 子流程图命名格式：`subgraph_方法名`
- 子流程图应独立完整，可单独理解

### 7. 特殊结构处理

#### 条件判断
```mermaid
decision{判断条件说明}
decision -->|true| trueProcess[真值处理 - 处理说明]
decision -->|false| falseProcess[假值处理 - 处理说明]
```

#### 循环结构
```mermaid
loopStart[循环开始 - 循环条件]
loopStart --> loopBody[循环体 - 循环处理内容]
loopBody --> loopCheck{循环条件检查}
loopCheck -->|继续| loopBody
loopCheck -->|结束| loopEnd[循环结束]
```

#### 异常处理
```mermaid
tryBlock[尝试执行 - 主要逻辑]
tryBlock --> success[执行成功 - 成功处理]
tryBlock --> exception[异常捕获 - 异常类型]
exception --> errorHandler[异常处理 - 处理方式]
```

### 8. 输出格式要求

#### 完整输出结构
1. **主流程图**：展示整体调用流程
2. **子流程图集合**：复杂方法的详细流程
3. **流程说明**：关键节点和流程的文字说明
4. **方法调用关系图**：可选的方法依赖关系图

#### 代码格式
- 使用标准的Mermaid语法
- 确保图形可以正确渲染
- 合理使用换行和缩进保持代码可读性
- 添加必要的注释说明

### 9. 质量检查标准
- 流程逻辑完整性：确保所有方法调用都被覆盖
- 节点信息准确性：方法名和功能说明准确无误
- 图形美观性：布局清晰，线条不交叉
- 语法正确性：Mermaid语法完全正确
- 中文说明清晰：避免歧义，表达准确

### 10. 示例模板

#### 输入格式
```
请分析以下Java方法的调用流程：
[起始方法名称]

[Java代码]
```

#### 输出格式
```
## 主流程图
[Mermaid代码]

## 子流程图
### [子流程名称]
[Mermaid代码]

## 流程说明
[文字说明]
```

## 注意事项
1. 仔细阅读所有相关的Java代码文件
2. 确保理解业务逻辑后再生成流程图
3. 优先保证流程完整性和准确性
4. 适当简化非核心的细节处理
5. 保持中文说明的专业性和准确性
6. 确保生成的流程图具有实际参考价值

请按照以上要求分析提供的Java代码，生成相应的Mermaid流程图。
