CREATE TABLE `t_zt_book_reward_record`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `zy_type` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '对应智盈type 1.控盘分利 2.用酒 3.批量奖扣',
  `role_type` int(11) NULL DEFAULT NULL COMMENT '账号类型(1：普通经销商,2：体验中心,3：普通分销商,4：合伙人, 5: 终端，6:平台公司)',
  `business_id` varchar(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '对应预算或上账对应的业务id',
  `business_table` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '记录来源业务主表',
  `channel_code` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '经销商编码',
  `store_code` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '终端编码',
  `mapping_code` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '预算控制表映射编码',
  `account_type` int(11) NULL DEFAULT NULL COMMENT '账户类型：经销商：1.动销奖励 2.开瓶奖励 3.终端使用积分4.会员使用积分5.宴席开瓶奖励6.宴席推广支持分销商使用积分  终端：',
  `contract_code` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '合同编码',
  `contract_type` tinyint(4) NULL DEFAULT NULL COMMENT '合同类型',
  `change_type` int(11) NULL DEFAULT NULL COMMENT '上账类型 1：入账；3：扣减',
  `account` decimal(10, 2) NULL DEFAULT NULL COMMENT '上账金额',
  `request_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '请求地址',
  `book_request_json` json NULL COMMENT '上账请求json',
  `callback_json` json NULL COMMENT '回调json',
  `send_zt_msg` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '预算接口返回信息',
  `sys_state` tinyint(4) NULL DEFAULT 0 COMMENT '同步中台预算状态(1:推送成功(需回调);2:推送失败;3:中台处理成功;4:中台预算失败)',
  `call_state` tinyint(4) NULL DEFAULT 0 COMMENT '回调状态 0是未回调 1是回调成功 2是处理上账成功',
  `company_id` int(11) NULL DEFAULT NULL COMMENT '商户id',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `callback_time` datetime NULL DEFAULT NULL COMMENT '回调时间',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `is_delete` int(1) NULL DEFAULT 0 COMMENT '是否删除0-否1-是',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `channelcode`(`channel_code`) USING BTREE,
  INDEX `sys_state`(`sys_state`) USING BTREE,
  INDEX `contract_code`(`contract_code`) USING BTREE,
  INDEX `mapping_code`(`mapping_code`) USING BTREE,
  INDEX `business_id`(`business_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 9 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '中台预算对账流水表' ROW_FORMAT = Dynamic;
