ALTER TABLE t_display_result MODIFY COLUMN compute_type tinyint(4) NULL COMMENT '计算类型(1:主协议的月度奖励  2、超级终端月度奖励  3、陈列月度奖励（算附加协议） 4、超年度包量奖励)  5、终端年度包量奖励  6、终端陈列搭赠 7、进货达成奖励 8、终端开瓶激励搭赠 9、终端开瓶激励奖励 10、季度奖励 11、终端宴席季度奖励 12、推介人奖励 13、终端奖励 14、其他奖励-终端奖励 15、宴席活动奖励-终端奖励 16、消费者奖励 17、终端宴席开瓶奖励 21、终端随单赠酒';
ALTER TABLE t_display_result MODIFY COLUMN source_flag tinyint(1) NULL COMMENT '数据来源(0:手动导入;1:正常计算;2:不在计算范围;3:手动写入MQ;4:宴席系统推送)';
ALTER TABLE t_display_result ADD business_code varchar(64) NULL COMMENT '业务编号：宴席奖励-宴席活动单号';
ALTER TABLE t_display_result MODIFY COLUMN signed_agreement_level_config text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '终端签约协议等级标准配置 config;宴席奖励记录宴席推送数据';

CREATE TABLE `t_terminal_banquet_reward_record` (
    `id` bigint(11) NOT NULL AUTO_INCREMENT COMMENT '自增主键',
    `activity_code` varchar(64) DEFAULT NULL COMMENT '活动类型',
    `activity_name` varchar(64) DEFAULT NULL COMMENT '活动类型名称',
    `activity_instance_bill_code` varchar(64) DEFAULT NULL COMMENT '宴席活动单号',
    `bill_code` varchar(64) DEFAULT NULL COMMENT '上账流水号',
    `support_name` varchar(64) DEFAULT NULL COMMENT '科目',
    `support_amount` decimal(20,2) DEFAULT NULL COMMENT '科目金额',
    `account_subject` varchar(64) DEFAULT NULL COMMENT '上账科目',
    `fk_customer_id` varchar(64) DEFAULT NULL COMMENT '经销商主键',
    `dealer_code` varchar(64) DEFAULT NULL COMMENT '经销商编码',
    `dealer_name` varchar(64) DEFAULT NULL COMMENT '经销商名称',
    `fk_contract_id` varchar(64) DEFAULT NULL COMMENT '合同',
    `contract_code` varchar(64) DEFAULT NULL COMMENT '合同编码',
    `member_shop_id` int(11) DEFAULT NULL COMMENT 'member_shop表id',
    `mapping_code` varchar(64) DEFAULT NULL,
    `account_type` int(11) DEFAULT NULL,
    `execute_date` datetime DEFAULT NULL COMMENT '执行日期',
    `execute_time` varchar(64) DEFAULT NULL COMMENT '执行时间',
    `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `create_by` varchar(64) DEFAULT NULL COMMENT '创建人',
    `update_by` varchar(64) DEFAULT NULL COMMENT '更新人',
    `is_delete` int(1) DEFAULT '0' COMMENT '是否删除(0:未删除,1已删除)',
    `remark` varchar(64) DEFAULT NULL COMMENT '扣减原因',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='宴席奖励数据记录';

CREATE TABLE `t_terminal_banquet_reward_record_data` (
     `id` bigint(11) NOT NULL AUTO_INCREMENT COMMENT '自增主键',
     `activity_code` varchar(64) DEFAULT NULL COMMENT '活动类型',
     `activity_name` varchar(64) DEFAULT NULL COMMENT '活动类型名称',
     `activity_instance_bill_code` varchar(64) DEFAULT NULL COMMENT '宴席活动单号',
     `bill_code` varchar(64) DEFAULT NULL COMMENT '上账流水号',
     `support_name` varchar(64) DEFAULT NULL COMMENT '科目',
     `support_amount` decimal(20,2) DEFAULT NULL COMMENT '科目金额',
     `account_subject` varchar(64) DEFAULT NULL COMMENT '上账科目',
     `fk_customer_id` varchar(64) DEFAULT NULL COMMENT '经销商主键',
     `dealer_code` varchar(64) DEFAULT NULL COMMENT '经销商编码',
     `dealer_name` varchar(64) DEFAULT NULL COMMENT '经销商名称',
     `fk_contract_id` varchar(64) DEFAULT NULL COMMENT '合同',
     `contract_code` varchar(64) DEFAULT NULL COMMENT '合同编码',
     `member_shop_id` int(11) DEFAULT NULL COMMENT 'member_shop表id',
     `mapping_code` varchar(64) DEFAULT NULL,
     `account_type` int(11) DEFAULT NULL,
     `execute_date` datetime DEFAULT NULL COMMENT '执行日期',
     `execute_time` varchar(64) DEFAULT NULL COMMENT '执行时间',
     `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
     `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
     `create_by` varchar(64) DEFAULT NULL COMMENT '创建人',
     `update_by` varchar(64) DEFAULT NULL COMMENT '更新人',
     `is_delete` int(1) DEFAULT '0' COMMENT '是否删除(0:未删除,1已删除)',
     `is_receive` int(1) DEFAULT '0' COMMENT '是否接收成功：0 否 1 是',
     `remark` varchar(64) DEFAULT NULL COMMENT '扣减原因',
     PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='宴席奖励数据原始记录表';