-- 在t_reward_activity表中增加奖励子类型字段
ALTER TABLE t_reward_activity ADD reward_sub_type varchar(20) NOT NULL COMMENT '奖励子类型：11-动销奖励普通类型，21-开瓶奖励普通类型，22-开瓶奖励激励类型';
-- 在t_reward_activity表中增加生产批次字段
ALTER TABLE t_reward_activity ADD production_batch_no varchar(500) NULL COMMENT '生产批次, 可能存在多个，分号分隔';
-- 在t_reward_activity表中增加国台出库开始的时间字段
ALTER TABLE t_reward_activity ADD gt_outbound_begin_time datetime NULL COMMENT '国台出库时间开始';
-- 在t_reward_activity表中增加国台出库结束的时间字段
ALTER TABLE t_reward_activity ADD gt_outbound_end_time datetime NULL COMMENT '国台出库时间结束';
-- 在t_reward_activity表中增加是否使用码标识
ALTER TABLE t_reward_activity ADD code_data_flag INT(1) DEFAULT 0 COMMENT '是否使用码，0-不使用，1-使用';
-- 在t_reward_activity表中增加码表id字段
ALTER TABLE t_reward_activity ADD code_data_ids varchar(500) NULL COMMENT '码表id，可能存在多个，分号分隔';
-- 在t_reward_activity表中增加经销商类型选择范围集合字段
ALTER TABLE t_reward_activity ADD terminal_types varchar(500) NULL COMMENT '终端类型范围，多个终端类型用逗号分隔';




-- 在t_cloud_dealer_activityNew表中增加奖励子类型字段
ALTER TABLE t_cloud_dealer_activityNew ADD reward_sub_type varchar(20) NOT NULL COMMENT '奖励子类型：11-动销奖励普通类型，21-开瓶奖励普通类型，22-开瓶奖励激励类型';
-- 在t_cloud_dealer_activityNew表中增加生产批次字段
ALTER TABLE t_cloud_dealer_activityNew ADD production_batch_no varchar(500) NULL COMMENT '生产批次，可能存在多个，分号分隔';
-- 在t_cloud_dealer_activityNew表中增加生产日期开始时间字段
ALTER TABLE t_cloud_dealer_activityNew ADD production_begin_time datetime NULL COMMENT '生产日期开始';
-- 在t_cloud_dealer_activityNew表中增加生产日期结束时间字段
ALTER TABLE t_cloud_dealer_activityNew ADD production_end_time datetime NULL COMMENT '生产日期结束';
-- 在t_cloud_dealer_activityNew表中增加国台出库开始的时间字段
ALTER TABLE t_cloud_dealer_activityNew ADD gt_outbound_begin_time datetime NULL COMMENT '国台出库时间开始';
-- 在t_cloud_dealer_activityNew表中增加国台出库结束的时间字段
ALTER TABLE t_cloud_dealer_activityNew ADD gt_outbound_end_time datetime NULL COMMENT '国台出库时间结束';
-- 在t_cloud_dealer_activityNew表中增加是否使用码标识
ALTER TABLE t_cloud_dealer_activityNew ADD code_data_flag INT(1) DEFAULT 0 COMMENT '是否使用码，0-不使用，1-使用';
-- 在t_cloud_dealer_activityNew表中增加码表id字段
ALTER TABLE t_cloud_dealer_activityNew ADD code_data_ids varchar(500) NULL COMMENT '码表id， 可能存在多个，分号分隔';
-- 在t_cloud_dealer_activityNew表中增加经销商类型选择范围集合字段
ALTER TABLE t_cloud_dealer_activityNew ADD dealer_types varchar(500) NULL COMMENT '经销商类型范围，多个经销商类型用逗号分隔';


CREATE TABLE t_activity_code (
     id BIGINT AUTO_INCREMENT PRIMARY KEY, -- 主键，自增
     rule_name VARCHAR(255) NOT NULL COMMENT '规则名称', -- 规则名称
     status TINYINT(1) DEFAULT 1 COMMENT '状态，0 表示禁用，1 表示启用', -- 状态，0 表示禁用，1 表示启用
     create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间', -- 创建时间，默认当前时间
     update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间', -- 更新时间，默认当前时间，在更新时自动更新
     create_by INT(64) NOT NULL COMMENT '创建人', -- 创建人
     update_by INT(64) NOT NULL COMMENT '更新人', -- 更新人
     is_delete TINYINT(1) DEFAULT 0 COMMENT '逻辑删除标识，0 表示未删除，1 表示已删除' -- 逻辑删除标识，0 表示未删除，1 表示已删除
)ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='活动码表';

CREATE INDEX idx_rule_name ON t_activity_code(rule_name);

CREATE TABLE t_activity_code_detail (
    id BIGINT AUTO_INCREMENT PRIMARY KEY, -- 主键，自增
    code_id BIGINT NOT NULL COMMENT '关联 t_cloud_dealer_activityNew_code 的记录', -- 关联 t_cloud_dealer_activityNew_code 的记录
    code VARCHAR(255) NOT NULL COMMENT '具体的码', -- 具体的码
    code_type INT(11) NOT NULL COMMENT '码类型 0 箱码，1 盒码，2 瓶码', -- 码类型
    status TINYINT(1) DEFAULT 1 COMMENT '状态，0 表示禁用，1 表示启用', -- 状态，0 表示禁用，1 表示启用
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间', -- 创建时间，默认当前时间
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间', -- 更新时间，默认当前时间，在更新时自动更新
    create_by INT(64) NOT NULL COMMENT '创建人', -- 创建人
    update_by INT(64) NOT NULL COMMENT '更新人', -- 更新人
    is_delete TINYINT(1) DEFAULT 0 COMMENT '逻辑删除标识，0 表示未删除，1 表示已删除', -- 逻辑删除标识，0 表示未删除，1 表示已删除
    CONSTRAINT fk_code_id FOREIGN KEY (code_id) REFERENCES t_activity_code(id) -- 外键约束
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='活动码详情表';

CREATE INDEX idx_code_id ON t_activity_code_detail(code_id);
CREATE INDEX idx_code ON t_activity_code_detail(code);
CREATE INDEX idx_code_type ON t_activity_code_detail(code_type);


-- -- todo 菜单配置：待确认生产环境id需要替换
-- -- 查询父级id
-- select  parent_id  from `dmp`.sys_permission sp where sp.system_code ="CLOUD_BUSINESS" and sp.name like "%奖励白名单%"
-- -- 查询序号
-- select order_num  from `dmp`.sys_permission  where parent_id =482 order by order_num desc
-- 例如：insert into `dmp`.`sys_permission` value(null, '482', 'CLOUD_BUSINESS', '码规则管理', '/codeRule/index', '', 'codeRuleIndex',2, '', 5,now(),now(),'');
-- 待执行：insert into `dmp`.`sys_permission` value(null, '替换查询父级id', 'CLOUD_BUSINESS', '码规则管理', '/codeRule/index', '', 'codeRuleIndex',2, '', 替换查询序号,now(),now(),'');

-- -- 查询菜单id
-- select  menu_id  from `dmp`.sys_permission sp where sp.system_code ="CLOUD_BUSINESS" and sp.name like "%码规则管理%"
-- 例如：insert into `dmp`.`sys_permission_company` value(null, '50', '1713', 'CLOUD_BUSINESS');
-- 待执行：insert into `dmp`.`sys_permission_company` value(null, '50', '执行上文insert语句后的id或上文查询的menu_id', 'CLOUD_BUSINESS');

ALTER TABLE t_activity_code_detail ADD batch_no varchar(100) NOT NULL COMMENT '导入批次';


-- 设置经销商动销活动的字类型为11
update t_cloud_dealer_activityNew
set reward_sub_type = '11'
where is_delete = 1
  and reward_type = 1;

-- 设置经销商开瓶活动的字类型为21
update t_cloud_dealer_activityNew
set reward_sub_type = '21'
where is_delete = 1
  and reward_type = 2;

-- 设置终端动销活动的字类型为11
update t_reward_activity
set reward_sub_type = '11'
where is_delete = 0
  and reward_type = 1;

-- 设置终端开瓶活动的字类型为21
update t_reward_activity
set reward_sub_type = '21'
where is_delete = 0
  and reward_type = 2;

