# AccountCashWayEnum 重构说明

## 概述

本次重构针对 `AccountCashWayEnum.getAccountCashWay()` 方法进行了优化，使用**策略模式 + 枚举映射**的方案替换了原有的复杂if-else嵌套结构。

## 重构背景

### 原有问题
1. **多重嵌套的if-else结构**：代码可读性差，维护困难
2. **硬编码的数字常量**：账户类型使用魔法数字，不够语义化
3. **重复的逻辑判断**：多处使用`Objects.equals()`进行类型比较
4. **扩展性差**：新增账户类型或奖励类型需要修改核心逻辑

### 原有代码结构
```java
public static AccountCashWayEnum getAccountCashWay(Integer accountType, Integer rewardType) {
    // 经销商(1,2,6)
    if (accountType == 1 || accountType == 2 || accountType == 6) {
        if (Objects.equals(rewardType, RewardType.SEND_SCORE_TYPE.getCode())) {
            return REWARD_ACCOUNT;
        } else if (Objects.equals(rewardType, RewardType.SEND_GIFT_TYPE.getCode())) {
            return DEALER_GIFT_POOL;
        }
    }
    // 分销商(3)、合伙人(4)
    else if (accountType == 3 || accountType == 4) {
        if (Objects.equals(rewardType, RewardType.SEND_SCORE_TYPE.getCode())) {
            return SCORE;
        }
    }
    // 终端(5)
    else if (accountType == 5) {
        if (Objects.equals(rewardType, RewardType.SEND_SCORE_TYPE.getCode()) ||
                Objects.equals(rewardType, RewardType.SEND_MONY_TYPE.getCode())) {
            return SCORE;
        } else if (Objects.equals(rewardType, RewardType.SEND_GIFT_TYPE.getCode())) {
            return TERMINAL_DIRECT_DELIVERY;
        }
    }
    
    throw new IllegalArgumentException("不支持的账户类型和奖励类型组合: accountType=" + accountType + ", rewardType=" + rewardType);
}
```

## 重构方案

### 设计思路
使用**策略模式 + 枚举映射**的方案：
- 创建内部枚举`AccountRewardCashWayMapping`定义映射关系
- 每个枚举项包含账户类型集合、奖励类型集合和对应的兑付方式
- 通过枚举遍历查找匹配的兑付方式

### 重构后的代码结构

#### 1. 内部映射枚举
```java
@Getter
@AllArgsConstructor
public enum AccountRewardCashWayMapping {
    
    /**
     * 经销商(1,2,6) - 积分奖励 -> 奖励入账
     */
    DEALER_SCORE_REWARD(
        new HashSet<>(Arrays.asList(1, 2, 6)), 
        new HashSet<>(Arrays.asList(RewardType.SEND_SCORE_TYPE.getCode())), 
        REWARD_ACCOUNT
    ),
    
    /**
     * 经销商(1,2,6) - 实物奖励 -> 进搭赠池
     */
    DEALER_GIFT_REWARD(
        new HashSet<>(Arrays.asList(1, 2, 6)), 
        new HashSet<>(Arrays.asList(RewardType.SEND_GIFT_TYPE.getCode())), 
        DEALER_GIFT_POOL
    ),
    
    /**
     * 分销商(3)、合伙人(4) - 积分奖励 -> 积分
     */
    DISTRIBUTOR_SCORE_REWARD(
        new HashSet<>(Arrays.asList(3, 4)), 
        new HashSet<>(Arrays.asList(RewardType.SEND_SCORE_TYPE.getCode())), 
        SCORE
    ),
    
    /**
     * 终端(5) - 积分奖励或红包奖励 -> 积分
     */
    TERMINAL_SCORE_MONEY_REWARD(
        new HashSet<>(Arrays.asList(5)), 
        new HashSet<>(Arrays.asList(RewardType.SEND_SCORE_TYPE.getCode(), RewardType.SEND_MONY_TYPE.getCode())), 
        SCORE
    ),
    
    /**
     * 终端(5) - 实物奖励 -> 直投酒
     */
    TERMINAL_GIFT_REWARD(
        new HashSet<>(Arrays.asList(5)), 
        new HashSet<>(Arrays.asList(RewardType.SEND_GIFT_TYPE.getCode())), 
        TERMINAL_DIRECT_DELIVERY
    );

    private final Set<Integer> accountTypes;
    private final Set<Integer> rewardTypes;
    private final AccountCashWayEnum cashWay;
}
```

#### 2. 简化的核心方法
```java
public static AccountCashWayEnum getAccountCashWay(Integer accountType, Integer rewardType) {
    // 参数校验
    if (accountType == null || rewardType == null) {
        throw new IllegalArgumentException("账户类型和奖励类型不能为空");
    }

    // 通过映射枚举查找兑付方式
    AccountCashWayEnum cashWay = AccountRewardCashWayMapping.findCashWay(accountType, rewardType);
    
    if (cashWay == null) {
        throw new IllegalArgumentException("不支持的账户类型和奖励类型组合: accountType=" + accountType + ", rewardType=" + rewardType);
    }
    
    return cashWay;
}
```

## 映射关系

| 账户类型 | 奖励类型 | 兑付方式 | 说明 |
|---------|---------|---------|------|
| 1,2,6 | 3(积分) | 奖励入账 | 经销商积分奖励 |
| 1,2,6 | 2(实物) | 进搭赠池 | 经销商实物奖励 |
| 3,4 | 3(积分) | 积分 | 分销商/合伙人积分奖励 |
| 5 | 3(积分)/1(红包) | 积分 | 终端积分/红包奖励 |
| 5 | 2(实物) | 直投酒 | 终端实物奖励 |

## 重构优势

### 1. 代码质量提升
- ✅ **消除了复杂的if-else嵌套**：提高代码可读性
- ✅ **配置化的映射关系**：易于维护和理解
- ✅ **更好的单元测试支持**：每个映射关系都可以独立测试

### 2. 性能优化
- ✅ **高效的查找机制**：时间复杂度O(n)，n为枚举项数量
- ✅ **减少重复计算**：避免多次调用`Objects.equals()`

### 3. 可维护性
- ✅ **良好的扩展性**：新增类型只需添加枚举项，无需修改核心逻辑
- ✅ **清晰的映射关系**：每个映射关系都有明确的注释说明
- ✅ **兼容Java 8**：使用`HashSet`和`Arrays.asList`替代`Set.of()`

### 4. 向后兼容性
- ✅ **API保持不变**：方法签名和使用方式完全不变
- ✅ **功能完全一致**：所有原有功能都得到保留

## 单元测试

创建了完整的单元测试类 `AccountCashWayEnumTest`，包含以下测试场景：

### 正常功能测试
- `testDealerScoreReward()` - 经销商积分奖励
- `testDealerGiftReward()` - 经销商实物奖励
- `testDistributorScoreReward()` - 分销商积分奖励
- `testTerminalScoreReward()` - 终端积分奖励
- `testTerminalGiftReward()` - 终端实物奖励

### 异常情况测试
- `testUnsupportedAccountType()` - 不支持的账户类型
- `testUnsupportedRewardType()` - 不支持的奖励类型
- `testUnsupportedCombination()` - 不支持的组合
- `testNullAccountType()` - null账户类型
- `testNullRewardType()` - null奖励类型

### 边界值和性能测试
- `testBoundaryValues()` - 边界值测试
- `testPerformance()` - 性能测试
- `testAllMappingCompleteness()` - 映射完整性测试

## 使用方式

重构后的使用方式完全不变：

```java
// 获取兑付方式
AccountCashWayEnum cashWay = AccountCashWayEnum.getAccountCashWay(accountType, rewardType);

// 示例
AccountCashWayEnum dealerScoreCashWay = AccountCashWayEnum.getAccountCashWay(1, RewardType.SEND_SCORE_TYPE.getCode());
// 返回: REWARD_ACCOUNT

AccountCashWayEnum terminalGiftCashWay = AccountCashWayEnum.getAccountCashWay(5, RewardType.SEND_GIFT_TYPE.getCode());
// 返回: TERMINAL_DIRECT_DELIVERY
```

## 扩展指南

如需新增账户类型或奖励类型的组合，只需在 `AccountRewardCashWayMapping` 枚举中添加新的映射项：

```java
/**
 * 新账户类型 - 新奖励类型 -> 新兑付方式
 */
NEW_ACCOUNT_REWARD_MAPPING(
    new HashSet<>(Arrays.asList(新账户类型)), 
    new HashSet<>(Arrays.asList(新奖励类型)), 
    新兑付方式
);
```

## 总结

本次重构通过引入策略模式和枚举映射，成功解决了原有代码的复杂性问题，提高了代码的可读性、可维护性和扩展性，同时保持了完全的向后兼容性。重构后的代码更加清晰、高效，为后续的功能扩展奠定了良好的基础。 