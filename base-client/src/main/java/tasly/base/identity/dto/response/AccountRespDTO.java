package tasly.base.identity.dto.response;

public class AccountRespDTO {

    /**
     * 主键id
     */
    private Integer id;
    /**
     * 名称
     */
    private String name;
    /**
     * 手机号
     */
    private String phone;
    /**
     * 账户类型(0:客户经理,1创建的下级经销商人员,2创建的下级终端人员,3创建的下级采集人员,4外部人员)
     */
    private Integer type;


    /**
     * 主键id
     */
    private Integer parentId;
    /**
     * 名称
     */
    private String parentName;
    /**
     * 手机号
     */
    private String parentPhone;
    /**
     * 账户类型(0:客户经理,1创建的下级经销商人员,2创建的下级终端人员,3创建的下级采集人员)
     */
    private Integer parentType;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public Integer getParentId() {
        return parentId;
    }

    public void setParentId(Integer parentId) {
        this.parentId = parentId;
    }

    public String getParentName() {
        return parentName;
    }

    public void setParentName(String parentName) {
        this.parentName = parentName;
    }

    public String getParentPhone() {
        return parentPhone;
    }

    public void setParentPhone(String parentPhone) {
        this.parentPhone = parentPhone;
    }

    public Integer getParentType() {
        return parentType;
    }

    public void setParentType(Integer parentType) {
        this.parentType = parentType;
    }
}
