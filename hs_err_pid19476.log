#
# There is insufficient memory for the Java Runtime Environment to continue.
# Native memory allocation (mmap) failed to map 264241152 bytes for G1 virtual space
# Possible reasons:
#   The system is out of physical RAM or swap space
#   The process is running with CompressedOops enabled, and the Java Heap may be blocking the growth of the native heap
# Possible solutions:
#   Reduce memory load on the system
#   Increase physical memory or swap space
#   Check if swap backing store is full
#   Decrease Java heap size (-Xmx/-Xms)
#   Decrease number of Java threads
#   Decrease Java thread stack sizes (-Xss)
#   Set larger code cache with -XX:ReservedCodeCacheSize=
#   JVM is running with Zero Based Compressed Oops mode in which the Java heap is
#     placed in the first 32GB address space. The Java Heap base address is the
#     maximum limit for the native heap growth. Please use -XX:HeapBaseMinAddress
#     to set the Java Heap base and to place the Java Heap above 32GB virtual address.
# This output file may be truncated or incomplete.
#
#  Out of Memory Error (os_windows.cpp:3771), pid=19476, tid=20780
#
# JRE version:  (17.0.7+10) (build )
# Java VM: OpenJDK 64-Bit Server VM (17.0.7+10-b829.16, mixed mode, sharing, tiered, compressed oops, compressed class ptrs, g1 gc, windows-amd64)
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#

---------------  S U M M A R Y ------------

Command Line: git4idea.http.GitAskPassApp Username for 'http://************': 

Host: 11th Gen Intel(R) Core(TM) i5-1135G7 @ 2.40GHz, 8 cores, 15G,  Windows 10 , 64 bit Build 19041 (10.0.19041.3636)
Time: Mon Dec 18 15:54:58 2023  Windows 10 , 64 bit Build 19041 (10.0.19041.3636) elapsed time: 0.006654 seconds (0d 0h 0m 0s)

---------------  T H R E A D  ---------------

Current thread (0x0000020ac697c0a0):  JavaThread "Unknown thread" [_thread_in_vm, id=20780, stack(0x000000a95cc00000,0x000000a95cd00000)]

Stack: [0x000000a95cc00000,0x000000a95cd00000]
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x683cca]
V  [jvm.dll+0x8424b4]
V  [jvm.dll+0x843cae]
V  [jvm.dll+0x844313]
V  [jvm.dll+0x24ad2f]
V  [jvm.dll+0x680b99]
V  [jvm.dll+0x67526a]
V  [jvm.dll+0x30b3cb]
V  [jvm.dll+0x312876]
V  [jvm.dll+0x36221e]
V  [jvm.dll+0x36244f]
V  [jvm.dll+0x2e14a8]
V  [jvm.dll+0x2e2414]
V  [jvm.dll+0x814441]
V  [jvm.dll+0x36ffe1]
V  [jvm.dll+0x7f3a1c]
V  [jvm.dll+0x3f305f]
V  [jvm.dll+0x3f4b91]
C  [jli.dll+0x528f]
C  [ucrtbase.dll+0x21bb2]
C  [KERNEL32.DLL+0x17344]
C  [ntdll.dll+0x526b1]


---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x00007ff9c0e2b098, length=0, elements={
}

Java Threads: ( => current thread )

Other Threads:
  0x0000020ac69e9230 GCTaskThread "GC Thread#0" [stack: 0x000000a95cd00000,0x000000a95ce00000] [id=22716]
  0x0000020ac69f9cc0 ConcurrentGCThread "G1 Main Marker" [stack: 0x000000a95ce00000,0x000000a95cf00000] [id=22692]
  0x0000020ac69fa6d0 ConcurrentGCThread "G1 Conc#0" [stack: 0x000000a95cf00000,0x000000a95d000000] [id=4060]

[error occurred during error reporting (printing all threads), id 0xc0000005, EXCEPTION_ACCESS_VIOLATION (0xc0000005) at pc=0x00007ff9c05e2087]

VM state: not at safepoint (not fully initialized)

VM Mutex/Monitor currently owned by a thread:  ([mutex/lock_event])
[0x0000020ac69776e0] Heap_lock - owner thread: 0x0000020ac697c0a0

Heap address: 0x0000000704a00000, size: 4022 MB, Compressed Oops mode: Zero based, Oop shift amount: 3

CDS archive(s) mapped at: [0x0000000000000000-0x0000000000000000-0x0000000000000000), size 0, SharedBaseAddress: 0x0000000800000000, ArchiveRelocationMode: 0.
Narrow klass base: 0x0000000000000000, Narrow klass shift: 0, Narrow klass range: 0x0

GC Precious Log:
<Empty>

Heap:
 garbage-first heap   total 0K, used 0K [0x0000000704a00000, 0x0000000800000000)
  region size 2048K, 0 young (0K), 0 survivors (0K)

[error occurred during error reporting (printing heap information), id 0xc0000005, EXCEPTION_ACCESS_VIOLATION (0xc0000005) at pc=0x00007ff9c09cdf59]

GC Heap History (0 events):
No events

Dll operation events (1 events):
Event: 0.004 Loaded shared library E:\idea\IntelliJ IDEA 2023.1.4\jbr\bin\java.dll

Deoptimization events (0 events):
No events

Classes unloaded (0 events):
No events

Classes redefined (0 events):
No events

Internal exceptions (0 events):
No events

VM Operations (0 events):
No events

Events (0 events):
No events


Dynamic libraries:
0x00007ff787840000 - 0x00007ff78784a000 	E:\idea\IntelliJ IDEA 2023.1.4\jbr\bin\java.exe
0x00007ffa603b0000 - 0x00007ffa605a8000 	C:\Windows\SYSTEM32\ntdll.dll
0x00007ffa5eec0000 - 0x00007ffa5ef7d000 	C:\Windows\System32\KERNEL32.DLL
0x00007ffa5dfc0000 - 0x00007ffa5e2b6000 	C:\Windows\System32\KERNELBASE.dll
0x00007ffa5dce0000 - 0x00007ffa5dde0000 	C:\Windows\System32\ucrtbase.dll
0x00007ff9da0a0000 - 0x00007ff9da0b7000 	E:\idea\IntelliJ IDEA 2023.1.4\jbr\bin\jli.dll
0x00007ffa58440000 - 0x00007ffa5845b000 	E:\idea\IntelliJ IDEA 2023.1.4\jbr\bin\VCRUNTIME140.dll
0x00007ffa5ea10000 - 0x00007ffa5ebae000 	C:\Windows\System32\USER32.dll
0x00007ffa5e2c0000 - 0x00007ffa5e2e2000 	C:\Windows\System32\win32u.dll
0x00007ffa4bd00000 - 0x00007ffa4bf9a000 	C:\Windows\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.19041.3636_none_60b6a03d71f818d5\COMCTL32.dll
0x00007ffa5ebb0000 - 0x00007ffa5ebdc000 	C:\Windows\System32\GDI32.dll
0x00007ffa5dbc0000 - 0x00007ffa5dcda000 	C:\Windows\System32\gdi32full.dll
0x00007ffa5e410000 - 0x00007ffa5e4ae000 	C:\Windows\System32\msvcrt.dll
0x00007ffa5de70000 - 0x00007ffa5df0d000 	C:\Windows\System32\msvcp_win.dll
0x00007ffa5e3e0000 - 0x00007ffa5e410000 	C:\Windows\System32\IMM32.DLL
0x00007ffa58430000 - 0x00007ffa5843c000 	E:\idea\IntelliJ IDEA 2023.1.4\jbr\bin\vcruntime140_1.dll
0x00007ffa39560000 - 0x00007ffa395ed000 	E:\idea\IntelliJ IDEA 2023.1.4\jbr\bin\msvcp140.dll
0x00007ff9c02f0000 - 0x00007ff9c0f5e000 	E:\idea\IntelliJ IDEA 2023.1.4\jbr\bin\server\jvm.dll
0x00007ffa5e5f0000 - 0x00007ffa5e69f000 	C:\Windows\System32\ADVAPI32.dll
0x00007ffa5e6a0000 - 0x00007ffa5e73c000 	C:\Windows\System32\sechost.dll
0x00007ffa5f0b0000 - 0x00007ffa5f1d6000 	C:\Windows\System32\RPCRT4.dll
0x00007ffa4e7c0000 - 0x00007ffa4e7e7000 	C:\Windows\SYSTEM32\WINMM.dll
0x00007ffa3fcf0000 - 0x00007ffa3fcf9000 	C:\Windows\SYSTEM32\WSOCK32.dll
0x00007ffa538b0000 - 0x00007ffa538ba000 	C:\Windows\SYSTEM32\VERSION.dll
0x00007ffa5f1f0000 - 0x00007ffa5f25b000 	C:\Windows\System32\WS2_32.dll
0x00007ffa5c480000 - 0x00007ffa5c492000 	C:\Windows\SYSTEM32\kernel.appcore.dll
0x00007ffa108b0000 - 0x00007ffa108ba000 	E:\idea\IntelliJ IDEA 2023.1.4\jbr\bin\jimage.dll
0x00007ffa5b4e0000 - 0x00007ffa5b6c4000 	C:\Windows\SYSTEM32\DBGHELP.DLL
0x00007ffa40cf0000 - 0x00007ffa40d24000 	C:\Windows\SYSTEM32\dbgcore.DLL
0x00007ffa5dde0000 - 0x00007ffa5de62000 	C:\Windows\System32\bcryptPrimitives.dll
0x00007ff9c9250000 - 0x00007ff9c9275000 	E:\idea\IntelliJ IDEA 2023.1.4\jbr\bin\java.dll

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;E:\idea\IntelliJ IDEA 2023.1.4\jbr\bin;C:\Windows\SYSTEM32;C:\Windows\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.19041.3636_none_60b6a03d71f818d5;E:\idea\IntelliJ IDEA 2023.1.4\jbr\bin\server

VM Arguments:
java_command: git4idea.http.GitAskPassApp Username for 'http://************': 
java_class_path (initial): E:/idea/IntelliJ IDEA 2023.1.4/plugins/vcs-git/lib/git4idea-rt.jar;E:/idea/IntelliJ IDEA 2023.1.4/lib/externalProcess-rt.jar
Launcher Type: SUN_STANDARD

[Global flags]
     intx CICompilerCount                          = 4                                         {product} {ergonomic}
     uint ConcGCThreads                            = 2                                         {product} {ergonomic}
     uint G1ConcRefinementThreads                  = 8                                         {product} {ergonomic}
   size_t G1HeapRegionSize                         = 2097152                                   {product} {ergonomic}
    uintx GCDrainStackTargetSize                   = 64                                        {product} {ergonomic}
   size_t InitialHeapSize                          = 264241152                                 {product} {ergonomic}
   size_t MarkStackSize                            = 4194304                                   {product} {ergonomic}
   size_t MaxHeapSize                              = 4217372672                                {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 2097152                                   {product} {ergonomic}
   size_t MinHeapSize                              = 8388608                                   {product} {ergonomic}
    uintx NonNMethodCodeHeapSize                   = 5839372                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 122909434                              {pd product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 122909434                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 251658240                              {pd product} {ergonomic}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
   size_t SoftMaxHeapSize                          = 4217372672                             {manageable} {ergonomic}
     bool UseCompressedClassPointers               = true                           {product lp64_product} {ergonomic}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseG1GC                                  = true                                      {product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}

Logging:
Log output configuration:
 #0: stdout all=warning uptime,level,tags
 #1: stderr all=off uptime,level,tags

Environment Variables:
JAVA_HOME=E:\idea\jdk\jdk1.8
PATH=E:/git/git/Git/mingw64/libexec/git-core;E:/git/git/Git/mingw64/libexec/git-core;E:\git\git\Git\mingw64\bin;E:\git\git\Git\usr\bin;C:\Users\<USER>\bin;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\Program Files\TortoiseGit\bin;C:\Users\<USER>\AppData\Roaming\nvm;C:\Users\<USER>\AppData\Roaming\nvm\nodejs;c:\Users\<USER>\AppData\Local\Programs\Cursor\resources\app\bin;E:\weChatTool\΢��web�����߹���\dll;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;E:\git\git\Git\cmd;E:\vscode\Microsoft VS Code\bin;C:\Users\<USER>\AppData\Roaming\nvm;C:\Users\<USER>\AppData\Roaming\nvm\nodejs
USERNAME=tsl-sunjianbu
DISPLAY=:0.0
LC_ALL=en_US.UTF-8
TERM=xterm-256color
TMPDIR=C:\Users\<USER>\AppData\Local\Temp
OS=Windows_NT
PROCESSOR_IDENTIFIER=Intel64 Family 6 Model 140 Stepping 1, GenuineIntel
TMP=C:\Users\<USER>\AppData\Local\Temp
TEMP=C:\Users\<USER>\AppData\Local\Temp


JNI global refs:

[error occurred during error reporting (JNI global references), id 0xc0000005, EXCEPTION_ACCESS_VIOLATION (0xc0000005) at pc=0x00007ff9c03a7fc0]


---------------  S Y S T E M  ---------------

OS:
 Windows 10 , 64 bit Build 19041 (10.0.19041.3636)
OS uptime: 0 days 7:04 hours

CPU: total 8 (initial active 8) (4 cores per cpu, 2 threads per core) family 6 model 140 stepping 1 microcode 0x86, cx8, cmov, fxsr, ht, mmx, 3dnowpref, sse, sse2, sse3, ssse3, sse4.1, sse4.2, popcnt, lzcnt, tsc, tscinvbit, avx, avx2, aes, erms, clmul, bmi1, bmi2, adx, avx512f, avx512dq, avx512cd, avx512bw, avx512vl, sha, fma, vzeroupper, avx512_vpopcntdq, avx512_vpclmulqdq, avx512_vaes, avx512_vnni, clflush, clflushopt, clwb, avx512_vbmi2, avx512_vbmi

Memory: 4k page, system-wide physical 16082M (1242M free)
TotalPageFile size 31442M (AvailPageFile size 182M)
current process WorkingSet (physical memory assigned to process): 10M, peak: 10M
current process commit charge ("private bytes"): 60M, peak: 312M

vm_info: OpenJDK 64-Bit Server VM (17.0.7+10-b829.16) for windows-amd64 JRE (17.0.7+10-b829.16), built on 2023-06-02 by "builduser" with MS VC++ 16.10 / 16.11 (VS2019)

END.
