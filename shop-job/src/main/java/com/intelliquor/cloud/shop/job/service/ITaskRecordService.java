package com.intelliquor.cloud.shop.job.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.intelliquor.cloud.shop.job.model.TaskRecordModel;
import com.intelliquor.cloud.shop.job.model.req.TaskRecordReq;
import com.intelliquor.cloud.shop.job.model.resp.TaskRecordResp;

import java.util.List;

/**
 * <p>
 * 任务记录表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-29
 */
public interface ITaskRecordService extends IService<TaskRecordModel> {

    /**
     * 分页查询任务记录
     *
     * @param req 查询条件
     * @return Response<PageInfo < TaskRecordResp>>
     */
    List<TaskRecordResp> getListByPage(TaskRecordReq req);

    /**
     * 任务记录超时定时
     */
    void taskRecordTimeout();

}
