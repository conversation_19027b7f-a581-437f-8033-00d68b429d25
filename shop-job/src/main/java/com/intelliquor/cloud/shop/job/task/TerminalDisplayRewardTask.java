package com.intelliquor.cloud.shop.job.task;

import com.intelliquor.cloud.shop.common.enums.ComputeTypeEnum;
import com.intelliquor.cloud.shop.common.enums.ProtocolTypeEnum;
import com.intelliquor.cloud.shop.common.enums.TerminalRewardOperationTypeEnum;
import com.intelliquor.cloud.shop.common.enums.TerminalRewardSendMQStatusEnum;
import com.intelliquor.cloud.shop.common.model.TerminalProductProtocolRelationModel;
import com.intelliquor.cloud.shop.common.model.business.terminalReward.RewardCalculateInfo;
import com.intelliquor.cloud.shop.common.service.TerminalProductProtocolV2Service;
import com.intelliquor.cloud.shop.common.service.TerminalRewardCalcBatchService;
import com.intelliquor.cloud.shop.common.service.TerminalRewardCalcDetailService;
import com.intelliquor.cloud.shop.common.service.TerminalRewardCalculatorService;
import com.intelliquor.cloud.shop.common.service.resp.ProtocoTimeParamResp;
import com.intelliquor.cloud.shop.job.rocketmq.MqSendService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.client.producer.SendStatus;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.Month;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 终端奖励定时任务
 *
 * @Author: mali
 * @CreateTime: 2024-03-20
 */
@Slf4j
@Component
public class TerminalDisplayRewardTask {

    @Value(value = "${rocketmq.producer.topic.display}")
    private String topic;

    @Value(value = "${rocketmq.producer.group}")
    private String group;

    @Autowired
    private TerminalProductProtocolV2Service terminalProductProtocolV2CommonService;
    @Autowired
    private TerminalRewardCalculatorService terminalRewardCalculatorService;

    @Autowired
    private TerminalRewardCalcDetailService terminalRewardCalcDetailService;

    @Autowired
    private TerminalRewardCalcBatchService terminalRewardCalcBatchService;


    @Autowired
    private MqSendService mqSendService;


    /**
     *  统计需要计算陈列奖励的终端
     *  每月1号0点0分0秒执行
     *
     * @throws Exception
     */
//    @Scheduled(cron = "0 0 0 1 * *")
    public void displayRewardCalculateInfo() throws Exception {
        log.info("统计陈列需要计算奖励的终端======开始执行");

        LocalDate currentDate = LocalDate.now();
        Month currentMonth = currentDate.getMonth();
        int currentDay = currentDate.getDayOfMonth();
        log.info("统计陈列需要计算奖励的终端======月份：", currentMonth);

        // 当前时间是每个季度的第一个月的1号
        if (currentDay == 1 &&
                (currentMonth == Month.JANUARY || currentMonth == Month.APRIL ||
                currentMonth == Month.JULY || currentMonth == Month.OCTOBER)) {
            log.info("统计陈列需要计算奖励的终端======进入执行逻辑");

            String year = String.valueOf(currentDate.getYear());
            //  Month 值从 1 开始，因此我们需要 +1 来获得当前月份
            int month = currentDate.getMonthValue();
            int quarter = ((month - 1) / 3) + 1;
            if (quarter == 1) {
                year = String.valueOf((Integer.valueOf(year) - 1));
                quarter =  4;
            } else {
                quarter = quarter - 1;
            }
            ProtocoTimeParamResp displayProtocoTimeParamResp = terminalProductProtocolV2CommonService.getDisplayTimeByProtocolTypeAndYearAndQuarter(year, quarter);
            List<TerminalProductProtocolRelationModel> displayRewardTerminals = terminalProductProtocolV2CommonService.getProtocolTerminalIdsByProtocolType(ProtocolTypeEnum.DISPLAY.getKey(),  displayProtocoTimeParamResp.getStartDate(),  displayProtocoTimeParamResp.getEndDate());
            if(CollectionUtils.isNotEmpty(displayRewardTerminals)){
                log.info("统计陈列需要计算奖励的终端======开始，终端奖励结果{}个", displayRewardTerminals.size());

                String batchNumber = terminalRewardCalculatorService.generateBatchNumber();

                // 拼装发送MQ结构RewardCalculateInfo、记录核算批次日志、核算详情日志
                List<RewardCalculateInfo> displayRewardCalculateInfoList= new ArrayList<RewardCalculateInfo>();
                for (TerminalProductProtocolRelationModel terminalProductProtocolRelationModel : displayRewardTerminals) {
                    RewardCalculateInfo rewardCalculateInfo = terminalRewardCalculatorService.getRewardCalculateInfo(year, quarter,
                            terminalProductProtocolRelationModel, batchNumber, null,
                            terminalRewardCalculatorService.generateSerialNumber(), ComputeTypeEnum.DISPLAY_QUARTERLY_BONUS.getKey());
                    displayRewardCalculateInfoList.add(rewardCalculateInfo);
                    terminalRewardCalcDetailService.saveTerminalRewardCalcDetail(rewardCalculateInfo.getBatchId(),rewardCalculateInfo.getSerialNum(), TerminalRewardOperationTypeEnum.INCENTIVE_ACCOUNTING.getKey(),TerminalRewardSendMQStatusEnum.NOT_SEND_MQ.getKey(),null,null,null,null);
                }
                terminalRewardCalcBatchService.saveTerminalRewardCalc(displayRewardCalculateInfoList.get(0), displayRewardTerminals.size(),TerminalRewardOperationTypeEnum.INCENTIVE_ACCOUNTING.getKey());

                // 发送MQ、更新核算详情日志、更新核算批次日志
                Integer mqSendOkNum = 0;
                for (RewardCalculateInfo rewardCalculateInfo : displayRewardCalculateInfoList) {
                    SendResult sendResult = null;
                    String msg = "";
                    try {
                        sendResult = mqSendService.send(rewardCalculateInfo, topic, group);
                    } catch (Exception e) {
                        msg = "发送mq失败，异常信息：{}" + e.getMessage();
                        log.error("发送MQ异常，终端陈列奖励信息{}", rewardCalculateInfo, e);
                    }

                    Integer status = null;
                    if(Objects.nonNull(sendResult) && sendResult.getSendStatus() == SendStatus.SEND_OK){
                        msg = "陈列奖励核算生产者处理成功";
                        status = TerminalRewardSendMQStatusEnum.SEND_MQ_SUCCESS.getKey();
                        ++mqSendOkNum;
                    }else{
                        status = TerminalRewardSendMQStatusEnum.SEND_MQ_FAIL.getKey();
                        msg = "陈列奖励核算生产者创建失败，原因:"+sendResult.getSendStatus().toString();
                    }
                    terminalRewardCalcDetailService.saveTerminalRewardCalcDetail(rewardCalculateInfo.getBatchId(),rewardCalculateInfo.getSerialNum(), TerminalRewardOperationTypeEnum.INCENTIVE_ACCOUNTING.getKey(),status, msg, sendResult.getMsgId(), sendResult.getTransactionId(),null);
                }
                terminalRewardCalcBatchService.updateTerminalRewardCalc(batchNumber, mqSendOkNum);
                log.info("统计陈列需要计算奖励的终端======结束，批次号{}，终端奖励结果{}个，发送MQ成功{}个", batchNumber, displayRewardTerminals.size(), mqSendOkNum);
            }else{
                log.info("统计陈列需要计算奖励的终端======结束，无终端需要计算奖励");
            }
        }
    }
}
