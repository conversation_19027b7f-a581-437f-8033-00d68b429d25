package com.intelliquor.cloud.shop.job.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 经销商合同关系附表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-01
 */
@Getter
@Setter
@TableName("t_dealer_contract_rel_info")
public class DealerContractRelInfoModel implements Serializable {



        /**
         * 主键id
         */
        @TableId(value = "id", type = IdType.AUTO)
        private Long id;

        /**
         * 经销商/分销商编码
         */
        private String dealerCode;

        /**
         * 经销商名称
         */
        private String dealerName;

        /**
         * 经销商状态 0:未合作 1:已合作 2:停止合作
         */
        private Integer dealerStatus;

        /**
         * 合同编码
         */
        private String contractCode;

        /**
         * 合同类型 0：国台主品合同1：国台酱酒合同2：常规渠道经销合同3：国台酱酒经销合同4：专卖店经销合同5：数智体验中心经销合同6：团购特约经销合同7：电商平台经销合同8：商超连锁经销合同9：国台酱酒经销合同(电商)
         */
        private Integer contractType;

        /**
         * 合同状态 0停用 1启用
         */
        private Integer contractStatus;

        /**
         * 创建时间
         */
        @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
        @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private Date createTime;

        /**
         * 业务区域
         */
        private String areaList;

        /**
         * 分公司ID
         */
        private String affiliateId;

        /**
         * 分公司名称
         */
        private String affiliateName;

        /**
         * 大区ID
         */
        private String regionId;

        /**
         * 大区名称
         */
        private String regionName;



        /**
         * 省
         */
        private String province;

        /**
         * 市
         */
        private String city;

        /**
         * 区
         */
        private String district;


    }


