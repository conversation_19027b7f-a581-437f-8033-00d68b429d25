package com.intelliquor.cloud.shop.job.model;

import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 拜访审核每日统计表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-27
 */
@Getter
@Setter
@TableName("visit_check_daily_log")
public class VisitCheckDailyLogModel implements Serializable {

    private static final long serialVersionUID = 3255126172070624758L;

    /**
     * 主键id
     */
    private Long id;

    /**
     * 人员id
     */
    private Long accountManagerId;

    /**
     * 年
     */
    private Integer recordYear;

    /**
     * 月
     */
    private Integer recordMonth;

    /**
     * 日
     */
    private Integer recordDay;

    /**
     * 周
     */
    private Integer recordWeek;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 已审核数量
     */
    private Integer checkNum;

    /**
     * 未审核数量
     */
    private Integer noCheckNum;

    /**
     * 拜访数量
     */
    private Integer submitCheck;


}
