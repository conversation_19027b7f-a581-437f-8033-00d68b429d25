package com.intelliquor.cloud.shop.job.task;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import com.intelliquor.cloud.shop.common.constant.RedisConstant;
import com.intelliquor.cloud.shop.common.dao.*;
import com.intelliquor.cloud.shop.common.exception.BusinessException;
import com.intelliquor.cloud.shop.common.model.*;
import com.intelliquor.cloud.shop.common.model.resp.CodeInfoResp;
import com.intelliquor.cloud.shop.common.service.IRequestLogService;
import com.intelliquor.cloud.shop.common.service.SendAwardDealerService;
import com.intelliquor.cloud.shop.common.service.WebCodeCommonComponent;
import com.intelliquor.cloud.shop.common.utils.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.StopWatch;

import java.time.LocalDate;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * 一键代会员收货 处理奖励
 */
@Component
@Slf4j
public class ShopScanManagerByCodeTask {

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    @Autowired
    private SendAwardDealerService sendAwardDealerService;

    @Autowired
    private ShopDao shopDao;

    @Autowired
    private DealerInfoCommonDao dealerInfoCommonDao;

    @Autowired
    private TerminalScanDetailCommonDao terminalScanDetailCommonDao;

    @Autowired
    private TerminalScanDetailPlusDao terminalScanDetailPlusDao;

    @Autowired
    private WebCodeCommonComponent webCodeCommonComponent;

    @Autowired
    private TerminalScanRecordDao terminalScanRecordDao;

    @Autowired
    private RewardReasonDao rewardReasonDao;

    @Autowired
    private CloudDealerRewardRecordDao cloudDealerRewardRecordDao;

    @Autowired
    private IRequestLogService requestLogService;

    public void dealReward() {

        // 1.首先查询未处理的经销商奖励 2023年1月的数据才给发奖励
        // 1.1 查询未处理经销商奖励的数据
        Calendar calendar = Calendar.getInstance();
        calendar.set(2023, 0, 1, 0, 0, 0);
        Date limitDate = calendar.getTime();
        List<CodeInfoResp> newCodeInfoList = new ArrayList<>();
        LambdaQueryWrapper<TerminalScanDetailModel> detailLqw = Wrappers.lambdaQuery();
        detailLqw.eq(TerminalScanDetailModel::getIsShopReward, 0);
        detailLqw.eq(TerminalScanDetailModel::getIsDelete, 0);
        detailLqw.gt(TerminalScanDetailModel::getOutTime, LocalDate.now());
        List<TerminalScanDetailModel> terminalScanDetailModels = terminalScanDetailPlusDao.selectList(detailLqw);
        //
        for (TerminalScanDetailModel terminalScanDetailModel : terminalScanDetailModels) {
            // balanceId 放入到redis里面  根据redis 判断这条记录是否在处理 如果正在处理 不在进行处理
            // 1.2 拼接发放经销商奖励需要的参数
            List<CodeInfoResp> infoList = new ArrayList<>();
            CloudDealerInfoModel dealerInfo = dealerInfoCommonDao.selectDealerIdByDealerCode("11111");
            //可能是老的经销商编码收的货，查询是对应的新编码，走计算奖励逻辑(20230313)
            String newDealerCodeBydealerCode = dealerInfoCommonDao.getNewDealerCodeBydealerCode(dealerInfo.getDealerCode());
            if (dealerInfo == null) {
                dealerInfo = dealerInfoCommonDao.selectDealerIdByDealerCode(newDealerCodeBydealerCode);
            }

            int originType = 1; //1:按单收货 2:智能布奖
            ShopModel shopInfo = shopDao.getById(143);
            TerminalScanDetailModel detailModel = new TerminalScanDetailModel();
            int receiptType = 0;//(0:扫码收货;1:出库收货;2:一键收货;3:客户经理代收)
            int handleDataType = 0; //是否处理数据请求:(0:收货;1:处理数据)
            RewardReason rewardReason = initRewardReason(detailModel, receiptType);

            // 1.3 发放经销商奖励
            sendAwardDealerService.sendAwardDealerByOrderNew(infoList, dealerInfo, 1, shopInfo, detailModel, receiptType, 0, rewardReason);

        }
    }


    public void dealDealerReward(Integer balanceId) {

        // 判断列表是否为空
        if (balanceId == null || balanceId == 0) {
            log.error("无效的balanceId:{}", balanceId);
            throw new BusinessException("无效的balanceId");
        }

        LambdaQueryWrapper<TerminalScanDetailModel> detailLqw = Wrappers.lambdaQuery();
        detailLqw.eq(TerminalScanDetailModel::getBalanceId, balanceId);
        List<TerminalScanDetailModel> terminalScanDetailModels = terminalScanDetailPlusDao.selectList(detailLqw);
        // 判断列表是否为空
        if (terminalScanDetailModels == null || terminalScanDetailModels.size() == 0) {
            log.error("根据balanceId:{}查询不到详情列表", balanceId);
            throw new BusinessException("已经执行完成经销商奖励");
        }
        for (TerminalScanDetailModel terminalScanDetailModel : terminalScanDetailModels) {
            int id = terminalScanDetailModel.getId();
            String qrcode = terminalScanDetailModel.getQrcode();
            String redisAwardDealerTerminalScanId = String.format(RedisConstant.REDIS_AWARD_DEALER_TERMINAL_SCAN, id);
            Boolean rewardBoolean = redisTemplate.opsForValue().setIfAbsent(redisAwardDealerTerminalScanId, id + qrcode, RedisConstant.REDIS_TIME_OUT_TEN, TimeUnit.MINUTES);

            RequestLog requestLog = new RequestLog();
            requestLog.setReqType(23);
            requestLog.setReqName("发放经销商奖励:" + id + "->" + qrcode);
            requestLog.setReqUrlPath("/dealDealerReward");
            requestLog.setCreateDate(new Date());
            requestLog.setReqJson(JSONObject.toJSONString(terminalScanDetailModel));
            requestLog.setReqKey(id+">"+qrcode);
            requestLog.setResRtnJson(redisAwardDealerTerminalScanId + "-->" + rewardBoolean);
            StopWatch watch = new StopWatch();
            StringBuilder logStr = new StringBuilder();


            // 先判断是不是已经处理完经销商奖励
            if (terminalScanDetailModel.getIsDealerReward() == 1) {
                log.error("终端扫码收货详情表id{} 已经执行完成经销商奖励", id);
                requestLog.setResMsg("已经执行完成经销商奖励");
                throw new BusinessException("已经执行完成经销商奖励");
            }
            if (terminalScanDetailModel.getIsDelete() == 1) {
                log.error("终端扫码收货详情表id{} 已经删除不再执行奖励", id);
                requestLog.setResMsg("终端扫码收货详情表id:" + id + ",已经删除不再执行奖励");
                throw new BusinessException("终端扫码收货详情表id:" + id + ",已经删除不再执行奖励");
            }

            if (StringUtils.isBlank(terminalScanDetailModel.getQrcode())) {
                log.error("终端扫码收货详情表id{} qrcode为空不能执行奖励", id);
                requestLog.setResMsg("终端扫码收货详情表id:" + id + ",qrcode为空不能执行奖励");
                throw new BusinessException("终端扫码收货详情表id:" + id + ",qrcode为空不能执行奖励");
            }
            // 根据id 去经销商奖励表去查询是否已经上账
            LambdaQueryWrapper<CloudDealerRewardRecordModel> clqw = Wrappers.lambdaQuery();
            clqw.eq(CloudDealerRewardRecordModel::getDetailId, id);
            clqw.eq(CloudDealerRewardRecordModel::getIsDelete, 0);
            clqw.last(" LIMIT 1");
            CloudDealerRewardRecordModel cloudDealerRewardRecordModel = cloudDealerRewardRecordDao.selectOne(clqw);
            if (Objects.nonNull(cloudDealerRewardRecordModel)) {
                log.error("终端扫码收货详情表id{}【已】发放经销商奖励", id);
                requestLog.setResMsg("终端扫码收货详情表id:" + id + "【已】发放经销商奖励");
                throw new BusinessException("终端扫码收货详情表id:" + id + "【已】发放经销商奖励");
            }

            if (!rewardBoolean) {//存在说明重复激活
                log.info("{}的奖励{}重复计算,{}-->{}", id, qrcode, redisAwardDealerTerminalScanId, rewardBoolean);
                requestLog.setResMsg("此码正在奖励中不能重复奖励...");
                throw new BusinessException("400", "此码正在奖励中不能重复奖励或1分钟后再试...");
            }

            try {


                //调用溯源获取码信息 重新获取码信息 获取出库信息
                CodeInfoResp codeInfo = null;
                try {
                    watch.start();
                    codeInfo = webCodeCommonComponent.dealCodeInfo(qrcode);
                    watch.stop();
                    logStr.append("调用溯源获取码信息webCodeCommonComponent.simplifyDealCodeInfo耗时").append(watch.getLastTaskTimeMillis()).append("毫秒,");
                } catch (BusinessException bus) {
                    log.error("BusinessException获取码信息失败，码{}，原因：{}", qrcode, bus);
                    TerminalScanRecordModel recordModel = generateRecord(terminalScanDetailModel, 1, bus.getMessage());
                    terminalScanRecordDao.insert(recordModel);
                    terminalScanDetailModel.setStatus(1);
                    terminalScanDetailModel.setScanMsg(bus.getMessage());
                    return;
                } catch (Exception e) {
                    log.error("Exception获取码信息失败，码{}，原因：{}", terminalScanDetailModel.getQrcode(), e);
                    String msg = e.getMessage().length() > 100 ? e.getMessage().substring(0, 100) : e.getMessage();
                    TerminalScanRecordModel recordModel = generateRecord(terminalScanDetailModel, 1, msg);
                    terminalScanRecordDao.insert(recordModel);
                    terminalScanDetailModel.setStatus(1);
                    terminalScanDetailModel.setScanMsg("获取码信息失败");
                    return;
                }

                //
                terminalScanDetailModel.setCodeXiang(codeInfo.getCodeXiang());
                terminalScanDetailModel.setCodeIn(codeInfo.getCodeIn());
                terminalScanDetailModel.setQuantity(codeInfo.getNumber());
                terminalScanDetailModel.setGoodsCode(codeInfo.getGoodsCode());
                terminalScanDetailModel.setGoodsName(codeInfo.getGoodsName());
                terminalScanDetailModel.setDealerCode(codeInfo.getDealerCode());
                terminalScanDetailModel.setDealerName(codeInfo.getDealerName());
                terminalScanDetailModel.setOutNo(codeInfo.getOrderNo());
                terminalScanDetailModel.setOutTime(codeInfo.getOutTime());
                terminalScanDetailModel.setBottleBoxRatio(codeInfo.getBottleBoxRatio());
                terminalScanDetailModel.setHasBox(codeInfo.getHasBox());
                terminalScanDetailModel.setContractNo(codeInfo.getContractNo());
                terminalScanDetailModel.setDealerOrderNo(codeInfo.getDealerOrderNo());
                terminalScanDetailModel.setIsDealerReward(1);
                terminalScanDetailPlusDao.updateById(terminalScanDetailModel);

                CloudDealerInfoModel dealerInfo = dealerInfoCommonDao.selectDealerIdByDealerCode(terminalScanDetailModel.getDealerCode());
                //可能是老的经销商编码收的货，查询是对应的新编码，走计算奖励逻辑(20230313)
                String newDealerCodeBydealerCode = dealerInfoCommonDao.getNewDealerCodeBydealerCode(dealerInfo.getDealerCode());
                if (dealerInfo == null) {
                    dealerInfo = dealerInfoCommonDao.selectDealerIdByDealerCode(newDealerCodeBydealerCode);
                }

                List<CodeInfoResp> codeInfoList = Lists.newArrayList(codeInfo);
                int receiptType = 0;//(0:扫码收货;1:出库收货;2:一键收货;3:客户经理代收)
                RewardReason rewardReason = initRewardReason(terminalScanDetailModel, receiptType);
                ShopModel shopInfo = shopDao.getById(terminalScanDetailModel.getShopId());
                if (shopInfo == null) {
                    log.error("终端扫码收货详情表id{} 进货终端为空不能执行奖励", id);
                    throw new BusinessException("终端扫码收货详情表id:" + id + ",进货终端为空不能执行奖励");
                }

                Calendar calendar = Calendar.getInstance();
                calendar.set(2023, 0, 1, 0, 0, 0);
                Date limitDate = calendar.getTime();

                Integer isMember = shopInfo.getIsMember();
                if (codeInfo.getDealerOutTime().after(limitDate)) {
                    if (isMember == 0 || isMember == 1 || isMember == 2) { //终端和会员  高端会员
                        watch.start();
                        sendAwardDealerService.sendAwardDealerByOrderNew(codeInfoList, dealerInfo, 1, shopInfo, terminalScanDetailModel, receiptType, 0, rewardReason);
                        terminalScanDetailModel.setIsDealerReward(1);
                        watch.stop();
                        logStr.append("sendAwardDealerService.sendAwardDealerByOrderNew耗时").append(watch.getLastTaskTimeMillis()).append("毫秒,");
                    } else {
                        terminalScanDetailModel.setIsDealerReward(1);
                        rewardReason.setHasDealer(0);
                        rewardReason.setIsSave(true);
                        rewardReason.setDealerDesc("收货端类型为:is_member=" + isMember + ",不在[0,1,2]中");
                    }
                } else {
                    terminalScanDetailModel.setIsDealerReward(1);
                    rewardReason.setHasDealer(0);
                    rewardReason.setIsSave(true);
                    rewardReason.setDealerDesc("第一次出货时间在限制时间在2023年前:" + DateUtils.convert2StringYYYYMMddHHmmss(codeInfo.getDealerOutTime()));
                }

                if (rewardReason.getIsSave()) {
                    rewardReasonDao.insert(rewardReason);
                }
                requestLog.setResCode("0");
                log.info("结束处理经销商奖励");
                // 删除redis
                redisTemplate.delete(redisAwardDealerTerminalScanId);
            } catch (Exception e) {
                redisTemplate.delete(redisAwardDealerTerminalScanId);
                requestLog.setResCode("-1");
                requestLog.setResMsg(e.getMessage());
            } finally {
                redisTemplate.delete(redisAwardDealerTerminalScanId);
                requestLog.setResJson(JSONObject.toJSONString(terminalScanDetailModel));
                String diffTime = DateUtils.longToHMS(watch.getTotalTimeMillis());
                requestLog.setReqTime(diffTime);
                requestLogService.insertLog(requestLog);
            }
            logStr.append("发放经销商奖励总耗时").append(watch.getTotalTimeSeconds()).append("秒");
            log.info(logStr.toString());
        }
    }


    /**
     * 生成扫码记录对象
     *
     * @param detail
     * @param status 状态0-正常 1-异常
     * @param errMsg 扫码异常原因
     * @return
     */
    private TerminalScanRecordModel generateRecord(TerminalScanDetailModel detail, Integer status, String errMsg) {
        TerminalScanRecordModel recordModel = new TerminalScanRecordModel();
        recordModel.setQrcode(detail.getQrcode());
        recordModel.setScanType(detail.getScanType());
        recordModel.setCreateUserId(detail.getCreateUserId());
        recordModel.setStatus(status);
        recordModel.setErrMsg(errMsg);
        return recordModel;
    }


    private RewardReason initRewardReason(TerminalScanDetailModel detail, Integer receiptType) {
        RewardReason rewardReason = new RewardReason();
        rewardReason.setReceiptType(receiptType);
        rewardReason.setDetailId(detail.getId());
        rewardReason.setShopId(detail.getShopId());
        rewardReason.setAccountType(detail.getAccountType());
        rewardReason.setRewardType(0);
        rewardReason.setOrderCode(detail.getReceivedOrderCode());
        rewardReason.setQrCode(detail.getQrcode());
        rewardReason.setIsSave(false);
        rewardReason.setCreateTime(new Date());
        rewardReason.setOrderTime(detail.getCreateTime());
        return rewardReason;
    }
}
