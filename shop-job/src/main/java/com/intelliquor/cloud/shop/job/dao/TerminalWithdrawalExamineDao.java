package com.intelliquor.cloud.shop.job.dao;

import com.intelliquor.cloud.shop.job.model.req.TerminalShopPaymentReq;
import com.intelliquor.cloud.shop.job.model.req.TerminalWithdrawalConfigReq;
import com.intelliquor.cloud.shop.job.model.resp.TerminalShopInfoResp;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;

public interface TerminalWithdrawalExamineDao {

    /**
     * 根据 终端名称 或 经销商名称 模糊查询终端信息
     * @param req 查询条件
     * @return 返回TerminalShopInfoResp集合
     */
    List<TerminalShopInfoResp> getTerminalInfoByNameOrDealerName(TerminalWithdrawalConfigReq req);

    /**
     * 根据 终端名称 或 经销商名称 模糊查询能够提现的终端信息
     * @param req 查询条件
     * @return 返回TerminalWithdrawalModel分页集合
     */
    List<TerminalShopInfoResp> getTerminalWithdrawalExamine(TerminalWithdrawalConfigReq req);

    /**
     * 更新 t_member_shop 表的 is_withdrawal 字段
     * @param isWithdrawal 积分是否可提现0否 1是
     * @return 更新结果
     */
    Integer updateMemberShopByIsWithdrawal(@Param("shopId") Integer shopId, @Param("isWithdrawal") Integer isWithdrawal);

    /**
     * 根据shop_id查询收款信息、虚拟贷款信息
     * @param shopId shop_id
     * @return TerminalShopInfoResp
     */
    TerminalShopInfoResp getTerminalPaymentInfoByShopId(@Param("shopId") Integer shopId);

    /**
     * 更新终端信息表（t_terminal_shop）的收款信息
     * @param req 更新条件
     * @return 更新结果
     */
    Integer updateTerminalShopPayment(TerminalShopPaymentReq req);

    /**
     * 根据shop_id查询t_member_shop的积分（虚拟贷款virtual_amount）
     * @param shopId shop_id
     * @return TerminalShopInfoResp
     */
    TerminalShopInfoResp getVirtualAmountByShopId(@Param("shopId") Integer shopId);

    /**
     * 根据 shopId 和 virtualAmount 更新 virtualAmount
     * @param shopId t_member_shop 的主键id
     * @param virtualAmount 需要扣减的积分（虚拟贷款）
     * @return 结果
     */
    Integer updateVirtualAmountByShopIdAndVirtualAmount(@Param("shopId") Integer shopId,
                                                        @Param("virtualAmount") BigDecimal virtualAmount);

    /**
     * 校验验证码（根据电话号码和验证码查询是否有数据）
     * @param phone 电话号码
     * @param code 验证码
     * @return 结果
     */
    Integer checkMessageByPhoneAndCode(@Param("phone")String phone, @Param("code")String code);


    /**
     * 根据shop_id查询终端是否可以提现
     * @param shopId 终端店铺id（t_member_shop的主键id）
     * @return 结果
     */
    Integer getTerminalWithdrawalStatusByShopId(@Param("shopId")Integer shopId);

    Integer getWithdrawalDealerStatus(@Param("contractCode") String contractCode,
                                      @Param("dealerCode") String dealerCode);
}
