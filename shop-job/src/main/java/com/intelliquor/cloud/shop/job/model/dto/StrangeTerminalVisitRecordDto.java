package com.intelliquor.cloud.shop.job.model.dto;

import lombok.Data;

import java.math.BigDecimal;

/**
 * @Auther: sunjianbu
 * @Date: 2023/7/11
 */
@Data
public class StrangeTerminalVisitRecordDto {
    /**
     * 拜访人id
     */
    private Integer createUserId;
    /**
     * 拜访次数
     */
    private Integer visits;
    /**
     * 陌生拜访任务点数
     */
    private BigDecimal points;
    /**
     * t_terminal_task_receive_month表id
     */
    private Integer tttrmId;
    /**
     * 年
     */
    private String year;
    /**
     * 月
     */
    private String month;
    /**
     * 陌生拜访合计任务点数
     */
    private BigDecimal sumPoints;
}
