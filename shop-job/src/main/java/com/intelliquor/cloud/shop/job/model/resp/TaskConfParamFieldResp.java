package com.intelliquor.cloud.shop.job.model.resp;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * 参数值配置
 * @TableName t_task_conf_param_field
 */
@Data
public class TaskConfParamFieldResp implements Serializable {
    /**
     * 主键
     */
    private Long id;

    /**
     * t_task_conf_param的id
     */
    private Long paramId;

    /**
     * 参数值名称
     */
    private String fieldName;

    /**
     * 参数值
     */
    private String fieldVal;

    /**
     * 批次
     */
    private String batchNo;

    /**
     * 是否最新
     */
    private Integer isNew;

    /**
     * 是否删除
     */
    private Integer isDelete;

    /**
     * 创建时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 创建人
     */
    private String createUserName;

    /**
     * 备注
     */
    private String remark;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
