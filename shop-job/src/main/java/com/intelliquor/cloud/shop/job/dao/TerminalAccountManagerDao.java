package com.intelliquor.cloud.shop.job.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.intelliquor.cloud.shop.common.model.TerminalAccountManagerModel;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <p>
 * 客户经理和人员账号表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-04
 */
@Repository
public interface TerminalAccountManagerDao extends BaseMapper<TerminalAccountManagerModel> {



    /**
     * 查询所有客户经理
     *
     * @return
     */
    List<TerminalAccountManagerModel> selectAllAccountManager();

    /**
     * 查询客户经理下级的拜访人员
     *
     * @param id
     * @return
     */
    List<TerminalAccountManagerModel> selectSubVisitAccountManager(@Param("id") Integer id,
                                                                   @Param("phone") String phone,
                                                                   @Param("searchText") String searchText);
}
