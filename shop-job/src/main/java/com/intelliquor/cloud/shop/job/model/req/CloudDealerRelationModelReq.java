package com.intelliquor.cloud.shop.job.model.req;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

@Data
public class CloudDealerRelationModelReq {

    @ApiModelProperty(value = "主键")
    private Long id;

    @ApiModelProperty(value = "关联类型，1：供货关联")
    private Integer type;

    @ApiModelProperty(value = "经销商/分销商id")
    private Integer dealerId;

    @ApiModelProperty(value = "父id")
    private Integer parentId;

    @ApiModelProperty(value = "邀请码")
    private String inviteCode;

    @ApiModelProperty(value = "公司id")
    private Integer companyId;

    @ApiModelProperty(value = "关联类型，1：直接关联(以邀请码注册),2：间接关联(以邀请码建立关联)")
    private Integer relationType;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @ApiModelProperty(value = "建立关联的员工号id")
    private Integer createRelationStaffId;

    /**
     * 排序字段默认为id
     */
    private String sortCode = "id";

    /**
     * 排序规则默认为降序排列(DESC/ASC)
     */
    private String sortRole = "DESC";

}
