package com.intelliquor.cloud.shop.job.model.resp;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * Description:
 *
 * <AUTHOR>
 * @since 2023/4/23 16:33
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class TerminalTaskScanCompleteDayResp {
    private Integer id;

    @ApiModelProperty("日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate day;

    @ApiModelProperty("t_terminal_task_package主键")
    private Integer tpId;

    @ApiModelProperty("业代Id")
    private Integer brokerId;

    @ApiModelProperty("终端id")
    private Integer shopId;

    @ApiModelProperty("终端name")
    private String shopName;

    @ApiModelProperty("商品编码")
    private String goodsCode;

    @ApiModelProperty("商品名称")
    private String goodsName;

    @ApiModelProperty("实际扫码箱数")
    private BigDecimal boxNum;

    @ApiModelProperty("系数")
    private BigDecimal ratio;

    @ApiModelProperty("计算后的任务点数")
    private BigDecimal calculateNum;

    @ApiModelProperty("删除标志(0:未删除;1:已删除)")
    private Integer deleteFlag;

    @ApiModelProperty("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    @ApiModelProperty("创建人")
    private Integer createBy;

    @ApiModelProperty("更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    @ApiModelProperty("更新人")
    private Integer updateBy;

    private Integer trId;

    private Integer tmId;

    private Integer tsId;

    private Integer ttsdId;
}
