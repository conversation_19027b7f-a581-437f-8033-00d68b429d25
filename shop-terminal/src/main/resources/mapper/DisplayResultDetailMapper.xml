<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.intelliquor.cloud.shop.terminal.dao.DisplayResultDetailDao">

    <resultMap id="BaseResultMap" type="com.intelliquor.cloud.shop.terminal.model.DisplayResultDetail">
            <id property="id" column="id" jdbcType="INTEGER"/>
            <result property="scanDetailId" column="scan_detail_id" jdbcType="INTEGER"/>
            <result property="displayResultId" column="display_result_id" jdbcType="INTEGER"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,scan_detail_id,display_result_id
    </sql>
</mapper>
