package com.intelliquor.cloud.shop.terminal.dao;

import com.intelliquor.cloud.shop.common.model.TerminalScanDetailModel;
import com.intelliquor.cloud.shop.terminal.model.DisplayResult;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.intelliquor.cloud.shop.terminal.model.req.DisplayResultReq;
import com.intelliquor.cloud.shop.terminal.model.req.ProtocolRewardAccountReq;
import com.intelliquor.cloud.shop.terminal.model.resp.DisplayResultInfoResp;
import com.intelliquor.cloud.shop.terminal.model.resp.DisplayResultResp;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【t_display_result(陈列统计)】的数据库操作Mapper
* @createDate 2023-03-16 14:49:50
* @Entity com.intelliquor.cloud.shop.terminal.model.DisplayResult
*/
@Repository
public interface DisplayResultDao extends BaseMapper<DisplayResult> {
    List<TerminalScanDetailModel> selectScanDetailList(@Param("terminalShopId") Integer terminalShopId, @Param("beginDate") String beginDate, @Param("endDate") String endDate);

    List<DisplayResultResp> selectResultList(DisplayResultReq req);

    List<DisplayResultResp> selectResultListNew(DisplayResultReq req);

    DisplayResultInfoResp selectResultInfoById(Integer id);

    List<Integer> getResultInfoByIds();

    List<DisplayResultResp> selectResultListByDeputyCode(ProtocolRewardAccountReq req);

}




