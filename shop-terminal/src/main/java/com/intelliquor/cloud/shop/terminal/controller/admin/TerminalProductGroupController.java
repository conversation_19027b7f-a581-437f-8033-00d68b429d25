package com.intelliquor.cloud.shop.terminal.controller.admin;


import com.intelliquor.cloud.shop.common.exception.BusinessException;
import com.intelliquor.cloud.shop.common.exception.RestResponse;
import com.intelliquor.cloud.shop.terminal.model.TerminalProductGroupModel;
import com.intelliquor.cloud.shop.terminal.service.TerminalProductGroupService;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <p>
 * 产品分组表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-07
 */

@Api(tags = {"产品分组表"}, description = "产品分组的查询")
@Slf4j
@RestController
@RequestMapping("/terminal/productGroup")
@Validated
public class TerminalProductGroupController {
    @Autowired
    private TerminalProductGroupService terminalProductGroupService;

    /**
     * 产品列表
     */
    @GetMapping(value = "/getAllGroupList")
    public RestResponse<List<TerminalProductGroupModel>> getAllGroupList() {
        try {
            //查询数据
            List<TerminalProductGroupModel> list = terminalProductGroupService.getAllGroupList();
            //返回数据
            return RestResponse.success("终端后台查询所有的产品成功", list);
        } catch (BusinessException e) {
            log.error("终端后台查询产品失败", e);
            return RestResponse.error(Integer.parseInt(e.getCode()), e.getMessage());
        } catch (Exception e) {
            log.error("终端后台查询产品失败", e);
            return RestResponse.error(e.getMessage());
        }
    }
}
