package com.intelliquor.cloud.shop.terminal.model.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * Description:
 *
 * <AUTHOR>
 * @since 2023/10/16 18:25
 */
@Getter
@AllArgsConstructor
public enum StrangeTerminalVisitCheckStatusEnum {
    /**
     * 未审核
     */
    UNCHECKED(0, "未审核"),

    /**
     * 已审核
     */
    CHECKED(1, "已审核"),

    /**
     * 审核失败
     */
    CHECK_FAIL(2, "审核失败");

    private final Integer key;

    private final String value;


    /**
     * 根据key获取value
     */
    public static String getValue(Integer key) {
        for (StrangeTerminalVisitCheckStatusEnum value : values()) {
            if (value.getKey().equals(key)) {
                return value.getValue();
            }
        }
        return null;
    }

}
