package com.intelliquor.cloud.shop.terminal.model.resp;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class TerminalBrokerTaskAdminResp {

    /**
     * 任务编号 (主键id)
     * */
    @ExcelProperty(value = "任务编号",index = 0)
    private Integer id;

    /**
     * 手机号
     * */
    @ExcelProperty(value = "手机号",index = 1)
    private String phone;

    /**
     * 姓名
     * */
    @ExcelProperty(value = "姓名",index = 2)
    private String name;

    /**
     * 任务指标
     * */
    @ExcelProperty(value = "任务指标",index = 3)
    private String taskIndicatorTypeName;

    /**
     * 岗位名称
     * */
    @ExcelProperty(value = "岗位名称",index = 4)
    private String postName;

    /**
     * 日期
     * */
    @ExcelProperty(value = "日期",index = 5)
    private String date;

    /**
     * 总任务
     * */
    @ExcelProperty(value = "总任务",index = 6)
    private String overallTask;

    /**
     * 已完成
     * */
    @ExcelProperty(value = "已完成",index = 7)
    private String finishedTask;

    /**
     * 完成率
     * */
    @ExcelProperty(value = "完成率",index = 8)
    private BigDecimal finishedPercentage;

    /**
     * 产品信息
     * */
    @ExcelProperty(value = "产品信息",index = 9)
    private String goodsData;

}
