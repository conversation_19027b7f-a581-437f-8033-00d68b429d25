package com.intelliquor.cloud.shop.terminal.service;

import com.github.pagehelper.PageInfo;
import com.intelliquor.cloud.shop.common.model.UserInfo;
import com.intelliquor.cloud.shop.terminal.model.TerminalVisitRecordModel;
import com.intelliquor.cloud.shop.terminal.model.req.TerminalVisitRecordReq;
import com.intelliquor.cloud.shop.terminal.model.req.VisitCheckTotalReq;
import com.intelliquor.cloud.shop.terminal.model.resp.TerminalVisitRecordAdminResp;
import com.intelliquor.cloud.shop.terminal.model.resp.TerminalVisitRecordResp;
import com.intelliquor.cloud.shop.terminal.model.resp.TerminalVisitShopResp;
import com.intelliquor.cloud.shop.terminal.model.resp.VisitCheckTotalResp;

import java.io.IOException;
import java.util.List;
import java.util.Map;

public interface TerminalVisitRecordService {

    /**
     * 查询数据
     *
     * @return
     */
    List<TerminalVisitRecordModel> selectList(Map<String, Object> searchMap) ;


    /**
     * 新增数据
     *
     * @param model
     */
    void insert(TerminalVisitRecordModel model);

    /**
     * 更新数据
     *
     * @param model
     */
    void update(TerminalVisitRecordModel model);

    /**
     * 删除数据
     *
     * @param id
     */
    void delete(Integer id);

    /**
     * 根据ID查询数据
     *
     * @param id
     */
    TerminalVisitRecordModel getById(Integer id);

    /**
     * 拜访记录列表
     * @param param
     * @return
     */
    PageInfo<TerminalVisitRecordResp> selectList4App(TerminalVisitRecordReq param);

    /**
     * 可拜访的终端列表
     * @param req
     * @return
     */
    PageInfo<TerminalVisitShopResp> getShopListByPage(TerminalVisitShopResp req);

    /**
     * 拜访记录列表-后台
     * @param param
     * @return
     */
    List<TerminalVisitRecordAdminResp> selectList4Admin(Integer page,Integer limit,TerminalVisitRecordReq param);

    /**
     * 导出
     * @param req
     * @param userInfo
     * @throws IOException
     */
    void export(TerminalVisitRecordReq req,
                                       UserInfo userInfo) throws IOException;

    PageInfo<TerminalVisitShopResp> getShopListByPage2(TerminalVisitShopResp req);

    PageInfo<TerminalVisitShopResp> getShopListByPage22024(TerminalVisitShopResp req);

    Integer insertReturnKey(TerminalVisitRecordModel model);

    Integer insertReturnKey2024Agreement(TerminalVisitRecordModel model);

    TerminalVisitRecordModel getByShopIdAndSignOutTime(Integer memberShopId, String yearMonthDay);

    TerminalVisitRecordModel getVisitRecordById(Integer visitRecordId);

    TerminalVisitRecordModel getVisitRecordByMemberShopId(Integer memberShopId);


    void sysDisplayImgToZt(TerminalVisitRecordModel model);

    PageInfo<TerminalVisitShopResp> getMemberShopListByPage(TerminalVisitShopResp req);

    /**
     * 查询拜访审核统计列表
     * @param req 查询条件
     * @return 拜访审核统计列表
     */
    List<VisitCheckTotalResp> getVisitCheckTotalListByPage(VisitCheckTotalReq req);

    /**
     * 查询拜访审核列表
     * @param req 请求参数
     * @return List<TerminalVisitRecordResp>
     */
    List<TerminalVisitRecordResp> selectVisitCheckList(TerminalVisitRecordReq req);

    /**
     * 拜访审核接口
     *
     * @param req 请求参数
     */
    void visitCheck(TerminalVisitRecordModel req);

    Integer selectVisitDayCount(Integer shopId, Integer accountId);
    Integer selectVisitWeekCount(Integer shopId, Integer accountId);
    Integer selectVisitMonthCount(Integer shopId, Integer accountId);



    void updateStepFlag(Integer id,Integer stepFlag);
}
