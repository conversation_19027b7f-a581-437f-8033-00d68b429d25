package com.intelliquor.cloud.shop.terminal.service.impl;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.intelliquor.cloud.shop.common.model.TerminalProtocolModel;
import com.intelliquor.cloud.shop.common.model.UserContext;
import com.intelliquor.cloud.shop.common.model.UserInfo;
import com.intelliquor.cloud.shop.common.model.resp.TerminalShopContractResp;
import com.intelliquor.cloud.shop.common.utils.TimeUtilis;
import com.intelliquor.cloud.shop.terminal.dao.*;
import com.intelliquor.cloud.shop.terminal.model.TCloudDealerInfoModel;
import com.intelliquor.cloud.shop.terminal.model.TerminalParentUpdateRecordModel;
import com.intelliquor.cloud.shop.terminal.model.TerminalShopInfoScheduleModel;
import com.intelliquor.cloud.shop.terminal.model.TerminalShopModel;
import com.intelliquor.cloud.shop.terminal.model.req.TerminalParentUpdateReq;
import com.intelliquor.cloud.shop.terminal.service.TerminalParentUpdateRecordService;
import com.intelliquor.cloud.shop.terminal.service.TerminalShopService;
import com.intelliquor.cloud.shop.terminal.util.TerminalDownloadCenterUtils;
import com.intelliquor.cloud.shop.terminal.util.enums.TerminalDownloadCenterEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

@Service
@Slf4j
public class TerminalParentUpdateRecordServiceImpl extends ServiceImpl<TerminalParentUpdateRecordDao, TerminalParentUpdateRecordModel> implements TerminalParentUpdateRecordService {

    @Resource
    public TerminalParentUpdateRecordDao updateRecordDao;

    @Resource
    public TCloudDealerInfoDao tCloudDealerInfoDao;

    @Autowired
    private UserContext userContext;

    @Resource
    private TerminalShopService shopService;

    @Autowired
    private TerminalProtocolDao terminalProtocolDao;

    @Autowired
    private TerminalShopContractDao terminalShopContractDao;

    @Autowired
    private TerminalShopDao terminalShopDao;

    @Autowired
    private TerminalShopInfoScheduleDao terminalShopInfoScheduleDao;

    @Autowired
    private TerminalDownloadCenterUtils terminalDownloadCenterUtils;

    @Override
    public PageInfo<TerminalParentUpdateRecordModel> page(Integer pageNum, Integer pageSize, TerminalParentUpdateReq terminalParentUpdateReq) {
        PageHelper.startPage(pageNum, pageSize);
        List<TerminalParentUpdateRecordModel> list = updateRecordDao.selectListPage(terminalParentUpdateReq);
        PageInfo<TerminalParentUpdateRecordModel> pageInfo = new PageInfo<>(list);
        return pageInfo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void terminalShopUpdate(List<String> terminalShopIds, Integer dealerId, String type) {
        List<TerminalShopModel> list = updateRecordDao.queryListByIds(terminalShopIds);
        // 查询分销商或者合伙人
        TCloudDealerInfoModel infoModel = tCloudDealerInfoDao.getById(dealerId);
        List<Integer> successIds = new ArrayList<>();
        List<String> deputyCodeList = new ArrayList<>();
        for (int i = 0; i < list.size(); i++) {
            TerminalShopModel model = list.get(i);
            TerminalParentUpdateRecordModel recordModel = new TerminalParentUpdateRecordModel();
            try {
                recordModel.setShopName(model.getShopName());
                recordModel.setDeputyCode(model.getDeputyCode());
                recordModel.setLeaderName(model.getLeaderName());
                recordModel.setLeaderPhone(model.getLeaderPhone());
                recordModel.setBeforeParentType(1); // 经销商
                //上级经销商/分销商信息
                if (Objects.nonNull(model.getDistributorId()) && model.getDistributorId() != 0) {
                    TCloudDealerInfoModel cloudDealerInfoModel = tCloudDealerInfoDao.selectById(model.getDistributorId());
                    recordModel.setBeforeParentCode(cloudDealerInfoModel.getDealerCode());
                    recordModel.setBeforeParentName(cloudDealerInfoModel.getDealerName());
                    recordModel.setBeforeParentType(2); // 分销商
                } else {
                    TerminalShopContractResp contractResp = terminalShopContractDao.selectTerminalShopContractResp(model.getId());
                    if (contractResp != null) {
                        recordModel.setBeforeParentCode(contractResp.getDealerCode());
                        recordModel.setBeforeParentName(contractResp.getDealerName());
                        recordModel.setBeforeParentType(1);
                    } else {

                    }
                }
                String typeName = "分销商";
                if (StringUtils.equals(type, "2")) {
                    typeName = "合伙人";
                } else if (StringUtils.equals(type, "0")) {
                    typeName = "经销商";
                }
                recordModel.setAfterParentInfo("类型:" + typeName + ";编码:" + infoModel.getDealerCode() + ";名称:" + infoModel.getDealerName());
                recordModel.setAfterParentDealerType(Integer.valueOf(type));
                recordModel.setAfterParentDealerName(infoModel.getDealerName());
                recordModel.setAfterParentDealerCode(infoModel.getDealerCode());
                recordModel.setCreateTime(new Date());
                recordModel.setCreateUser(userContext.getUserInfo().getUserId());
                recordModel.setCreateUserName(userContext.getUserInfo().getUsername());
                recordModel.setUpdateResult("1");
                recordModel.setShopId(model.getId());
                recordModel.setOperateType(0);
                // 校验是否通过
                boolean flag = false;
                flag = chekckArea(infoModel, model);
                if (StringUtils.equals(type, "1") && flag) { // 当类型为分销商时校验区域
                    recordModel.setRemark("区域不符合");
                    recordModel.setUpdateResult("0");
                    recordModel.setUpdateTime(new Date());
                } else {
                    TerminalShopInfoScheduleModel scheduleModel = copyProperties(model);
                    // 因为在处理同步中台的参数时，上级信息是通过这两个参数获取，因此这里要设置成接收到的上级id
                    scheduleModel.setDistributorId(dealerId);
                    scheduleModel.setCopartnerId(dealerId);
                    //查询合同数据
                    TerminalProtocolModel protocolModel = terminalProtocolDao.selectProtocolByTerminalShopId(model.getId());
                    // 合同
                    TerminalShopContractResp contractResp = terminalShopContractDao.selectTerminalShopContractResp(scheduleModel.getTerminalShopId());
                    // 根据下面的逻辑如果上级时合伙人要变更合同，所以这里也作处理
                    if (StringUtils.equals(type, "2")) {
                        contractResp.setContractCode(infoModel.getContractCode());
                        contractResp.setContractType(infoModel.getContractType());
                        contractResp.setContractCodeName(infoModel.getContractName());
                    }
                    // 调用国台修改分销商接口
                    shopService.sendZtData(scheduleModel, model, contractResp, 1, "终端修改分销商信息", protocolModel);
                    recordModel.setUpdateTime(new Date());
                    recordModel.setUpdateResult("1"); // 同步成功
                    // 修改本系统数据
                    successIds.add(model.getId());
                    deputyCodeList.add(model.getDeputyCode());
                }
            } catch (Exception e) {
                log.error("修改终端分销商数据失败：" + e.getMessage());
                recordModel.setRemark(e.getMessage());
                recordModel.setUpdateResult("0"); // 同步失败
                recordModel.setUpdateTime(new Date());
            }
            this.baseMapper.insert(recordModel);
        }
        // 批量修改终端terminalShop.distributorId(分销商id)
        if (successIds.size() > 0 && deputyCodeList.size() > 0) {
            if (StringUtils.equals(type, "0")) {
                batchUpdateDistributorId(successIds, 0); // 修改cloud_info表上级
                terminalShopInfoScheduleDao.batchUpdateParentDistributorId(deputyCodeList, 0, "批件处理修改上级为经销商id:" + dealerId);
            } else if (StringUtils.equals(type, "1")) { // 上级为分销商的逻辑
                batchUpdateDistributorId(successIds, dealerId); // 修改cloud_info表上级
                terminalShopInfoScheduleDao.batchUpdateParentDistributorId(deputyCodeList, dealerId, "批件处理修改上级为分销商id:" + dealerId);
            } else if (StringUtils.equals(type, "2")) { // 上级为合伙人的逻辑
                // 修改cloud_info表上级
                terminalShopDao.batchUpdateCopartnerId(successIds, dealerId);
                terminalShopInfoScheduleDao.batchUpdateParentCopartnerId(deputyCodeList, dealerId, "批件处理修改上级为合伙人id:" + dealerId);
                // 修改合同
                terminalShopDao.updateTerminalContract(infoModel, successIds);
            }
            // 修改中间表数据
            terminalShopInfoScheduleDao.updateRelation(deputyCodeList, dealerId);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void terminalShopUpdateNew(List<String> terminalShopIds, Integer dealerId, String type) {
        List<TerminalShopModel> list = updateRecordDao.queryListByIds(terminalShopIds);
        // 查询分销商或者合伙人
        TCloudDealerInfoModel infoModel = tCloudDealerInfoDao.getById(dealerId);
        List<Integer> successIds = new ArrayList<>();
        List<String> deputyCodeList = new ArrayList<>();
        for (int i = 0; i < list.size(); i++) {
            TerminalShopModel model = list.get(i);
            TerminalParentUpdateRecordModel recordModel = new TerminalParentUpdateRecordModel();
            try {
                recordModel.setShopName(model.getShopName());
                recordModel.setDeputyCode(model.getDeputyCode());
                recordModel.setLeaderName(model.getLeaderName());
                recordModel.setLeaderPhone(model.getLeaderPhone());
                recordModel.setBeforeParentType(1); // 经销商
                //上级经销商/分销商信息
                if (Objects.nonNull(model.getDistributorId()) && model.getDistributorId() != 0) {
                    TCloudDealerInfoModel cloudDealerInfoModel = tCloudDealerInfoDao.selectById(model.getDistributorId());
                    recordModel.setBeforeParentCode(cloudDealerInfoModel.getDealerCode());
                    recordModel.setBeforeParentName(cloudDealerInfoModel.getDealerName());
                    recordModel.setBeforeParentType(2); // 分销商
                } else {
                    TerminalShopContractResp contractResp = terminalShopContractDao.selectTerminalShopContractResp(model.getId());
                    if (contractResp != null) {
                        recordModel.setBeforeParentCode(contractResp.getDealerCode());
                        recordModel.setBeforeParentName(contractResp.getDealerName());
                        recordModel.setBeforeParentType(1);
                    } else {

                    }
                }
                String typeName = "分销商";
                if (StringUtils.equals(type, "2")) {
                    typeName = "合伙人";
                } else if (StringUtils.equals(type, "0")) {
                    typeName = "经销商";
                }
                recordModel.setAfterParentInfo("类型:" + typeName + ";编码:" + infoModel.getDealerCode() + ";名称:" + infoModel.getDealerName());
                recordModel.setAfterParentDealerType(Integer.valueOf(type));
                recordModel.setAfterParentDealerName(infoModel.getDealerName());
                recordModel.setAfterParentDealerCode(infoModel.getDealerCode());
                recordModel.setCreateTime(new Date());
                recordModel.setCreateUser(userContext.getUserInfo().getUserId());
                recordModel.setCreateUserName(userContext.getUserInfo().getUsername());
                recordModel.setUpdateResult("1");
                recordModel.setShopId(model.getId());
                recordModel.setOperateType(0);
                // 校验是否通过
                boolean flag = false;
                flag = chekckArea(infoModel, model);
                if (StringUtils.equals(type, "1") && flag) { // 当类型为分销商时校验区域
                    recordModel.setRemark("区域不符合");
                    recordModel.setUpdateResult("0");
                    recordModel.setUpdateTime(new Date());
                } else {
                    TerminalShopInfoScheduleModel scheduleModel = copyProperties(model);
                    // 因为在处理同步中台的参数时，上级信息是通过这两个参数获取，因此这里要设置成接收到的上级id
                    scheduleModel.setDistributorId(dealerId);
                    scheduleModel.setCopartnerId(dealerId);
                    //查询合同数据
                    TerminalProtocolModel protocolModel = terminalProtocolDao.selectProtocolByTerminalShopId(model.getId());
                    // 合同
                    TerminalShopContractResp contractResp = terminalShopContractDao.selectTerminalShopContractResp(scheduleModel.getTerminalShopId());
                    // 根据下面的逻辑如果上级时合伙人要变更合同，所以这里也作处理
                    if (StringUtils.equals(type, "2")) {
                        contractResp.setContractCode(infoModel.getContractCode());
                        contractResp.setContractType(infoModel.getContractType());
                        contractResp.setContractCodeName(infoModel.getContractName());
                    }
                    // 调用国台修改分销商接口
                    shopService.sendZtDataNew(scheduleModel, model, contractResp, 1, "终端修改分销商信息", protocolModel);
                    recordModel.setUpdateTime(new Date());
                    recordModel.setUpdateResult("1"); // 同步成功
                    // 修改本系统数据
                    successIds.add(model.getId());
                    deputyCodeList.add(model.getDeputyCode());
                }
            } catch (Exception e) {
                log.error("修改终端分销商数据失败：" + e.getMessage());
                recordModel.setRemark(e.getMessage());
                recordModel.setUpdateResult("0"); // 同步失败
                recordModel.setUpdateTime(new Date());
            }
            this.baseMapper.insert(recordModel);
        }
        // 批量修改终端terminalShop.distributorId(分销商id)
        if (successIds.size() > 0 && deputyCodeList.size() > 0) {
            if (StringUtils.equals(type, "0")) {
                batchUpdateDistributorId(successIds, 0); // 修改cloud_info表上级
                terminalShopInfoScheduleDao.batchUpdateParentDistributorId(deputyCodeList, 0, "批件处理修改上级为经销商id:" + dealerId);
            } else if (StringUtils.equals(type, "1")) { // 上级为分销商的逻辑
                batchUpdateDistributorId(successIds, dealerId); // 修改cloud_info表上级
                terminalShopInfoScheduleDao.batchUpdateParentDistributorId(deputyCodeList, dealerId, "批件处理修改上级为分销商id:" + dealerId);
            } else if (StringUtils.equals(type, "2")) { // 上级为合伙人的逻辑
                // 修改cloud_info表上级
                terminalShopDao.batchUpdateCopartnerId(successIds, dealerId);
                terminalShopInfoScheduleDao.batchUpdateParentCopartnerId(deputyCodeList, dealerId, "批件处理修改上级为合伙人id:" + dealerId);
                // 修改合同
                terminalShopDao.updateTerminalContract(infoModel, successIds);
            }
            // 修改中间表数据
            terminalShopInfoScheduleDao.updateRelation(deputyCodeList, dealerId);
        }
    }

    @Override
    public void export(TerminalParentUpdateReq terminalParentUpdateReq) throws IOException {
        try {
            //先获取当前登录的用户
            UserInfo userInfo = userContext.getUserInfo();
            TerminalDownloadCenterEnum downloadEnum = TerminalDownloadCenterEnum.TERMINAL_UPDATE_PARENT_TOTAL;
            Integer ExcelId = terminalDownloadCenterUtils.insertOrUpdateOrderDownloadCenterTerminal(userInfo, terminalParentUpdateReq.getBatchNo(), downloadEnum);
            ByteArrayOutputStream os = new ByteArrayOutputStream();
            ExcelWriter excelWriter = EasyExcel
                    .write(os, TerminalParentUpdateRecordModel.class)
                    .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
                    .build();
            WriteSheet writeSheet = EasyExcel.writerSheet("终端上级变更").build();
            terminalParentUpdateReq.setMaxLimit(10000);
           terminalParentUpdateReq.setStartId(1);
            while (true) {
                List<TerminalParentUpdateRecordModel> exportData = updateRecordDao.selectListPage(terminalParentUpdateReq);
                if (CollectionUtils.isEmpty(exportData)) {
                    break;
                }
                for (TerminalParentUpdateRecordModel terminalParentUpdateRecordModel : exportData) {
                   terminalParentUpdateRecordModel.setUpdateResult(StringUtils.equals(terminalParentUpdateRecordModel.getUpdateResult(), "1") ? "变更成功" : "变更失败");
                    if(terminalParentUpdateRecordModel.getShopType() == null){
                        terminalParentUpdateRecordModel.setShopTypeStr("终端");
                    }else{
                        terminalParentUpdateRecordModel.setShopTypeStr(terminalParentUpdateRecordModel.getShopType() == 1 ? "终端" :
                                terminalParentUpdateRecordModel.getShopType() == 2 ? "合伙人" :
                                        terminalParentUpdateRecordModel.getShopType() == 3 ? "分销商" :
                                                terminalParentUpdateRecordModel.getShopType() == 4 ? "会员" : "");
                    }
                   if (Objects.nonNull(terminalParentUpdateRecordModel.getBeforeParentType())) {
                       terminalParentUpdateRecordModel.setBeforeParentTypeStr(terminalParentUpdateRecordModel.getBeforeParentType() == 1?"经销商":
                               terminalParentUpdateRecordModel.getBeforeParentType() == 2 ? "分销商":"");
                   }
                    if(terminalParentUpdateRecordModel.getZtSend() == null){
                        terminalParentUpdateRecordModel.setZtSendStr("推送中台成功");
                    }else{
                        terminalParentUpdateRecordModel.setZtSendStr(terminalParentUpdateRecordModel.getZtSend() == 1?"推送中台成功":
                                terminalParentUpdateRecordModel.getZtSend() == 0?"暂未推送中台":
                                        terminalParentUpdateRecordModel.getZtSend() == 2?"推送中台失败":"");
                    }


                }
                excelWriter.write(exportData, writeSheet);
                terminalParentUpdateReq.setStartId(exportData.get(0).getId());
            }
            excelWriter.finish();
            String fileName = (new StringBuffer()).append("ZDBGSJ").append(TimeUtilis.getCurrentFormatTime("yyyyMMddHHmmss")).append(".xlsx").toString();
            String fileAddress = terminalDownloadCenterUtils.getFileUrl(fileName, os.toByteArray());
            terminalDownloadCenterUtils.updateOrderDownloadCenterTerminalById(ExcelId, fileAddress);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 批量修改终端terminalShop.distributorId(分销商id)
     *
     * @param successIds 终端id集合
     * @param dealerId   分销商id
     * <AUTHOR>
     */
    private void batchUpdateDistributorId(List<Integer> successIds, Integer dealerId) {
        terminalShopDao.batchUpdateDistributorId(successIds, dealerId);
    }

    private TerminalShopInfoScheduleModel copyProperties(TerminalShopModel model) {
        TerminalShopInfoScheduleModel shopInfoScheduleModel = new TerminalShopInfoScheduleModel();
        BeanUtils.copyProperties(model, shopInfoScheduleModel);
        shopInfoScheduleModel.setTerminalShopId(model.getId());
        return shopInfoScheduleModel;
    }

    /**
     * 校验区域是否符合
     *
     * @param infoModel
     * @param model
     * @return
     */
    private boolean chekckArea(TCloudDealerInfoModel infoModel, TerminalShopModel model) {
//        if (!StringUtils.equals(infoModel.getProvinces(),model.getProvince())) {
//            return true;
//        }
//        if (!StringUtils.equals(infoModel.getCity(),model.getCity())) {
//            return true;
//        }
//        return !StringUtils.equals(infoModel.getDistrict(), model.getDistrict());
        // 2024/1/2 sunshine 修改变更终端上级为分销商，判断分销商业务区域是否符合
        Boolean flag = true;
        if (StringUtils.isNotBlank(infoModel.getDealerAreaList())) {
            String shopArea = (StringUtils.isNotBlank(model.getProvince()) ? model.getProvince() : "")
                    + (StringUtils.isNotBlank(model.getCity()) ? model.getCity() : "")
                    + (StringUtils.isNotBlank(model.getDistrict()) ? model.getDistrict() : "");
            String[] areaList = infoModel.getDealerAreaList().split(",");
            for (String area : areaList) {
                if (StringUtils.equals(area, shopArea)) {
                    flag = false;
                    break;
                }
            }
        }
        return flag;
    }
}
