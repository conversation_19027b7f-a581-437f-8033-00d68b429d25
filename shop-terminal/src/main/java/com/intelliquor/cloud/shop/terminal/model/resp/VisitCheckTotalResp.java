package com.intelliquor.cloud.shop.terminal.model.resp;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * @Author: MAX
 * @CreateTime: 2023-07-27  15:21
 */
@Data
public class VisitCheckTotalResp implements Serializable {

    private static final long serialVersionUID = -3733933339764150733L;
    /**
     * 人员id
     */
    private Long accountManagerId;

    /**
     * 人员姓名
     */
    private String accountManagerName;

    /**
     * 人员手机号
     */
    private String accountManagerPhone;

    /**
     * 周统计数据
     */
    private List<VisitCheckWeekResp> weekData;
}
