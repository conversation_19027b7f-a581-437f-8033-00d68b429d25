package com.intelliquor.cloud.shop.terminal.service.impl;


import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.intelliquor.cloud.shop.common.exception.BusinessException;
import com.intelliquor.cloud.shop.common.model.CloudDealerInfoModel;
import com.intelliquor.cloud.shop.common.model.TerminalAccountManagerModel;
import com.intelliquor.cloud.shop.common.model.TerminalScanDetailModel;
import com.intelliquor.cloud.shop.common.model.UserContext;
import com.intelliquor.cloud.shop.common.utils.SearchUtil;
import com.intelliquor.cloud.shop.terminal.dao.*;
import com.intelliquor.cloud.shop.terminal.model.*;
import com.intelliquor.cloud.shop.terminal.model.dto.StrangeTerminalVisitRecordDto;
import com.intelliquor.cloud.shop.common.enums.DeleteFlagEnum;
import com.intelliquor.cloud.shop.common.enums.TerminalAccountTypeEnum;
import com.intelliquor.cloud.shop.terminal.model.enums.TerminalTaskPackageStatusFlagEnum;
import com.intelliquor.cloud.shop.terminal.model.enums.TerminalYearlyDetailTypeEnum;
import com.intelliquor.cloud.shop.terminal.model.req.*;
import com.intelliquor.cloud.shop.terminal.model.resp.*;
import com.intelliquor.cloud.shop.terminal.service.*;
import com.intelliquor.cloud.shop.terminal.util.RedisUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAdjusters;
import java.util.*;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;
import java.util.stream.Collectors;


/**
 * <p>
 * 任务基本信息-用于国台客户经理发布任务 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-19
 */
@Slf4j
@Service
public class TerminalTaskPackageServiceImpl
        extends ServiceImpl<TerminalTaskPackageDao, TerminalTaskPackageModel>
        implements ITerminalTaskPackageService {

    @Resource
    private TerminalTaskPackageDao taskPackageDao;

    @Resource
    private TerminalTaskPackageRegionDao taskPackageRegionDao;

    @Resource
    private TerminalTaskMonthlyDao monthlyDao;

    @Resource
    private TerminalTaskMonthlyDetailDao monthlyDetailDao;

    @Resource
    private TerminalTaskMonthlyGroupDetailDao monthlyGroupDetailDao;

    @Resource
    private TerminalTaskYearlyDao yearlyDao;

    @Resource
    private TerminalTaskYearlyDetailDao yearlyDetailDao;

    @Resource
    private TerminalTaskReceiveDao receiveDao;

    @Resource
    private TerminalTaskReceiveMonthDao receiveMonthDao;

    @Resource
    private TerminalShopDao terminalShopDao;

    @Autowired
    private RedisUtil redisUtil;


    @Autowired
    private TerminalAccountManagerDao terminalAccountManagerDao;


    @Autowired
    private UserContext userContext;

    @Autowired
    private TerminalMeetingRecordScoreService terminalMeetingRecordScoreService;

    @Autowired
    private ITerminalTaskPackageRegionService terminalTaskPackageRegionService;
    @Autowired
    private ITerminalTaskMonthlyService terminalTaskMonthlyService;

    @Autowired
    private ITerminalTaskMonthlyDetailService terminalTaskMonthlyDetailService;

    @Autowired
    private ITerminalTaskYearlyService terminalTaskYearlyService;

    @Autowired
    private ITerminalTaskYearlyDetailService terminalTaskYearlyDetailService;

    @Autowired
    private TerminalVisitRecordScoreDao terminalVisitRecordScoreDao;

    @Autowired
    private StrangeTerminalVisitRecordService strangeTerminalVisitRecordService;
    @Autowired
    private ITerminalTaskReceiveService terminalTaskReceiveService;

    @Autowired
    private ITerminalTaskReceiveMonthService terminalTaskReceiveMonthService;

    @Autowired
    private TerminalScanDetailDao terminalScanDetailDao;

    @Autowired
    private TerminalTaskMonthlyGroupDetailService terminalTaskMonthlyGroupDetailService;

    @Autowired
    private TerminalScanDetailService terminalScanDetailService;

    private static final Lock lock=new ReentrantLock();

    private static final Lock claimTaskLock=new ReentrantLock();

    @Override
    public String getTaskNum(Integer year) {
        //生成任务编码，按照所选年份下所有任务编码自增方式，5位数字字符+年份
        //获取年份后两位
        String taskNum="";
        try {
            //获取锁
            lock.tryLock();
            //redis获取编码
            taskNum = (String)redisUtil.get(year + "taskNum");
            if(taskNum==null||taskNum.equals("")){
                //获取数据库中当年最新的任务编号
                LambdaQueryWrapper<TerminalTaskPackageModel> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(TerminalTaskPackageModel::getYear,year)
                        .orderByDesc(TerminalTaskPackageModel::getId).last("limit 1");
                TerminalTaskPackageModel one = taskPackageDao.selectOne(queryWrapper);
                if(one==null){
                    //初始数据
                    taskNum = String.format("%07d", year%2000+100);
                }else{
                    //截取字符串去除年份后,转数字自增100
                    Integer integer = Integer.valueOf(one.getTaskNum());
                    integer+=100;
                    taskNum = String.format("%07d", integer);
                }
            }else{
                //截取字符串去除年份后,转数字自增100
                Integer integer = Integer.valueOf(taskNum);
                integer+=100;
                taskNum = String.format("%07d", integer);
            }
            redisUtil.set(year+"taskNum",taskNum);
        }catch (Exception e){
            log.error("获取任务编码异常:"+e);
            throw e;
        }finally {
            //释放锁
            lock.unlock();
        }
        return taskNum;
    }

    @Override
    public List<TerminalTaskPackageResp> selectTerminalTaskPackageList(Integer page, Integer limit, TerminalTaskPackageReq terminalTaskPackageReq) {

        //分页不为空先分页
        if(null != page && null != limit){
            PageHelper.startPage(page,limit);
        }
        if(!StringUtils.isEmpty(terminalTaskPackageReq.getDistrict())){
            List<Integer> districtIds = Arrays.stream(terminalTaskPackageReq.getDistrict().split(",")).map(Integer::parseInt).collect(Collectors.toList());
            terminalTaskPackageReq.setDistrictIds(districtIds);
        }
        //查询任务列表
        List<TerminalTaskPackageResp> selectDataList = taskPackageDao.selectTerminalTaskPackageList(terminalTaskPackageReq);
        //返回
        return selectDataList;
    }



    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<Pair<Integer, String>> save(List<TerminalTaskPackageReq> modelList,
                                            Integer userId,String phone) throws Exception {
        if(phone==null||"".equals(phone)){
            throw new BusinessException("创建任务失败,当前登陆人未设置手机号");
        }

        List<Pair<Integer, String>> pairList=new ArrayList<>();
        for (int i = 0; i < modelList.size(); i++) {
            TerminalTaskPackageReq model = modelList.get(i);
            TerminalTaskPackageModel packageModel=new TerminalTaskPackageModel();
            BeanUtils.copyProperties(model,packageModel);
            List<TerminalTaskPackageRegionModel> regionModelList = model.getRegionModelList();

            //新增
            if(model.getId()==null){
                //填充手机号
                packageModel.setCreatorPhone(phone);
                Integer tpId = insert(packageModel,regionModelList,userId);
                pairList.add(Pair.of(tpId, packageModel.getTaskNum()));
            }else{
                //编辑
                update(packageModel,regionModelList,userId);
                pairList.add(Pair.of(packageModel.getId(), packageModel.getTaskNum()));
            }
        }
        return pairList;
    }

    /**
     * 1.创建任务包
     * 2.创建任务包下的多个行政区域关联
     * @param model
     * @param regionModel
     */
    public int insert(TerminalTaskPackageModel model,
                       List<TerminalTaskPackageRegionModel> regionModel,Integer userId){
        model.setCreateBy(userId);
        //创建任务
        taskPackageDao.insert(model);
        Integer tpId = model.getId();
        //一个任务会有多个行政区域的设置,保存行政区域
        regionModel.stream().forEach(x->{x.setTpId(tpId);x.setCreateBy(userId);});
        taskPackageRegionDao.insertBatch(regionModel);
        return tpId;
    }

    /**
     * 编辑任务包
     * @param model
     * @param regionModel
     */
    public void update(TerminalTaskPackageModel model,
                       List<TerminalTaskPackageRegionModel> regionModel,Integer userId) {
        //主任务id
        Integer id = model.getId();
        //修改时间置空，这是一个业务需求
        TerminalTaskPackageModel taskPackageModel = taskPackageDao.selectById(id);
        if(taskPackageModel!=null){
            model.setUpdateTime(taskPackageModel.getUpdateTime());
        }
        taskPackageDao.updateById(model);
        //获取id不为空的id集合
        //获取当前应保存的所有id集合
        List<Integer> collectIds = new ArrayList<>();

        for (int i = 0; i < regionModel.size(); i++) {
            TerminalTaskPackageRegionModel regionModel1 = regionModel.get(i);
            if(regionModel1.getId()==null){
                //根据主任务id和省市区id来判断此条记录是否存在（DeleteFlag=1的状态），
                // 存在则不新增，改为把DeleteFlag置为0
                LambdaQueryWrapper<TerminalTaskPackageRegionModel> queryWrapper=new LambdaQueryWrapper();
                queryWrapper.eq(TerminalTaskPackageRegionModel::getTpId,id)
                        .eq(TerminalTaskPackageRegionModel::getDistrictId,regionModel1.getDistrictId())
                        .eq(TerminalTaskPackageRegionModel::getDeleteFlag,1);
                TerminalTaskPackageRegionModel regionModel2 = taskPackageRegionDao.selectOne(queryWrapper);
                if(regionModel2==null){
                    //新增
                    regionModel1.setCreateBy(userId);
                    regionModel1.setTpId(id);
                    taskPackageRegionDao.insert(regionModel1);
                }else{
                    //修改状态
                    regionModel1.setUpdateBy(userId);
                    regionModel1.setTpId(id);
                    regionModel1.setDeleteFlag(0);
                    taskPackageRegionDao.updateById(regionModel1);
                }
            }else{
                regionModel1.setUpdateBy(userId);
//                regionModel1.setDeleteFlag(0);
                taskPackageRegionDao.updateById(regionModel1);
            }
            collectIds.add(regionModel1.getId());
        }
        //除应保存的行政区域，其余状态当前任务包下的行政区域记录变为删除
        LambdaUpdateWrapper<TerminalTaskPackageRegionModel> updateWrapper=new LambdaUpdateWrapper();
        updateWrapper.eq(TerminalTaskPackageRegionModel::getTpId,id)
                .set(TerminalTaskPackageRegionModel::getDeleteFlag,1);
        if(collectIds!=null&&collectIds.size()>0){
            updateWrapper.notIn(TerminalTaskPackageRegionModel::getId,collectIds);
        }
        taskPackageRegionDao.update(null,updateWrapper);
    }

    /**
     * 创建任务包详情
     * @param monthlyReq
     * @param yearlyReq
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void insertDetail(List<TerminalTaskMonthlyReq> monthlyReq,
                             TerminalTaskYearlyReq yearlyReq,Integer userId) {
        //月度任务创建
        for (int i=0;i<monthlyReq.size();i++){
            TerminalTaskMonthlyReq terminalTaskMonthlyReq = monthlyReq.get(i);
            modifyMonthlyTask(terminalTaskMonthlyReq,userId);
        }
        //年度任务创建
        modifyYearlyTask(yearlyReq,userId);
        //获取主任务id
        Integer tpId = yearlyReq.getTpId();
        TerminalTaskPackageModel taskPackageModel=new TerminalTaskPackageModel();
        taskPackageModel.setId(tpId);
        taskPackageModel.setConfirmStatus(1);
        //创建完成后，修改主任务状态为：已确认 ;确认状态(0:未确认;1:已确认)
        taskPackageDao.updateById(taskPackageModel);
    }


    /**
     * 编辑任务包详情
     * @param monthlyReq
     * @param yearlyReq
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateDetail(List<TerminalTaskMonthlyReq> monthlyReq,
                             TerminalTaskYearlyReq yearlyReq,Integer userId) {
        Integer id=null;
        //月度任务更改
        for (int i=0;i<monthlyReq.size();i++){
            id=monthlyReq.get(0).getTpId();
            TerminalTaskMonthlyReq terminalTaskMonthlyReq = monthlyReq.get(i);
            modifyMonthlyTask(terminalTaskMonthlyReq,userId);
        }
        //年任务更改
        modifyYearlyTask(yearlyReq,userId);
        //更新主任务包修改时间
        TerminalTaskPackageModel taskPackageModel=new TerminalTaskPackageModel();
        taskPackageModel.setId(id);
        taskPackageDao.updateById(taskPackageModel);
    }
    /**
     * 判断用户是否有权限
     */
    // 校验用户是否有权限
    private void checkUserPermission() {
        // 根绝checkUserHasPermission返回的布尔值输出throw
        if (!checkUserHasPermission()) {
            throw new BusinessException("当前用户无权限");
        }
    }

    /**
     * 判断用户是否有权限
     */
    @Override
    public Boolean checkUserHasPermission() {
        String userPhone = userContext.getUserInfo().getPhone();
        // 使用queryWrapper查询terminalAccountManager表中userPhone对应的数据
        LambdaQueryWrapper<TerminalAccountManagerModel> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TerminalAccountManagerModel::getPhone, userPhone)
                    .eq(TerminalAccountManagerModel::getType, TerminalAccountTypeEnum.CUSTOMER_MANAGER.getKey());
        TerminalAccountManagerModel terminalAccountManagerModel = terminalAccountManagerDao.selectOne(queryWrapper);
        if (Objects.isNull(terminalAccountManagerModel)){
            return false;
        }
        return true;
    }

    /**
     * 判断小程序用户是否有权限
     */
    @Override
    public Boolean checkAppletUserHasPermission() {
        String userPhone = userContext.getTerminalModel().getPhone();
        // 使用queryWrapper查询terminalAccountManager表中userPhone对应的数据
        LambdaQueryWrapper<TerminalAccountManagerModel> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TerminalAccountManagerModel::getPhone, userPhone)
                .eq(TerminalAccountManagerModel::getType, TerminalAccountTypeEnum.CUSTOMER_MANAGER.getKey());
        TerminalAccountManagerModel terminalAccountManagerModel = terminalAccountManagerDao.selectOne(queryWrapper);
        if (Objects.isNull(terminalAccountManagerModel)){
            return false;
        }
        return true;
    }

    /**
     * 月度任务更改
     * @param monthlyReq
     * @param userId
     */
    private void modifyMonthlyTask(TerminalTaskMonthlyReq monthlyReq,Integer userId){
        // 先校验权重字段
        monthlyReq.checkWeight();
        TerminalTaskMonthlyModel monthlyModel=new TerminalTaskMonthlyModel();
        BeanUtils.copyProperties(monthlyReq,monthlyModel);
        //List<TerminalTaskMonthlyDetailModel> monthlyDetailModel = monthlyReq.getMonthlyDetailModel();
        List<TerminalTaskMonthlyGroupDetailModel> monthlyGroupDetailModels = monthlyReq.getMonthlyGroupDetailModels();
        //月度任务更改
        //如果id不存在则新增
        if(StringUtils.isEmpty(monthlyModel.getId())){
            monthlyModel.setCreateBy(userId);
            monthlyDao.insert(monthlyModel);
        }else{
            monthlyModel.setUpdateBy(userId);
            monthlyDao.updateById(monthlyModel);
        }
        Integer monthlyModelId = monthlyModel.getId();
        //月度收货设置更改
        //获取当前应保存的所有id集合
        //List<Integer> collectIds = new ArrayList<>();

        //月任务的收货设置不能有相同的货品数据
//        if(monthlyDetailModel!=null){
//            List<Integer> collect = monthlyDetailModel.stream()
//                    .map(TerminalTaskMonthlyDetailModel::getBtrcId).collect(Collectors.toList());
//            if(collect!=null&&collect.size()>0){
//                long count = collect.stream().distinct().count();
//                if(collect.size()>count){
//                    throw new BusinessException(monthlyModel.getMonth()+"月有重复收货设置数据");
//                }
//            }
//            for (int i = 0; i < monthlyDetailModel.size(); i++) {
//                TerminalTaskMonthlyDetailModel detailModel = monthlyDetailModel.get(i);
//                //如果id不存在则新增
//                if(StringUtils.isEmpty(detailModel.getId())){
//                    detailModel.setTmId(monthlyModelId);
//                    detailModel.setCreateBy(userId);
//                    monthlyDetailDao.insert(detailModel);
//                }else{
//                    detailModel.setUpdateBy(userId);
////                detailModel.setDeleteFlag(0);
//                    monthlyDetailDao.updateById(detailModel);
//
//                }
//                collectIds.add(detailModel.getId());
//            }
//        }
//        //月度任务下其余记录状态变更
//        LambdaUpdateWrapper<TerminalTaskMonthlyDetailModel> updateWrapper=new LambdaUpdateWrapper();
//        updateWrapper.eq(TerminalTaskMonthlyDetailModel::getTmId,monthlyModelId)
//                .set(TerminalTaskMonthlyDetailModel::getDeleteFlag,1);
//        if(collectIds!=null&&collectIds.size()>0){
//            updateWrapper.notIn(TerminalTaskMonthlyDetailModel::getId,collectIds);
//        }
//        monthlyDetailDao.update(null,updateWrapper);
        // 月度
        if(monthlyGroupDetailModels != null){
            for(TerminalTaskMonthlyGroupDetailModel terminalTaskMonthlyGroupDetailModel : monthlyGroupDetailModels){
                if(StringUtils.isEmpty(terminalTaskMonthlyGroupDetailModel.getId())) {
                    terminalTaskMonthlyGroupDetailModel.setTaskMonthId(monthlyModelId);
                    terminalTaskMonthlyGroupDetailModel.setCreateUserId(userId);
                    terminalTaskMonthlyGroupDetailModel.setUpdateUserId(userId);
                    monthlyGroupDetailDao.insert(terminalTaskMonthlyGroupDetailModel);
                } else {
                    terminalTaskMonthlyGroupDetailModel.setUpdateUserId(userId);
                    monthlyGroupDetailDao.updateById(terminalTaskMonthlyGroupDetailModel);

                }
            }

        }
    }

    /**
     * 年度任务更改
     * @param yearlyReq
     * @param userId
     */
    private void modifyYearlyTask(TerminalTaskYearlyReq yearlyReq,Integer userId){
        //年度任务更改
        TerminalTaskYearlyModel yearlyModel=new TerminalTaskYearlyModel();
        BeanUtils.copyProperties(yearlyReq,yearlyModel);

        if(StringUtils.isEmpty(yearlyModel.getId())){
            yearlyModel.setCreateBy(userId);
            yearlyDao.insert(yearlyModel);
        }else{
            yearlyModel.setUpdateBy(userId);
            yearlyDao.updateById(yearlyModel);
        }
        Integer yearlyModelId = yearlyModel.getId();
        //年度任务详情更改
        List<TerminalTaskYearlyDetailModel> yearlyDetailModels = yearlyReq.getYearlyDetailModel();
        List<Integer> collectIds = new ArrayList<>();
        if(yearlyDetailModels!=null){
//年任务的分配终端不能有相同终端记录
            List<Integer> collect = yearlyDetailModels.stream()
                    .map(TerminalTaskYearlyDetailModel::getMsId).collect(Collectors.toList());


            if(collect!=null&&collect.size()>0){
                long count = collect.stream().distinct().count();
                if(collect.size()>count){

//                    throw new BusinessException("年度任务有重复终端数据");
                    // 获取重复的终端id，查询终端名称
                    List<Integer> duplicates = new ArrayList<>();

                    for (int i = 0; i < collect.size(); i++) {
                        for (int j = i + 1; j < collect.size(); j++) {
                            if (collect.get(i).equals(collect.get(j))) {
                                if (!duplicates.contains(collect.get(i))) {
                                    duplicates.add(collect.get(i));
                                }
                            }
                        }
                    }
                    log.info("duplicates",duplicates);
                    LambdaQueryWrapper<TerminalShopModel> queryWrapper = new LambdaQueryWrapper<>();
                    queryWrapper.in(TerminalShopModel::getMemberShopId, duplicates);
                    List<TerminalShopModel> terminalShopModels = terminalShopDao.selectList(queryWrapper);
                    String msNames = terminalShopModels.stream().map(TerminalShopModel::getShopName).collect(Collectors.joining(","));
                    throw new BusinessException("年度任务有重复终端数据，重复终端名称为：" + msNames);
                }
            }

            for (int i = 0; i < yearlyDetailModels.size(); i++) {
                TerminalTaskYearlyDetailModel model = yearlyDetailModels.get(i);
                //如果id不存在则新增
                if(StringUtils.isEmpty(model.getId())){
                    //如果此终端已经分配给了其他任务，且状态正常,则不可新增
                    LambdaQueryWrapper<TerminalTaskYearlyDetailModel> queryWrapper=new LambdaQueryWrapper();
                    queryWrapper.eq(TerminalTaskYearlyDetailModel::getMsId,model.getMsId())
                            .eq(TerminalTaskYearlyDetailModel::getDeleteFlag,0)
                            .eq(TerminalTaskYearlyDetailModel::getStatus,TerminalTaskPackageStatusFlagEnum.VALID.getKey());
                    TerminalTaskYearlyDetailModel model1 = yearlyDetailDao.selectOne(queryWrapper);


                    if(model1!=null){
                        LambdaQueryWrapper<TerminalShopModel> queryWrapper1 = new LambdaQueryWrapper<>();
                        queryWrapper1.in(TerminalShopModel::getMemberShopId, model1.getMsId());
                        TerminalShopModel terminalShopModel = terminalShopDao.selectOne(queryWrapper1);
                        log.warn("年度终端数据分配异常,"+model1.getMsId()+"终端已被分配，不能重复分配");
                        log.warn("年度终端数据分配异常,"+terminalShopModel.getShopName()+"终端已被分配，不能重复分配");
                        throw new BusinessException("年度终端数据分配异常,"+terminalShopModel.getShopName()+"终端已被分配，不能重复分配");
                    }
                    //如果当前带插入数据已在数据库中存在（用年任务id和终端id判断）,
                    // 但是DeleteFlag=1,则不新增，把DeleteFlag置0
                    LambdaQueryWrapper<TerminalTaskYearlyDetailModel>  queryWrapper1=new LambdaQueryWrapper();
                    queryWrapper.eq(TerminalTaskYearlyDetailModel::getTyId,yearlyModelId)
                            .eq(TerminalTaskYearlyDetailModel::getMsId,model.getMsId())
                            .eq(TerminalTaskYearlyDetailModel::getDeleteFlag,1);
                    TerminalTaskYearlyDetailModel model2 = yearlyDetailDao.selectOne(queryWrapper);
                    if(model2==null){
                        //新增
                        model.setTyId(yearlyModelId);
                        model.setCreateBy(userId);
                        yearlyDetailDao.insert(model);
                    }else {
                        //修改状态
                        model.setUpdateBy(userId);
                        model.setTyId(yearlyModelId);
                        model.setDeleteFlag(0);
                        model.setStatus(TerminalTaskPackageStatusFlagEnum.VALID.getKey());
                        yearlyDetailDao.updateById(model);
                    }
                }else{
                    model.setUpdateBy(userId);
                    model.setDeleteFlag(0);
                    model.setStatus(TerminalTaskPackageStatusFlagEnum.VALID.getKey());
                    yearlyDetailDao.updateById(model);
                }
                collectIds.add(model.getId());
            }
        }
        //对当前年度任务下其余终端状态变为已删除
        LambdaUpdateWrapper<TerminalTaskYearlyDetailModel> updateWrapper=new LambdaUpdateWrapper();
        updateWrapper.eq(TerminalTaskYearlyDetailModel::getTyId,yearlyModelId)
                //.eq(TerminalTaskYearlyDetailModel::getTerminalType,0)
                .set(TerminalTaskYearlyDetailModel::getDeleteFlag,1)
                .set(TerminalTaskYearlyDetailModel::getStatus,TerminalTaskPackageStatusFlagEnum.INVALID.getKey());
        if(collectIds!=null&&collectIds.size()>0){
            updateWrapper.notIn(TerminalTaskYearlyDetailModel::getId,collectIds);
        }
        yearlyDetailDao.update(null,updateWrapper);
    }

    /**
     * 获取当前登录客户经理关联的经销商列表
     * @param accountId 客户经理Id
     * @return
     */
    @Override
    public List<CloudDealerInfoModel> getDealerList(Integer accountId) {
        List<CloudDealerInfoModel> list = taskPackageDao.selectDealerList(accountId);
        return list;
    }

    /**
     * 查询当前登录业务代理所在岗位关联所在区域的可见任务包
     * 如果已有领取的任务包，则只能看见自己领取的任务
     * @param year
     * @param taskNum
     * @param userId
     * @return
     */
    @Override
    public List<TerminalTaskPackageAdminResp> agent_getList(Integer year,String taskNum,Integer userId) {
        //查看当前业代是否已领取任务
//        LambdaQueryWrapper<TerminalTaskReceiveModel> queryWrapper=new LambdaQueryWrapper<>();
//        queryWrapper.eq(TerminalTaskReceiveModel::getBrokerId,userId)
//                .eq(TerminalTaskReceiveModel::getDeleteFlag,0);
//        TerminalTaskReceiveModel model = receiveDao.selectOne(queryWrapper);
        TerminalTaskReceiveModel model = terminalTaskReceiveService.getReceiveByYear(year,userId);
        //如果已有领取的任务包，则只能看见自己领取的任务
        List<TerminalTaskPackageAdminResp> respList=null;
        if(model==null){
            respList = taskPackageDao.agent_selectList(year,taskNum,userId);
        }else {
            Integer tpId = model.getTpId();
            respList = taskPackageDao.agent_selectOwner(year,tpId,userId);
        }
        return respList;
    }


    /**
     * 根据主任务id获取当前月份得月度任务数据与月度任务得详情
     * @param taskId
     * @param month
     * @return
     */
    @Override
    public TerminalTaskMonthlyResp agent_getMonthDetail(Integer taskId, Integer month) {
        TerminalTaskMonthlyResp resp = taskPackageDao.agent_getMonth(taskId, month);
        if(resp!=null){
            //LambdaQueryWrapper<TerminalTaskMonthlyDetailModel> queryWrapper=new LambdaQueryWrapper<>();
            //queryWrapper.eq(TerminalTaskMonthlyDetailModel::getTmId,resp.getTmId())
            //        .eq(TerminalTaskMonthlyDetailModel::getDeleteFlag,0);
            //List<TerminalTaskMonthlyDetailModel> list = monthlyDetailDao.selectList(queryWrapper);
            //resp.setMonthlyDetailModel(list);
            //获取月度任务类型设置详情
            List<TerminalTaskMonthlyGroupDetailResp> terminalTaskMonthlyGroupDetailResps = terminalTaskMonthlyGroupDetailService.getGroupDetailListByMonthId(resp.getTmId());
            resp.setMonthlyGroupDetailModels(terminalTaskMonthlyGroupDetailResps);
        }
        return resp;
    }

    /**
     * 填充月度任务完成率与完成详情
     * @param resp
     * @param userId
     * @return
     */
    @Override
    public TerminalTaskMonthlyResp agent_fillMonthDetail(TerminalTaskMonthlyResp resp,Integer userId) {
        //根据当前登陆人和主任务id、月度任务id,获取当前月度任务完成率
        //主任务id
        Integer id = resp.getId();
        //获取月份与年
        TerminalTaskPackageModel taskPackageModel = taskPackageDao.selectById(id);
        Integer year = taskPackageModel.getYear();
        Integer month = resp.getMonth();
        //获取查询月第一天与最后一天
        LocalDate firstDay = LocalDate.of(year, month, 1);
        LocalDate endDay = firstDay.with(TemporalAdjusters.lastDayOfMonth());

        // 获取string类型起止时间
        LocalDateTime firstDayStart = firstDay.atStartOfDay();
        LocalDateTime endDayEnd = endDay.atTime(23, 59, 59);
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        String firstDayStr = firstDayStart.format(formatter);
        String endDayStr = endDayEnd.format(formatter);

        //获取查询月 收货统计分数
        //收货完成任务金额
        //BigDecimal completeScanScore = receiveMonthDao
        //        .statisticalScanScore(firstDayStr,endDayStr, userId);
        BigDecimal completeScanScore = terminalScanDetailService.getScanDetailAmountListByBrokerIdAndYearMonth(userId, year, month);
        resp.setCompleteScanScore(completeScanScore);
        //拜访完成任务点数
        //todo 月度拜访完成分数，修改掉此处，改成自己的逻辑
        BigDecimal completeVisitScore = visitStatisticsScore(resp, firstDayStr, endDayStr);
        BigDecimal completeMeetingScore = meetingStatisticsScore(resp, firstDayStr, endDayStr);
        resp.setCompleteVisitScore(completeVisitScore.add(completeMeetingScore));
        resp.setCompleteMeetingScore(completeMeetingScore);
        resp.setCompleteVisitAndStrangerScore(completeVisitScore);

        //填充月度任务完成率
        BigDecimal p=new BigDecimal(100);
        BigDecimal a=completeScanScore;
        BigDecimal b=resp.getPlanScanScore();
        BigDecimal scanP=BigDecimal.ZERO;
        if(b.compareTo(BigDecimal.ZERO)!=0){
            BigDecimal planScanScore = b.multiply(new BigDecimal(10000));
            scanP = a.divide(planScanScore, 2, BigDecimal.ROUND_HALF_UP).multiply(p);

        }
        resp.setScanScorePercentage(scanP);
        BigDecimal c=completeVisitScore.add(completeMeetingScore);
        BigDecimal d=resp.getPlanVisitScore();
        BigDecimal visitP=BigDecimal.ZERO;
        if(d.compareTo(BigDecimal.ZERO)!=0){
            visitP = c.divide(d, 2, BigDecimal.ROUND_HALF_UP).multiply(p);
        }
        resp.setVisitScorePercentage(visitP);

        //填充月度完成详情
            //获取当前月度任务完成详情
        List<TerminalTaskMonthlyDetailModel> list =
                taskPackageDao.agent_getCompleteMonthDetail(firstDayStr, endDayStr, userId);

        resp.setCompleteMonthlyDetailModel(list);
        List<TerminalScanAmountDetailResp> completeMonthlyDetailModel = terminalScanDetailService.getScanDetailListByBrokerIdAndYearMonth(userId, year, month);
        resp.setCompleteMonthlyScanDetails(completeMonthlyDetailModel);

        //todo 填充激活终端数量  逻辑待修改
        TerminalTaskActivateStatisticsModel terminalTaskActivateStatisticsModel = terminalShopDao.getActivateStatisticsByTmId(resp.getTmId(), firstDayStr, endDayStr);
        Integer terminalActivateStatis = 0;
        if(Objects.nonNull(terminalTaskActivateStatisticsModel) && Objects.nonNull(terminalTaskActivateStatisticsModel.getTotalNum())){
            terminalActivateStatis = terminalTaskActivateStatisticsModel.getTotalNum();
        }
        resp.setActivateTerminalNum(terminalActivateStatis);

        Integer livelyTerminalCount = taskPackageDao
                .agent_getLivelyTerminalCount(firstDayStr, endDayStr, userId);
        resp.setLivelyTerminalNum(livelyTerminalCount);

        return resp;
    }

    // 只统计任务完成的拜访得分，包括终端拜访和陌生终端拜访
    private BigDecimal visitStatisticsScore(TerminalTaskMonthlyResp resp, String firstDayStr, String endDayStr){
        // 查询本月拜访得分
        TerminalVisitRecordScoreReq terminalVisitRecordScoreReq = new TerminalVisitRecordScoreReq();
        terminalVisitRecordScoreReq.setTmId(resp.getTmId());
        terminalVisitRecordScoreReq.setStartTime(firstDayStr);
        terminalVisitRecordScoreReq.setEndTime(endDayStr);
        TerminalTaskStatisticsResp TerminalTaskStatisticsResp = terminalVisitRecordScoreDao.getMonthVisitScore(terminalVisitRecordScoreReq);
        BigDecimal visitScore = BigDecimal.ZERO;
        if(Objects.nonNull(TerminalTaskStatisticsResp)){
            visitScore = TerminalTaskStatisticsResp.getCompletedScore();
        }

        // 获取陌生终端得分
        StrangeTerminalVisitRecordDto strangeTerminalVisitRecordDto = strangeTerminalVisitRecordService.statisticsStrangeTerminalPointByTmId(resp.getTmId(), firstDayStr, endDayStr);
        BigDecimal strangeVisitScore = BigDecimal.ZERO;
        if(Objects.nonNull(strangeTerminalVisitRecordDto)){
            strangeVisitScore = strangeTerminalVisitRecordDto.getSumPoints();
        }
        return visitScore.add(strangeVisitScore);
    }

    // 统计已完成的会议得分
    private BigDecimal meetingStatisticsScore(TerminalTaskMonthlyResp resp, String firstDayStr, String endDayStr){
        //查询会议得分
        TerminalMeetingRecordScoreReq terminalMeetingRecordScoreReq = new TerminalMeetingRecordScoreReq();
        terminalMeetingRecordScoreReq.setTmId(resp.getTmId());
        TerminalTaskMeetingCompleteScoreResp terminalTaskMeetingCompleteScoreResp = terminalMeetingRecordScoreService.getMonthMeetingScore(terminalMeetingRecordScoreReq);
        BigDecimal meetingScore = BigDecimal.ZERO;
        if(Objects.nonNull(terminalTaskMeetingCompleteScoreResp)){
            meetingScore = terminalTaskMeetingCompleteScoreResp.getMeetingScore();
        }
        return meetingScore;
    }

    /**
     * 填充年度任务
     * @param resp
     * @param userId
     * @return
     */
    @Override
    public TerminalTaskYearlyResp agent_fillYearlyDetail(TerminalTaskYearlyResp resp, Integer userId) {
        //获取主任务id
        Integer id = resp.getTpId();
        //获取年度终端采集完成数量
        List<TerminalTaskYearlyDetailResp> respList = taskPackageDao.agent_getCompleteYearlyDetail(id, userId);
        Integer num=0;
        if(respList!=null){
            num=respList.size();
            resp.setDetailRespList(respList);
        }
        //已建终端数量
        resp.setEstablishedTerminal(num);
        //年总终端
        Integer totalTerminal = resp.getTotalTerminal();
        //已分配终端
        Integer allocatedTerminalNum = resp.getAllocatedTerminalNum();
        //总待建终端数量
        int total = totalTerminal - allocatedTerminalNum;
        resp.setTotalUnEstablishedTerminal(total);

        //未建终端数量
        int i = totalTerminal - allocatedTerminalNum - num;
        resp.setUnEstablishedTerminal(i);

        //新建终端百分比
        BigDecimal p=new BigDecimal(100);
        BigDecimal a=new BigDecimal(num);
        BigDecimal b=new BigDecimal(total);
        int result=0;
        if(b.compareTo(BigDecimal.ZERO)!=0){
            result = a.divide(b, 2, BigDecimal.ROUND_HALF_UP).multiply(p).intValue();
        }
        resp.setEstablishedTerminalPercentage(result);

        return resp;
    }

    /**
     * 领取任务
     * @param taskId
     * @param userId
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void claimTask(Integer taskId, Integer userId) {
        try {
            //获取锁
            claimTaskLock.tryLock();
            //查询此人是否有已领取的任务，已有则不能再领取
            LambdaQueryWrapper<TerminalTaskReceiveModel> queryWrapper3=new LambdaQueryWrapper<>();
            queryWrapper3.eq(TerminalTaskReceiveModel::getBrokerId,userId)
                    .eq(TerminalTaskReceiveModel::getDeleteFlag,0)
                    .eq(TerminalTaskReceiveModel::getStatus, TerminalTaskPackageStatusFlagEnum.VALID.getKey());
            TerminalTaskReceiveModel model4 = receiveDao.selectOne(queryWrapper3);
            if(model4!=null){
                throw new BusinessException("业务异常:有在执行中的任务，不可再领取任务");
            }
            //查询当前任务包的状态，确保此任务包是未被领取状态
            TerminalTaskPackageModel taskPackageModel = taskPackageDao.selectById(taskId);
            Integer deleteFlag = taskPackageModel.getDeleteFlag();
            if(deleteFlag==1){
                throw new BusinessException("业务异常:此任务不可用");
            }
            Integer receiveStatus = taskPackageModel.getReceiveStatus();
            if(receiveStatus==1){
                throw new BusinessException("业务异常:此任务已被领取,不可再领取");
            }

            //查询此任务包是否已被绑定
            LambdaQueryWrapper<TerminalTaskReceiveModel> queryWrapper1=new LambdaQueryWrapper<>();
            queryWrapper1.eq(TerminalTaskReceiveModel::getDeleteFlag,0)
                    .eq(TerminalTaskReceiveModel::getTpId,taskId);
            TerminalTaskReceiveModel model2 = receiveDao.selectOne(queryWrapper1);
            if(model2!=null){
                throw new BusinessException("业务异常:此任务不可领取");
            }
            //查询此人之前是否有过领取记录，有过领取记录，则恢复原领取数据
            LambdaQueryWrapper<TerminalTaskReceiveModel> queryWrapper2=new LambdaQueryWrapper<>();
            queryWrapper2.eq(TerminalTaskReceiveModel::getTpId,taskId)
                    .eq(TerminalTaskReceiveModel::getBrokerId,userId)
                    .eq(TerminalTaskReceiveModel::getDeleteFlag,1);
            TerminalTaskReceiveModel model3 = receiveDao.selectOne(queryWrapper2);
            if(model3!=null){
                //有过领取记录,恢复原领取数据

                //绑定业务代表与任务包关系
                Integer id = model3.getId();
                LambdaUpdateWrapper<TerminalTaskReceiveModel> updateWrapper=new LambdaUpdateWrapper<>();
                updateWrapper.eq(TerminalTaskReceiveModel::getId,id)
                        .set(TerminalTaskReceiveModel::getDeleteFlag,0);
                receiveDao.update(null,updateWrapper);

                //恢复生成月度任务与被领取人之间的关系
                receiveMonthDao.updateByTrId(id);
            }else{
                //领取人没有过领取记录则执行下面逻辑
                //绑定业务代表与任务包关系
                TerminalTaskReceiveModel model=new TerminalTaskReceiveModel();
                model.setTpId(taskId);
                model.setBrokerId(userId);
                model.setCreateBy(userId);
                receiveDao.insert(model);
                Integer trId = model.getId();
                //生成月度任务与被领取人之间的关系
                LambdaQueryWrapper<TerminalTaskMonthlyModel> queryWrapper=new LambdaQueryWrapper<>();
                queryWrapper.eq(TerminalTaskMonthlyModel::getTpId,taskId);
                List<TerminalTaskMonthlyModel> monthlyModels = monthlyDao.selectList(queryWrapper);
                List<TerminalTaskReceiveMonthModel> list=new ArrayList<>();
                if(monthlyModels==null||monthlyModels.size()==0){
                    throw new BusinessException("业务异常:此任务未设置月度任务，不可领取");
                }
                for (int i = 0; i < monthlyModels.size(); i++) {
                    TerminalTaskMonthlyModel monthlyModel = monthlyModels.get(i);
                    TerminalTaskReceiveMonthModel model1=new TerminalTaskReceiveMonthModel();
                    model1.setTmId(monthlyModel.getId());
                    model1.setTrId(trId);
                    list.add(model1);
                }
                receiveMonthDao.insertBatch(list);
            }
            //业代领取任务包后，增加与发布任务包的客户经理进行关联的业务逻辑

            //获取创建此任务的客户经理id
            Integer createBy = taskPackageModel.getCreateBy();
            //绑定业代与客户经理关系
            LambdaUpdateWrapper<TerminalAccountManagerModel> updateWrapper=new LambdaUpdateWrapper<>();
            updateWrapper.eq(TerminalAccountManagerModel::getId,userId)
                    .set(TerminalAccountManagerModel::getParentId,createBy);
            terminalAccountManagerDao.update(null,updateWrapper);

            //主任务变为已领取
            TerminalTaskPackageModel packageModel=new TerminalTaskPackageModel();
            packageModel.setId(taskId);
            packageModel.setReceiveStatus(1);
            taskPackageDao.updateById(packageModel);
            //同步采集人领取任务前采集的未分配任务终端
            addUnDistributedTerminal(taskId,userId);

        }catch (Exception e){
            log.error("领取任务异常:"+e);
            throw e;
        }finally {
//            释放锁
            claimTaskLock.unlock();
        }

    }






    /**
    *   同步采集人领取任务前采集的未分配任务终端
     */
    public void addUnDistributedTerminal(Integer taskId, Integer userId){
        // 查询当前登录人采集终端
        LambdaUpdateWrapper<TerminalShopModel> updateWrapper1=new LambdaUpdateWrapper<>();
        updateWrapper1.eq(TerminalShopModel::getCreateUser,userId)
                .eq(TerminalShopModel::getStatus, 1)
                .eq(TerminalShopModel::getIsDelete, 0);
        List<Integer> memberShopId = terminalShopDao.selectList(updateWrapper1).stream().map(TerminalShopModel::getMemberShopId).collect(Collectors.toList());
        if(memberShopId != null && memberShopId.size() > 0){
//            查询已分配任务包的终端MS_ID
            LambdaQueryWrapper<TerminalTaskYearlyDetailModel> queryWrapper2=new LambdaQueryWrapper<>();
            queryWrapper2.eq(TerminalTaskYearlyDetailModel::getDeleteFlag,0);

            List<Integer> yearlyDetailMsId = yearlyDetailDao.selectList(queryWrapper2).stream().map(TerminalTaskYearlyDetailModel::getMsId).collect(Collectors.toList());
            // 查询当前登录人采集终端memberShopId中，不包含在yearlyDetailMsId的待添加memberShopId
            List<Integer> addMemberShopId = memberShopId.stream().filter(item -> !yearlyDetailMsId.contains(item)).collect(Collectors.toList());
            if(addMemberShopId != null && addMemberShopId.size() > 0){
                //  获取t_terminal_task_yearly表中tp_id= taskId的id值
                LambdaQueryWrapper<TerminalTaskYearlyModel> queryWrapper3=new LambdaQueryWrapper<>();
                queryWrapper3.eq(TerminalTaskYearlyModel::getDeleteFlag,0)
                        .eq(TerminalTaskYearlyModel::getTpId,taskId);
                List<Integer> taskYearlyId = yearlyDao.selectList(queryWrapper3).stream().map(TerminalTaskYearlyModel::getId).collect(Collectors.toList());
                Integer tyId = null;
                if(taskYearlyId != null && taskYearlyId.size() > 0){
                    tyId = taskYearlyId.get(0);
                    for (int i = 0; i < addMemberShopId.size(); i++) {
                        TerminalTaskYearlyDetailModel model=new TerminalTaskYearlyDetailModel();
                        model.setMsId(addMemberShopId.get(i));
                        model.setTyId(tyId);
                        model.setCreateBy(userId);
                        model.setTerminalType(TerminalYearlyDetailTypeEnum.COLLECTION.getKey());
                        model.setDeleteFlag(0);
                        yearlyDetailDao.insert(model);
                    }
                }
            }
        }
    }
    /**
     * 撤销任务
     * @param taskId
     * @param userId
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void abortTask(Integer taskId, Integer userId) {

        //查询当前任务领取人是否是当前操作人，不一致抛出异常
        LambdaQueryWrapper<TerminalTaskReceiveModel> queryWrapper=new LambdaQueryWrapper<>();
        queryWrapper.eq(TerminalTaskReceiveModel::getDeleteFlag,0)
                .eq(TerminalTaskReceiveModel::getTpId,taskId)
                .eq(TerminalTaskReceiveModel::getBrokerId,userId);
        TerminalTaskReceiveModel model = receiveDao.selectOne(queryWrapper);
        if(model==null){
            throw new BusinessException("业务异常:此任务不可撤销");
        }
        //解绑业务代表与任务包的关系,deleteFlag置为1
        LambdaUpdateWrapper<TerminalTaskReceiveModel> updateWrapper=new LambdaUpdateWrapper<>();
        updateWrapper.eq(TerminalTaskReceiveModel::getId,model.getId())
                .set(TerminalTaskReceiveModel::getDeleteFlag,1);
        receiveDao.update(null,updateWrapper);
        //解绑月度任务与被领取人之间的关系,deleteFlag置为1
        LambdaUpdateWrapper<TerminalTaskReceiveMonthModel> updateWrapper1=new LambdaUpdateWrapper<>();
        updateWrapper1.eq(TerminalTaskReceiveMonthModel::getTrId,model.getId())
                .eq(TerminalTaskReceiveMonthModel::getDeleteFlag,0)
                .set(TerminalTaskReceiveMonthModel::getDeleteFlag,1);
        receiveMonthDao.update(null,updateWrapper1);

        //业代撤销任务包后，撤销与发布任务包的客户经理进行关联的业务逻辑

        //撤销业代与客户经理关系
//        LambdaUpdateWrapper<TerminalAccountManagerModel> updateWrapper2=new LambdaUpdateWrapper<>();
//        updateWrapper2.eq(TerminalAccountManagerModel::getId,userId)
//                .set(TerminalAccountManagerModel::getParentId,null);
//        terminalAccountManagerDao.update(null,updateWrapper2);

        // 将任务包中的采集终端修改为分配终端
        List<TerminalTaskYearlyDetailModel> yearlyDetailModel= yearlyDetailDao.getTaskTerminalListByTpId(taskId);
        if(!Objects.isNull(yearlyDetailModel)) {
            // 根据yearlyDetailModel中的id批量更新每一条terminal_type为1的数据修改为0
            List<Integer> ids = yearlyDetailModel.stream().filter(item -> item.getTerminalType() == 1).map(TerminalTaskYearlyDetailModel::getId).collect(Collectors.toList());
            LambdaUpdateWrapper<TerminalTaskYearlyDetailModel> TerminalTaskYearlyDetailUpdateWrapper = new LambdaUpdateWrapper<>();
            if(ids.size() > 0) {
                TerminalTaskYearlyDetailUpdateWrapper.in(TerminalTaskYearlyDetailModel::getId, ids)
                        .set(TerminalTaskYearlyDetailModel::getTerminalType, 0);
                yearlyDetailDao.update(null, TerminalTaskYearlyDetailUpdateWrapper);
            }
        }
        // 根据t_terminal_task_yearly表中的tp_id=taskId的id值
        LambdaQueryWrapper<TerminalTaskYearlyModel> queryWrapper3=new LambdaQueryWrapper<>();
        queryWrapper3.eq(TerminalTaskYearlyModel::getDeleteFlag,0)
                .eq(TerminalTaskYearlyModel::getTpId,taskId);
        List<Integer> taskYearlyId = yearlyDao.selectList(queryWrapper3).stream().map(TerminalTaskYearlyModel::getId).collect(Collectors.toList());
        // 根据id更新t_terminal_task_yearly表中的已分配终端数
        LambdaUpdateWrapper<TerminalTaskYearlyModel> TerminalTaskYearlyUpdateWrapper = new LambdaUpdateWrapper<>();
        TerminalTaskYearlyUpdateWrapper.in(TerminalTaskYearlyModel::getId, taskYearlyId)
                .set(TerminalTaskYearlyModel::getAllocatedTerminalNum, yearlyDetailModel.size());
        yearlyDao.update(null, TerminalTaskYearlyUpdateWrapper);

        //主任务变为未领取
        TerminalTaskPackageModel packageModel=new TerminalTaskPackageModel();
        packageModel.setId(taskId);
        packageModel.setReceiveStatus(0);
        taskPackageDao.updateById(packageModel);
    }

    /**
     * 获取当前登录得业务代表领取得全部任务
     * @param userId
     * @return
     */
    @Override
    public List<TerminalTaskPackageModel> agent_getReceivedList(Integer userId) {
        List<TerminalTaskPackageModel> list=new ArrayList<>();
        //获取当前登陆人所有领取得任务集合
        LambdaQueryWrapper<TerminalTaskReceiveModel> queryWrapper=new LambdaQueryWrapper<>();
        queryWrapper.eq(TerminalTaskReceiveModel::getBrokerId,userId)
                .eq(TerminalTaskReceiveModel::getDeleteFlag,0);
        List<TerminalTaskReceiveModel> receiveModels = receiveDao.selectList(queryWrapper);
        if(receiveModels!=null&&receiveModels.size()>0){
            //获取主任务id集合
            List<Integer> collect = receiveModels.stream()
                    .map(TerminalTaskReceiveModel::getTpId).collect(Collectors.toList());
            LambdaQueryWrapper<TerminalTaskPackageModel> queryWrapper1=new LambdaQueryWrapper<>();
            queryWrapper1.eq(TerminalTaskPackageModel::getDeleteFlag,0)
                    .in(TerminalTaskPackageModel::getId,collect);
            list= taskPackageDao.selectList(queryWrapper1);
        }
        return list;
    }

    /**
     * 删除未领取的主任务
     * 已领取或者有过领取记录的不能删除
     * 并且删除任务下分配终端
     * @param id
     * @param userId
     * @return
     */
    @Override
    @Transactional
    public void removeTask(Integer id, Integer userId) {
        //检查任务是否已领取或有过领取记录，已领取不能删除
        LambdaQueryWrapper<TerminalTaskReceiveModel> queryWrapper=new LambdaQueryWrapper<>();
        queryWrapper.eq(TerminalTaskReceiveModel::getTpId,id);
        TerminalTaskReceiveModel receiveModel = receiveDao.selectOne(queryWrapper);
        if(receiveModel!=null){
            //任务已领取或有过领取记录
            throw new BusinessException("任务已领取或有过领取记录,不可删除");
        }

        //1.删除主任务
        LambdaUpdateWrapper<TerminalTaskPackageModel> updateWrapper1=new LambdaUpdateWrapper<>();
        updateWrapper1.eq(TerminalTaskPackageModel::getId,id)
                .set(TerminalTaskPackageModel::getDeleteFlag,1);
        taskPackageDao.update(null,updateWrapper1);

        //2.删除任务对应区域
        LambdaUpdateWrapper<TerminalTaskPackageRegionModel> updateWrapper2=new LambdaUpdateWrapper<>();
        updateWrapper2.eq(TerminalTaskPackageRegionModel::getTpId,id)
                .set(TerminalTaskPackageRegionModel::getDeleteFlag,1);
        taskPackageRegionDao.update(null,updateWrapper2);

        //3.获取年度任务主键，用于删除年度任务详情(分配终端)
        LambdaQueryWrapper<TerminalTaskYearlyModel> queryWrapper1=new LambdaQueryWrapper<>();
        queryWrapper1.eq(TerminalTaskYearlyModel::getTpId,id);

        TerminalTaskYearlyModel model = yearlyDao.selectOne(queryWrapper1);
        if(model!=null){
            Integer tyId=model.getId();
            //3.1 删除分配终端
            LambdaUpdateWrapper<TerminalTaskYearlyDetailModel> updateWrapper6=new LambdaUpdateWrapper<>();
            updateWrapper6.eq(TerminalTaskYearlyDetailModel::getTyId,tyId)
                    .set(TerminalTaskYearlyDetailModel::getDeleteFlag,1);
            yearlyDetailDao.update(null,updateWrapper6);
        }

        //4.删除年度任务
        LambdaUpdateWrapper<TerminalTaskYearlyModel> updateWrapper3=new LambdaUpdateWrapper<>();
        updateWrapper3.eq(TerminalTaskYearlyModel::getTpId,id)
                .set(TerminalTaskYearlyModel::getDeleteFlag,1);
        yearlyDao.update(null,updateWrapper3);

        //5.获取月度任务主键，用于删除月度任务详情
        LambdaQueryWrapper<TerminalTaskMonthlyModel> queryWrapper2=new LambdaQueryWrapper<>();
        queryWrapper2.eq(TerminalTaskMonthlyModel::getTpId,id);
        List<TerminalTaskMonthlyModel> monthlyModels = monthlyDao.selectList(queryWrapper2);
        if(monthlyModels!=null&&monthlyModels.size()>0){
            List<Integer> collect = monthlyModels.stream()
                    .map(TerminalTaskMonthlyModel::getId).collect(Collectors.toList());

            //5.1 删除月度详情
            LambdaUpdateWrapper<TerminalTaskMonthlyDetailModel> updateWrapper5=new LambdaUpdateWrapper<>();
            updateWrapper5.in(TerminalTaskMonthlyDetailModel::getTmId,collect)
                    .set(TerminalTaskMonthlyDetailModel::getDeleteFlag,1);
            monthlyDetailDao.update(null,updateWrapper5);
            // 5.2 删除月度分组详情设置
            LambdaUpdateWrapper<TerminalTaskMonthlyGroupDetailModel> updateWrapper6=new LambdaUpdateWrapper<>();
            updateWrapper6.in(TerminalTaskMonthlyGroupDetailModel::getTaskMonthId,collect)
                    .set(TerminalTaskMonthlyGroupDetailModel::getIsDelete, DeleteFlagEnum.DELETED.getKey());
            monthlyGroupDetailDao.update(null,updateWrapper6);
        }

        //6.删除月度任务
        LambdaUpdateWrapper<TerminalTaskMonthlyModel> updateWrapper4=new LambdaUpdateWrapper<>();
        updateWrapper4.eq(TerminalTaskMonthlyModel::getTpId,id)
                .set(TerminalTaskMonthlyModel::getDeleteFlag,1);
        monthlyDao.update(null,updateWrapper4);
    }

    /**
     * 业代小程序
     * 获取当前登陆人终端列表
     * 此登陆人必须已领取任务
     * @param req
     * @return
     */
    @Override
    public List<TerminalVisitShopAppletResp> agent_getShopListByPage(TerminalVisitShopAppletResp req) {
        List<TerminalVisitShopAppletResp> list =
                taskPackageDao.agent_getShopListByPage(SearchUtil.getSearch(req));
        return list;
    }

    /**
     * 查询全部任务列表
     */
    @Override
    public List<TerminalTaskPackageResp> selectAllTerminalTaskPackageList(Integer page, Integer limit, TerminalTaskPackageReq terminalTaskPackageReq) {

        //分页不为空先分页
        if(null != page && null != limit){
            PageHelper.startPage(page,limit);
        }
        if(!StringUtils.isEmpty(terminalTaskPackageReq.getDistrict())){
            List<Integer> districtIds = Arrays.stream(terminalTaskPackageReq.getDistrict().split(",")).map(Integer::parseInt).collect(Collectors.toList());
            terminalTaskPackageReq.setDistrictIds(districtIds);
        }
        //查询任务列表
        List<TerminalTaskPackageResp> selectDataList = taskPackageDao.selectAllTerminalTaskPackageList(terminalTaskPackageReq);
        //返回
        return selectDataList;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void changeManager(TerminalTaskPackageChangeManagerReq terminalTaskPackageChangeManagerReq){
        // 根据tpId查询任务包，若不存在或者delete_flag != 0则抛出异常
        TerminalTaskPackageModel taskPackageModel = taskPackageDao.selectById(terminalTaskPackageChangeManagerReq.getTpId());
        if(Objects.isNull(taskPackageModel) || taskPackageModel.getDeleteFlag() != DeleteFlagEnum.NOT_DELETE.getKey()){
            throw new BusinessException("任务包不存在或已删除");
        }
        // 根据managerId查询客户经理，若不存在或者delete_flag != 0则抛出异常
        TerminalAccountManagerModel terminalAccountManagerModel = terminalAccountManagerDao.selectById(terminalTaskPackageChangeManagerReq.getManagerId());
        if(Objects.isNull(terminalAccountManagerModel) || terminalAccountManagerModel.getIsDelete() != DeleteFlagEnum.NOT_DELETE.getKey()){
            throw new BusinessException("客户经理不存在或已删除");
        }
        // 变更任务包的客户经理（创建人）
        TerminalTaskPackageModel terminalTaskPackageModel = new TerminalTaskPackageModel();
        terminalTaskPackageModel.setId(terminalTaskPackageChangeManagerReq.getTpId());
        terminalTaskPackageModel.setCreateBy(terminalTaskPackageChangeManagerReq.getManagerId());
        taskPackageDao.updateById(terminalTaskPackageModel);

        // 变更任务包的客户经理（创建人）下的所有任务的客户经理（创建人）
        LambdaQueryWrapper<TerminalTaskReceiveModel> queryWrapper=new LambdaQueryWrapper<>();
        queryWrapper.eq(TerminalTaskReceiveModel::getTpId, terminalTaskPackageChangeManagerReq.getTpId())
                .eq(TerminalTaskReceiveModel::getDeleteFlag, DeleteFlagEnum.NOT_DELETE.getKey());
        TerminalTaskReceiveModel receiveModel = receiveDao.selectOne(queryWrapper);

        if(Objects.nonNull(receiveModel)){
            LambdaUpdateWrapper<TerminalAccountManagerModel> updateWrapper=new LambdaUpdateWrapper<>();
            updateWrapper.eq(TerminalAccountManagerModel::getId, receiveModel.getBrokerId())
                    .eq(TerminalAccountManagerModel::getIsDelete, DeleteFlagEnum.NOT_DELETE.getKey())
                    .set(TerminalAccountManagerModel::getParentId, terminalTaskPackageChangeManagerReq.getManagerId());
            terminalAccountManagerDao.update(null, updateWrapper);
        }
    };

    @Override
    public TerminalTaskMonthlyResp getManagerMonthlyStatistics(Integer userId, Integer year, Integer month){
        TerminalTaskMonthlyResp terminalTaskMonthlyTotal = new TerminalTaskMonthlyResp();
        //获取当前登陆人创建的任务
        LambdaQueryWrapper<TerminalTaskPackageModel> queryWrapper=new LambdaQueryWrapper<>();
        queryWrapper.eq(TerminalTaskPackageModel::getCreateBy,userId)
                .eq(TerminalTaskPackageModel::getYear,year)
                .eq(TerminalTaskPackageModel::getConfirmStatus,1)
                .eq(TerminalTaskPackageModel::getDeleteFlag,0);
        List<TerminalTaskPackageModel> list = taskPackageDao.selectList(queryWrapper);
        // 判断list是否为空，不为空则遍历list，获取任务包id，根据任务包id获取月度任务详情
        if(!Objects.isNull(list) && list.size() > 0){
            List<TerminalTaskMonthlyResp> terminalTaskMonthlyTotalList = taskMonthlyStatistics(list, month);
            terminalTaskMonthlyTotal = calculateTotal(terminalTaskMonthlyTotalList);
        }
        return terminalTaskMonthlyTotal;
    };

    @Override
    public PageInfo<TerminalTaskMonthlyResp> getManagerMonthlyScanList(TerminalTaskByManagerReq req){
        List<TerminalTaskMonthlyResp> terminalTaskMonthlyRespList = new ArrayList<>();
        // 查询当前登录人创建的任务
        PageHelper.startPage(req.getPageNum(), req.getPageSize());
        LambdaQueryWrapper<TerminalTaskPackageModel> queryWrapper=new LambdaQueryWrapper<>();
        queryWrapper.eq(TerminalTaskPackageModel::getCreateBy,req.getManagerId())
                .eq(TerminalTaskPackageModel::getYear,req.getYear())
                .eq(TerminalTaskPackageModel::getConfirmStatus,1)
                .eq(TerminalTaskPackageModel::getDeleteFlag,DeleteFlagEnum.NOT_DELETE.getKey())
                .like(Objects.nonNull(req.getTaskNum()), TerminalTaskPackageModel::getTaskNum, req.getTaskNum())
                .orderByDesc(TerminalTaskPackageModel::getCreateTime);
        List<TerminalTaskPackageModel> terminalTaskPackagelist = taskPackageDao.selectList(queryWrapper);
        // 判断list是否为空，不为空则遍历list，获取月度任务详情
        if(!Objects.isNull(terminalTaskPackagelist) && terminalTaskPackagelist.size() > 0){
             terminalTaskMonthlyRespList = taskMonthlyStatistics(terminalTaskPackagelist, req.getMonth());
        }
        PageInfo<TerminalTaskMonthlyResp> pageInfo = new PageInfo<>();
        pageInfo.setPageNum(((Page) terminalTaskPackagelist).getPageNum());
        pageInfo.setPageSize(((Page) terminalTaskPackagelist).getPageSize());
        pageInfo.setTotal(((Page) terminalTaskPackagelist).getTotal());
        pageInfo.setPages(((Page) terminalTaskPackagelist).getPages());
        pageInfo.setList(terminalTaskMonthlyRespList);
        return pageInfo;
    };

    @Override
    public TerminalTaskMonthlyResp getManagerMonthlyVisitStatistics(TerminalTaskByManagerReq req){
        TerminalTaskMonthlyResp terminalTaskMonthlyResp = new TerminalTaskMonthlyResp();
        // 查询当前登录人创建的任务
        LambdaQueryWrapper<TerminalTaskPackageModel> queryWrapper=new LambdaQueryWrapper<>();
        queryWrapper.eq(TerminalTaskPackageModel::getCreateBy,req.getManagerId())
                .eq(TerminalTaskPackageModel::getYear,req.getYear())
                .eq(TerminalTaskPackageModel::getConfirmStatus,1)
                .eq(TerminalTaskPackageModel::getDeleteFlag,DeleteFlagEnum.NOT_DELETE.getKey())
                .like(Objects.nonNull(req.getTaskNum()), TerminalTaskPackageModel::getTaskNum, req.getTaskNum());
        List<TerminalTaskPackageModel> terminalTaskPackagelist = taskPackageDao.selectList(queryWrapper);
        // 判断list是否为空，不为空则遍历list，获取月度任务详情
        if(!Objects.isNull(terminalTaskPackagelist) && terminalTaskPackagelist.size() > 0){
            List<TerminalTaskMonthlyResp> terminalTaskMonthlyRespList = taskMonthlyStatistics(terminalTaskPackagelist, req.getMonth());
            terminalTaskMonthlyResp = calculateTotal(terminalTaskMonthlyRespList);
        }
        return terminalTaskMonthlyResp;
    };

    /**
     * 获取客户经理的任务拜访列表
     */
    @Override
    public PageInfo<TerminalTaskMonthlyResp> getManagerMonthlyVisitList(TerminalTaskByManagerReq req){
        List<TerminalTaskMonthlyResp> terminalTaskMonthlyRespList = new ArrayList<>();
        // 查询当前登录人创建的任务
        PageHelper.startPage(req.getPageNum(), req.getPageSize());
        LambdaQueryWrapper<TerminalTaskPackageModel> queryWrapper=new LambdaQueryWrapper<>();
        queryWrapper.eq(TerminalTaskPackageModel::getCreateBy,req.getManagerId())
                .eq(TerminalTaskPackageModel::getYear,req.getYear())
                .eq(TerminalTaskPackageModel::getConfirmStatus,1)
                .eq(TerminalTaskPackageModel::getDeleteFlag,DeleteFlagEnum.NOT_DELETE.getKey())
                .like(Objects.nonNull(req.getTaskNum()), TerminalTaskPackageModel::getTaskNum, req.getTaskNum())
                .orderByDesc(TerminalTaskPackageModel::getCreateTime);
        List<TerminalTaskPackageModel> terminalTaskPackagelist = taskPackageDao.selectList(queryWrapper);
        // 判断list是否为空，不为空则遍历list，获取月度任务详情
        if(!Objects.isNull(terminalTaskPackagelist) && terminalTaskPackagelist.size() > 0){
            terminalTaskMonthlyRespList = taskMonthlyStatistics(terminalTaskPackagelist, req.getMonth());
        }
        PageInfo pageInfo = new PageInfo<>();
        pageInfo.setPageNum(((Page) terminalTaskPackagelist).getPageNum());
        pageInfo.setPageSize(((Page) terminalTaskPackagelist).getPageSize());
        pageInfo.setTotal(((Page) terminalTaskPackagelist).getTotal());
        pageInfo.setPages(((Page) terminalTaskPackagelist).getPages());
        pageInfo.setList(terminalTaskMonthlyRespList);
        return pageInfo;
    };

    @Override
    public Integer getNewShopCountByStaffIdAndYearAndMonth(Integer brokerId, Integer year, Integer month){
        Integer total = 0;
        // 获取当前业代在时间范围内第一次审核通过的终端
        List<TerminalShopModel> terminalShopModelList = taskPackageDao.getNewShopCountByStaffIdAndYearAndMonth(brokerId, year, month);
        if(Objects.nonNull(terminalShopModelList) && terminalShopModelList.size() > 0){
            // 获取terminalShopModelList中的member_shop_id列表，以便下面获取收货列表的in方法使用
            List<Integer> shopIds = terminalShopModelList.stream().map(TerminalShopModel::getMemberShopId).collect(Collectors.toList());
            // 获取当前时间范围内完成收货的终端个数
            List<TerminalScanDetailModel> terminalScanDetailModelList = terminalScanDetailDao.selectList(new LambdaQueryWrapper<TerminalScanDetailModel>()
                    .eq(TerminalScanDetailModel::getIsDelete, DeleteFlagEnum.NOT_DELETE.getKey())
                    .in(TerminalScanDetailModel::getShopId, shopIds)
                    .apply("DATE_FORMAT(create_time, '%Y-%m') = {0}", YearMonth.of(year, month))
            );
            if(Objects.nonNull(terminalScanDetailModelList) && terminalScanDetailModelList.size() > 0){
                // 获取terminalScanDetailModelList中不同的shop_id次数，即为新终端个数
                List<Integer> newShopCountList = terminalScanDetailModelList.stream().map(TerminalScanDetailModel::getShopId).distinct().collect(Collectors.toList());
                total = newShopCountList.size();
            }
        }
        return total;
    }

    // 根据任务包id获取月度任务详情
    private List<TerminalTaskMonthlyResp> taskMonthlyStatistics(List<TerminalTaskPackageModel> list, Integer month){
        List<TerminalTaskMonthlyResp> terminalTaskMonthlyTotalList = new ArrayList<>();
        for (TerminalTaskPackageModel terminalTaskPackageModel : list) {
            Integer taskId = terminalTaskPackageModel.getId();
            TerminalTaskMonthlyResp terminalTaskMonthlyResp = new TerminalTaskMonthlyResp();
            // 根据任务包id获取月度任务详情
            terminalTaskMonthlyResp = agent_getMonthDetail(taskId, month);
            // 根据任务包id获取任务领取人信息
            LambdaQueryWrapper<TerminalTaskReceiveModel> queryWrapper=new LambdaQueryWrapper<>();
            queryWrapper.eq(TerminalTaskReceiveModel::getTpId,taskId)
                    .eq(TerminalTaskReceiveModel::getDeleteFlag,0);
            TerminalTaskReceiveModel receiveModel = receiveDao.selectOne(queryWrapper);
            if(!Objects.isNull(terminalTaskMonthlyResp) && !Objects.isNull(receiveModel)){
                //填充月度任务完成率与完成详情
                terminalTaskMonthlyResp = manage_fillMonthDetail(terminalTaskMonthlyResp, receiveModel.getBrokerId());
            }
            terminalTaskMonthlyTotalList.add(terminalTaskMonthlyResp);
        }
        return terminalTaskMonthlyTotalList;
    };

    /**
     * 填充月度任务完成率与完成详情
     * @param resp
     * @param userId
     * @return
     */
    private TerminalTaskMonthlyResp manage_fillMonthDetail(TerminalTaskMonthlyResp resp,Integer userId) {
        //根据当前登陆人和主任务id、月度任务id,获取当前月度任务完成率
        //主任务id
        Integer id = resp.getId();
        //获取月份与年
        TerminalTaskPackageModel taskPackageModel = taskPackageDao.selectById(id);
        Integer year = taskPackageModel.getYear();
        Integer month = resp.getMonth();
        //获取查询月第一天与最后一天
        LocalDate firstDay = LocalDate.of(year, month, 1);
        LocalDate endDay = firstDay.with(TemporalAdjusters.lastDayOfMonth());

        // 获取string类型起止时间
        LocalDateTime firstDayStart = firstDay.atStartOfDay();
        LocalDateTime endDayEnd = endDay.atTime(23, 59, 59);
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        String firstDayStr = firstDayStart.format(formatter);
        String endDayStr = endDayEnd.format(formatter);

        //获取查询月 收货统计分数
        //收货完成任务点数
        //BigDecimal completeScanScore = receiveMonthDao
        //        .statisticalScanScore(firstDayStr,endDayStr, userId);
        BigDecimal completeScanScore = terminalScanDetailService.getScanDetailAmountListByBrokerIdAndYearMonth(userId, year, month);
        resp.setCompleteScanScore(completeScanScore);
        //拜访完成任务点数
        //todo 月度拜访完成分数，修改掉此处，改成自己的逻辑
        BigDecimal completeVisitScore = visitStatisticsScore(resp, firstDayStr, endDayStr);
        BigDecimal completeMeetingScore = meetingStatisticsScore(resp, firstDayStr, endDayStr);
        resp.setCompleteVisitScore(completeVisitScore.add(completeMeetingScore));
        resp.setCompleteMeetingScore(completeMeetingScore);
        resp.setCompleteVisitAndStrangerScore(completeVisitScore);

        //填充月度任务完成率
        BigDecimal p=new BigDecimal(100);
        BigDecimal a=completeScanScore;
        BigDecimal b=resp.getPlanScanScore();
        BigDecimal scanP=BigDecimal.ZERO;
        if(b.compareTo(BigDecimal.ZERO)!=0){
            BigDecimal planScanScore = b.multiply(new BigDecimal(10000));
            scanP = a.divide(planScanScore, 2, BigDecimal.ROUND_HALF_UP).multiply(p);

        }
        resp.setScanScorePercentage(scanP);
        BigDecimal c=completeVisitScore.add(completeMeetingScore);
        BigDecimal d=resp.getPlanVisitScore();
        BigDecimal visitP=BigDecimal.ZERO;
        if(d.compareTo(BigDecimal.ZERO)!=0){
            visitP = c.divide(d, 2, BigDecimal.ROUND_HALF_UP).multiply(p);
        }
        resp.setVisitScorePercentage(visitP);

        //填充月度完成详情
        //获取当前月度任务完成详情
        List<TerminalTaskMonthlyDetailModel> list =
                taskPackageDao.agent_getCompleteMonthDetail(firstDayStr, endDayStr, userId);

        resp.setCompleteMonthlyDetailModel(list);

        //填充已收货金额详情
        List<TerminalScanAmountDetailResp> completeMonthlyScanDetails = terminalScanDetailService.getScanDetailListByBrokerIdAndYearMonth(userId, year, month);
        resp.setCompleteMonthlyScanDetails(completeMonthlyScanDetails);

        //todo 填充激活终端数量  逻辑待修改
        //List<TerminalShopModel> activateTerminalShopModelList = terminalShopDao.getActivateListByTmId(resp.getTmId(), firstDayStr, endDayStr);
        //if(Objects.nonNull(activateTerminalShopModelList) && activateTerminalShopModelList.size() > 0){
        //    resp.setActivateTerminalList(activateTerminalShopModelList);
        //}
        TerminalTaskActivateStatisticsModel terminalTaskActivateStatisticsModel = terminalShopDao.getActivateStatisticsByTmId(resp.getTmId(), firstDayStr, endDayStr);
        Integer terminalActivateStatis = 0;
        if(Objects.nonNull(terminalTaskActivateStatisticsModel) && Objects.nonNull(terminalTaskActivateStatisticsModel.getTotalNum())){
            terminalActivateStatis = terminalTaskActivateStatisticsModel.getTotalNum();
        }
        resp.setActivateTerminalNum(terminalActivateStatis);

        List<TerminalShopModel> livelyTerminalList = taskPackageDao.getLivelyTerminalList(firstDayStr, endDayStr, userId);
        if(Objects.nonNull(livelyTerminalList) && livelyTerminalList.size() > 0){
            resp.setLivelyTerminalList(livelyTerminalList);
        }

        return resp;
    }

    // 计算总的完成数据
    private TerminalTaskMonthlyResp calculateTotal(List<TerminalTaskMonthlyResp> terminalTaskMonthlyTotalList){
        TerminalTaskMonthlyResp terminalTaskMonthlyTotal = new TerminalTaskMonthlyResp();
        terminalTaskMonthlyTotal.setPlanScanScore(BigDecimal.ZERO);
        terminalTaskMonthlyTotal.setPlanVisitScore(BigDecimal.ZERO);
        terminalTaskMonthlyTotal.setCompleteScanScore(BigDecimal.ZERO);
        terminalTaskMonthlyTotal.setCompleteVisitScore(BigDecimal.ZERO);
        terminalTaskMonthlyTotal.setPlanActivateTerminalNum(0);
        terminalTaskMonthlyTotal.setPlanLivelyTerminalNum(0);
        terminalTaskMonthlyTotal.setActivateTerminalNum(0);
        terminalTaskMonthlyTotal.setLivelyTerminalNum(0);
        terminalTaskMonthlyTotal.setCompleteVisitAndStrangerScore(BigDecimal.ZERO);
        terminalTaskMonthlyTotal.setCompleteMeetingScore(BigDecimal.ZERO);

        // 获取monthlyDetailModel中goodsCode相同的对象，保留一个对象并将其goodsNum相加
        Map<String, TerminalTaskMonthlyDetailModel> monthlyDetailModelMap = new HashMap<>();
        Map<Integer, TerminalTaskMonthlyGroupDetailResp> monthlyGroupDetailModelMap = new HashMap<>();
        Map<String, TerminalTaskMonthlyDetailModel> completeMonthlyDetailModelMap = new HashMap<>();
        Map<String, TerminalScanAmountDetailResp> completeMonthlyScanDetailsMap = new HashMap<>();
        //Set<TerminalShopModel> activateTerminalSet = new HashSet<>();
        Set<Integer> livelyTerminalIds = new HashSet<>();
        for(TerminalTaskMonthlyResp item : terminalTaskMonthlyTotalList) {
            if (Objects.nonNull(item)) {
                terminalTaskMonthlyTotal.setPlanScanScore(terminalTaskMonthlyTotal.getPlanScanScore().add(item.getPlanScanScore()));
                terminalTaskMonthlyTotal.setPlanVisitScore(terminalTaskMonthlyTotal.getPlanVisitScore().add(item.getPlanVisitScore()));
                // 判断completeScanScore是否为空，不为空则相加
                if (!Objects.isNull(item.getCompleteScanScore())) {
                    terminalTaskMonthlyTotal.setCompleteScanScore(terminalTaskMonthlyTotal.getCompleteScanScore().add(item.getCompleteScanScore()));
                }
                // 判断completeVisitScore是否为空，不为空则相加
                if (!Objects.isNull(item.getCompleteVisitScore())) {
                    terminalTaskMonthlyTotal.setCompleteVisitScore(terminalTaskMonthlyTotal.getCompleteVisitScore().add(item.getCompleteVisitScore()));
                }
                if (!Objects.isNull((item.getPlanActivateTerminalNum()))) {
                    terminalTaskMonthlyTotal.setPlanActivateTerminalNum(terminalTaskMonthlyTotal.getPlanActivateTerminalNum() + item.getPlanActivateTerminalNum());
                }
                if (!Objects.isNull((item.getPlanLivelyTerminalNum()))) {
                    terminalTaskMonthlyTotal.setPlanLivelyTerminalNum(terminalTaskMonthlyTotal.getPlanLivelyTerminalNum() + item.getPlanLivelyTerminalNum());
                }
                if (!Objects.isNull((item.getActivateTerminalNum()))) {
                    terminalTaskMonthlyTotal.setActivateTerminalNum(terminalTaskMonthlyTotal.getActivateTerminalNum() + item.getActivateTerminalNum());
                }
                if (!Objects.isNull((item.getLivelyTerminalNum()))) {
                    terminalTaskMonthlyTotal.setLivelyTerminalNum(terminalTaskMonthlyTotal.getLivelyTerminalNum() + item.getLivelyTerminalNum());
                }
                if (!Objects.isNull((item.getCompleteMeetingScore()))) {
                    terminalTaskMonthlyTotal.setCompleteMeetingScore(terminalTaskMonthlyTotal.getCompleteMeetingScore().add(item.getCompleteMeetingScore()));
                }
                if (!Objects.isNull((item.getCompleteVisitAndStrangerScore()))) {
                    terminalTaskMonthlyTotal.setCompleteVisitAndStrangerScore(terminalTaskMonthlyTotal.getCompleteVisitAndStrangerScore().add(item.getCompleteVisitAndStrangerScore()));
                }
                if (Objects.nonNull(item.getMonthlyDetailModel()) && item.getMonthlyDetailModel().size() > 0) {
                    for (TerminalTaskMonthlyDetailModel model : item.getMonthlyDetailModel()) {
                        monthlyDetailModelMap.compute(model.getGoodsCode(), (k, v) -> {
                            if (v == null) {
                                return model;
                            } else {
                                BigDecimal oldNum = Objects.nonNull(v.getGoodsNum()) ? v.getGoodsNum() : BigDecimal.ZERO;
                                BigDecimal newNum = Objects.nonNull(model.getGoodsNum()) ? model.getGoodsNum() : BigDecimal.ZERO;
                                v.setGoodsNum(oldNum.add(newNum));
                                return v;
                            }
                        });
                    }
                }

                //
                if (Objects.nonNull(item.getMonthlyGroupDetailModels()) && item.getMonthlyGroupDetailModels().size() > 0) {
                    for (TerminalTaskMonthlyGroupDetailResp model : item.getMonthlyGroupDetailModels()) {
                        monthlyGroupDetailModelMap.compute(model.getGroupId(), (k, v) -> {
                            if (v == null) {
                                return model;
                            } else {
                                BigDecimal oldNum = Objects.nonNull(v.getAmount()) ? v.getAmount() : BigDecimal.ZERO;
                                BigDecimal newNum = Objects.nonNull(model.getAmount()) ? model.getAmount() : BigDecimal.ZERO;
                                v.setAmount(oldNum.add(newNum));
                                return v;
                            }
                        });
                    }
                }
                // 计算已收货金额详情
                if (Objects.nonNull(item.getCompleteMonthlyDetailModel()) && item.getCompleteMonthlyDetailModel().size() > 0) {
                    for (TerminalTaskMonthlyDetailModel model : item.getCompleteMonthlyDetailModel()) {
                        completeMonthlyDetailModelMap.compute(model.getGoodsCode(), (k, v) -> {
                            if (v == null) {
                                return model;
                            } else {
                                BigDecimal oldNum = Objects.nonNull(v.getGoodsNum()) ? v.getGoodsNum() : BigDecimal.ZERO;
                                BigDecimal newNum = Objects.nonNull(model.getGoodsNum()) ? model.getGoodsNum() : BigDecimal.ZERO;
                                v.setGoodsNum(oldNum.add(newNum));
                                return v;
                            }
                        });
                    }
                }

                if (Objects.nonNull(item.getCompleteMonthlyScanDetails()) && item.getCompleteMonthlyScanDetails().size() > 0) {
                    for (TerminalScanAmountDetailResp model : item.getCompleteMonthlyScanDetails()) {
                        completeMonthlyScanDetailsMap.compute(model.getGoodsCode(), (k, v) -> {
                            if (v == null) {
                                return model;
                            } else {
                                BigDecimal oldNum = Objects.nonNull(v.getGoodsAmount()) ? v.getGoodsAmount() : BigDecimal.ZERO;
                                BigDecimal newNum = Objects.nonNull(model.getGoodsAmount()) ? model.getGoodsAmount() : BigDecimal.ZERO;
                                v.setGoodsAmount(oldNum.add(newNum));
                                return v;
                            }
                        });
                    }
                }

                if (Objects.nonNull(item.getLivelyTerminalList()) && item.getLivelyTerminalList().size() > 0) {
                    for (TerminalShopModel terminal : item.getLivelyTerminalList()) {
                        livelyTerminalIds.add(terminal.getId());
                    }
                }
            }
            terminalTaskMonthlyTotal.setMonthlyDetailModel(new ArrayList<>(monthlyDetailModelMap.values()));
            terminalTaskMonthlyTotal.setMonthlyGroupDetailModels(new ArrayList<>(monthlyGroupDetailModelMap.values()));
            terminalTaskMonthlyTotal.setCompleteMonthlyDetailModel(new ArrayList<>(completeMonthlyDetailModelMap.values()));
            terminalTaskMonthlyTotal.setCompleteMonthlyScanDetails(new ArrayList<>(completeMonthlyScanDetailsMap.values()));
            // 设置激活终端数量，如果activateTerminalSet不为空则设置激活终端数量为set的size，否则设置为0，使用三元运算符
            //terminalTaskMonthlyTotal.setActivateTerminalNum(Objects.nonNull(activateTerminalSet) ? activateTerminalSet.size() : 0);
            terminalTaskMonthlyTotal.setLivelyTerminalNum(Objects.nonNull(livelyTerminalIds) ? livelyTerminalIds.size() : 0);

            //填充月度任务完成率
            BigDecimal p = new BigDecimal(100);
            BigDecimal a = terminalTaskMonthlyTotal.getCompleteScanScore();
            BigDecimal b = terminalTaskMonthlyTotal.getPlanScanScore();
            BigDecimal scanP = BigDecimal.ZERO;
            if (b.compareTo(BigDecimal.ZERO) != 0) {
                BigDecimal planScanScore = b.multiply(new BigDecimal(10000));
                scanP = a.divide(planScanScore, 2, BigDecimal.ROUND_HALF_UP).multiply(p);
            }
            terminalTaskMonthlyTotal.setScanScorePercentage(scanP);
            BigDecimal c = terminalTaskMonthlyTotal.getCompleteVisitScore();
            BigDecimal d = terminalTaskMonthlyTotal.getPlanVisitScore();
            BigDecimal visitP = BigDecimal.ZERO;
            if (d.compareTo(BigDecimal.ZERO) != 0) {
                visitP = c.divide(d, 2, BigDecimal.ROUND_HALF_UP).multiply(p);
            }
            terminalTaskMonthlyTotal.setVisitScorePercentage(visitP);
        }
        return terminalTaskMonthlyTotal;
    };

    private TerminalTaskMonthlyModel getTerminalTaskMonthlyDetail(Integer tpId, Integer month){
        LambdaQueryWrapper<TerminalTaskMonthlyModel> taskMonthQueryWrapper = new LambdaQueryWrapper<>();
        taskMonthQueryWrapper.eq(TerminalTaskMonthlyModel::getTpId, tpId)
                .eq(TerminalTaskMonthlyModel::getMonth,month)
                .eq(TerminalTaskMonthlyModel::getDeleteFlag,0);
        return monthlyDao.selectOne(taskMonthQueryWrapper);
    }
    private TerminalTaskMonthlyResp convertTaskMonthlyResp(TerminalTaskMonthlyModel terminalTaskMonthlyModel){
        TerminalTaskMonthlyResp terminalTaskMonthly = new TerminalTaskMonthlyResp();
        terminalTaskMonthly.setId(terminalTaskMonthlyModel.getId());
        terminalTaskMonthly.setTpId(terminalTaskMonthlyModel.getTpId());
        terminalTaskMonthly.setMonth(terminalTaskMonthlyModel.getMonth());
        terminalTaskMonthly.setPlanVisitScore(terminalTaskMonthlyModel.getPlanVisitScore());
        terminalTaskMonthly.setPlanActivateTerminalNum(terminalTaskMonthlyModel.getPlanActivateTerminalNum());
        terminalTaskMonthly.setPlanLivelyTerminalNum(terminalTaskMonthlyModel.getPlanLivelyTerminalNum());
        terminalTaskMonthly.setDeleteFlag(terminalTaskMonthlyModel.getDeleteFlag());
        terminalTaskMonthly.setScanWeight(terminalTaskMonthlyModel.getScanWeight());
        terminalTaskMonthly.setLivelyWeight(terminalTaskMonthlyModel.getLivelyWeight());
        terminalTaskMonthly.setDevelopWeight(terminalTaskMonthlyModel.getDevelopWeight());
        terminalTaskMonthly.setPlanBanquetNum(terminalTaskMonthlyModel.getPlanBanquetNum());
        terminalTaskMonthly.setBanquetWeight(terminalTaskMonthlyModel.getBanquetWeight());
        terminalTaskMonthly.setExecutionAbility(terminalTaskMonthlyModel.getExecutionAbility());
        terminalTaskMonthly.setServiceAttitude(terminalTaskMonthlyModel.getServiceAttitude());
        terminalTaskMonthly.setWorkQuality(terminalTaskMonthlyModel.getWorkQuality());
        terminalTaskMonthly.setDiscipline(terminalTaskMonthlyModel.getDiscipline());
        terminalTaskMonthly.setComment(terminalTaskMonthlyModel.getComment());
        return terminalTaskMonthly;
    }
    @Override
    public TerminalTaskMonthlyResp getLastMonthTaskDetail(TerminalTaskPackageReq terminalTaskPackageReq) {
        TerminalTaskMonthlyResp terminalTaskMonthly = new TerminalTaskMonthlyResp();
        LambdaQueryWrapper<TerminalTaskPackageModel> queryWrapper=new LambdaQueryWrapper<>();
        queryWrapper
//                .eq(TerminalTaskPackageModel::getYear,terminalTaskPackageReq.getYear())
                .eq(TerminalTaskPackageModel::getId,terminalTaskPackageReq.getId())
                .eq(TerminalTaskPackageModel::getDeleteFlag,0);
        TerminalTaskPackageModel taskPackageModel = taskPackageDao.selectOne(queryWrapper);
        // 获取当前时间的月份
        LocalDate date = LocalDate.now();
        int month = date.getMonthValue();
//        判断是否是1月数据,跨年情况
        if(month == 1){
            //  查询继承任务
            if(Objects.nonNull(taskPackageModel.getExtendTpId())){
                //  继承任务获取12月份任务数据
                TerminalTaskMonthlyModel terminalTaskMonthlyModel = getTerminalTaskMonthlyDetail(taskPackageModel.getExtendTpId(), 12);
                if (Objects.nonNull(terminalTaskMonthlyModel)) {
                    terminalTaskMonthly = convertTaskMonthlyResp(terminalTaskMonthlyModel);
                }
            }
        }else{
            // 非1月数据查询上月任务数据
            int lastMonth = month -1;
            TerminalTaskMonthlyModel terminalTaskMonthlyModel = getTerminalTaskMonthlyDetail(terminalTaskPackageReq.getId(), lastMonth);
            if (Objects.nonNull(terminalTaskMonthlyModel)) {
                terminalTaskMonthly = convertTaskMonthlyResp(terminalTaskMonthlyModel);
            }
        }
        return terminalTaskMonthly;

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void extendTask (Integer sourceYear, Integer targetYear){
//      todo-测试&上线打开根据年份检查任务是否完成继承
        if(checkExtendTaskComplete(targetYear)){
            throw new BusinessException(targetYear + "年度任务包已完成继承操作！");
        }
//        根据年份查询需继承的有效任务
        LambdaQueryWrapper<TerminalTaskPackageModel> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TerminalTaskPackageModel::getYear, sourceYear)
            .eq(TerminalTaskPackageModel::getDeleteFlag, DeleteFlagEnum.NOT_DELETE.getKey())
            .eq(TerminalTaskPackageModel::getStatus,TerminalTaskPackageStatusFlagEnum.VALID.getKey());
//           todo 开发联调使用，提测&上线去掉
//            .in(TerminalTaskPackageModel::getId,360,602);
        List<TerminalTaskPackageModel> terminalTaskPackageList = taskPackageDao.selectList(queryWrapper);
        List<TerminalTaskPackageModel> extendTaskList = new ArrayList<>();
        //  生成目标年份任务包
        for( TerminalTaskPackageModel terminalTaskPackageModel : terminalTaskPackageList){
            TerminalTaskPackageModel model = new TerminalTaskPackageModel();
            BeanUtils.copyProperties(terminalTaskPackageModel,model);
        //  继承任务包并生成task_num
            model.setExtendTpId(terminalTaskPackageModel.getId());
            String genExtendTaskNum = genExtendTaskNum(terminalTaskPackageModel.getTaskNum(), targetYear);
            model.setId(null);
            model.setTaskNum(genExtendTaskNum);
            model.setYear(targetYear);
            model.setCreateTime(LocalDateTime.now());
            model.setUpdateTime(LocalDateTime.now());
            model.setStatus(TerminalTaskPackageStatusFlagEnum.VALID.getKey());
            extendTaskList.add(model);
//            terminalTaskPackageModel.setStatus(TerminalTaskPackageStatusFlagEnum.INVALID.getKey());
        }
//      todo-- 任务包入库
//        log.info("1, = " + System.currentTimeMillis());
        this.saveBatch(extendTaskList,extendTaskList.size());
//        log.info("2, = " + System.currentTimeMillis());
//        更新原任务包状态
//        this.saveOrUpdateBatch(terminalTaskPackageList,terminalTaskPackageList.size());
//        log.info("3, = " + System.currentTimeMillis());
//        更新上一年任务包无效状态
        taskPackageDao.updateInvalidStatus(sourceYear);
//        this.updateBatchById(terminalTaskPackageList,terminalTaskPackageList.size());
//        log.info("4, = " + System.currentTimeMillis());
//      设置 redis 任务号计数器数值
        setRedisTaskNum(targetYear);
//      获取需要继承的区域
        Map<Integer, List<TerminalTaskPackageRegionModel>> regionMap = terminalTaskPackageRegionService.selectExtendTaskPackageRegion(sourceYear);
//      批量添加需要继承的任务区域
        if(!CollectionUtil.isEmpty(regionMap)) {
            terminalTaskPackageRegionService.insertExtendTaskPackageRegion(extendTaskList, regionMap);
            terminalTaskPackageRegionService.updateInvalidStatus(sourceYear);
        }
//      获取需要继承的月度任务
        Map<Integer, List<TerminalTaskMonthlyModel>> monthlyMap = terminalTaskMonthlyService.selectExtendTaskMonthly(sourceYear);
//      批量添加需要继承的月度任务
        if(!CollectionUtil.isEmpty(monthlyMap)) {
            terminalTaskMonthlyService.insertExtendTaskMonthly(extendTaskList, monthlyMap);
            terminalTaskMonthlyService.updateInvalidStatus(sourceYear);
        }
//      获取需要继承的月度任务详情 -- 与产品沟通月度任务详情不做继承
//        Map<Integer, List<TerminalTaskMonthlyDetailModel>> monthlyDetailMap = terminalTaskMonthlyDetailService.selectExtendTaskMonthlyDetail(sourceYear);
//      批量添加继承月度任务详情
//        terminalTaskMonthlyDetailService.insertExtendTaskMonthlyDetail(extendTaskMonthlyList, monthlyDetailMap);
//      获取需要继承的年度任务
        Map<Integer, List<TerminalTaskYearlyModel>> yearlyMap = terminalTaskYearlyService.selectExtendTaskYearly(sourceYear);
//      批量添加继承年度任务
        if(!CollectionUtil.isEmpty(yearlyMap)) {
            List<TerminalTaskYearlyModel> extendTaskYearlyList = terminalTaskYearlyService.insertExtendTaskYearly(extendTaskList, yearlyMap);
            //      更新上年月度任务为无效
            terminalTaskYearlyService.updateInvalidStatus(sourceYear);
//      获取需要继承的年度任务详情
            Map<Integer, List<TerminalTaskYearlyDetailModel>> yearlyDetailMap = terminalTaskYearlyDetailService.selectExtendTaskYearlyDetail(sourceYear);
//      批量添加继承年度任务详情
            if(!CollectionUtil.isEmpty(yearlyDetailMap)){
                terminalTaskYearlyDetailService.insertExtendTaskYearlyDetail(extendTaskYearlyList, yearlyDetailMap);
                terminalTaskYearlyDetailService.updateInvalidStatus(sourceYear);
            }
        }
//      获取需要继承人包关系
        Map<Integer, List<TerminalTaskReceiveModel>> taskReceiveMap = terminalTaskReceiveService.selectExtendTaskReceive(sourceYear);
//      批量添加继承人包关系
        if(!CollectionUtil.isEmpty(taskReceiveMap)){
            List<TerminalTaskReceiveModel> extendReceiveList = terminalTaskReceiveService.insertExtendTaskReceive(extendTaskList, taskReceiveMap);
            terminalTaskReceiveService.updateInvalidStatus(sourceYear);
            //批量添加任务领取记录-月度
            terminalTaskReceiveMonthService.insertExtendTaskReceiveMonth(extendReceiveList);
        }

    }

    public Boolean checkExtendTaskComplete(Integer year){
        LambdaQueryWrapper<TerminalTaskPackageModel> queryWrapper=new LambdaQueryWrapper<>();
//       不校验删除标记，状态，可能存在占用task_num情况
        queryWrapper.eq(TerminalTaskPackageModel::getYear,year);
//                .eq(TerminalTaskPackageModel::getDeleteFlag,DeleteFlagEnum.NOT_DELETE.getKey())
//                .eq(TerminalTaskPackageModel::getStatus, TerminalTaskPackageStatusFlagEnum.VALID.getKey());
//                .isNotNull(TerminalTaskPackageModel::getExtendTpId);
        List<TerminalTaskPackageModel> terminalTaskPackagelist = taskPackageDao.selectList(queryWrapper);
        if(Objects.nonNull(terminalTaskPackagelist) && terminalTaskPackagelist.size()>0){
            return true;
        }
        return false;
    }
    public String genExtendTaskNum(String sourceTaskNum, Integer targetYear){
        String taskNum = sourceTaskNum.substring(0, sourceTaskNum.length() - 2);
        Integer year = targetYear%100;
        String targetTaskNum = String.format("%s%d", taskNum, year);
        return targetTaskNum;
    }

    public void setRedisTaskNum(Integer targetYear){
        //根据年份获取数据库中最新的任务编号
        LambdaQueryWrapper<TerminalTaskPackageModel> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TerminalTaskPackageModel::getYear,targetYear)
                .orderByDesc(TerminalTaskPackageModel::getId).last("limit 1");
        TerminalTaskPackageModel one = taskPackageDao.selectOne(queryWrapper);
        redisUtil.set(targetYear+"taskNum",one.getTaskNum());
    }


}
