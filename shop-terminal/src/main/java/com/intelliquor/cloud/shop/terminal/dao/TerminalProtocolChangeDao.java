package com.intelliquor.cloud.shop.terminal.dao;

import com.intelliquor.cloud.shop.common.model.TerminalProtocolModel;
import com.intelliquor.cloud.shop.terminal.model.TerminalProtocolChangeModel;
import com.intelliquor.cloud.shop.terminal.model.req.TerminalProtocolChangeReq;
import com.intelliquor.cloud.shop.terminal.model.resp.TerminalProtocolChangeDetailResp;
import com.intelliquor.cloud.shop.common.model.resp.TerminalProtocolResp;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface TerminalProtocolChangeDao {

    /**
     * 查询终端关联的协议列表
     *
     * @param terminalId
     * @param sortType
     * @return
     */
    List<TerminalProtocolResp> selectTerminalProtocolListByShopId(@Param("terminalShopId") Integer terminalId, @Param("sortType") Integer sortType);

    /**
     * 查询终端关联的协议列表
     *
     * @param terminalId
     * @param sortType
     * @return
     */
    List<TerminalProtocolChangeReq> getTerminalProtocolListByShopId(@Param("terminalShopId") Integer terminalId, @Param("sortType") Integer sortType);

    /**
     * 根据协议类型查询该终端上一次审核通过的协议
     * @return
     */
    TerminalProtocolChangeReq selectCheckedReqModel(@Param("terminalShopId") Integer terminalShopId, @Param("protocolType") Integer protocolType);

    TerminalProtocolChangeReq selectProtocolReqModelByParams(@Param("id") Integer id);

    /**
     * 新增协议
     * @param changeReq
     */
    void insertTerminalProtocol(@Param("protocol") TerminalProtocolChangeReq changeReq);

    /**
     * 逻辑删除终端协议
     * @param changeReq
     */
    void deleteTerminalProtocolById(@Param("protocol") TerminalProtocolModel changeReq);

    /**
     * 审核协议
     * @param changeReq
     */
    void checkTerminalProtocol(@Param("protocol") TerminalProtocolChangeReq changeReq);

    void insertTerminalProtocolChange(@Param("changeModel") TerminalProtocolChangeModel changeModel);

    /**
     * 更新协议记录的状态
     * @param newId t_terminal_protocol的主键id
     * @param status
     */
    void updateChangeRecordApprovalStatusByNewId(@Param("newId") Integer newId, @Param("status") Integer status);

    List<TerminalProtocolChangeModel> getTerminalProtocolChangeList(@Param("terminalShopId") Integer terminalShopId);

    Integer getOldIdByNewId(@Param("newId") Integer id);

    TerminalProtocolChangeDetailResp selectDetailById(@Param("id") Integer id);

    Integer getChangeTypeByTerminalShopId(@Param("terminalShopId") Integer terminalShopId);

    List<TerminalProtocolChangeModel> selectListByOldIds(List<Long> idList);
}
