package com.intelliquor.cloud.shop.terminal.model.req;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

@Data
public class TerminalMeetingRecordScoreReq {

    /**
     * 会议打卡用户id
     * */
    private Integer createUserId;

    /**
     * 任务包id
     * */
    private Integer tpId;

    private Integer tmId;

    @ApiModelProperty("活动开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date startTime;

    /**
     * 活动结束时间
     */
    @ApiModelProperty("活动结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date endTime;

    /**
     * 业代id
     */
    @ApiModelProperty("业代id")
    private Integer brokerId;
}
