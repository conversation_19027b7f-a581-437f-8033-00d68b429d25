package com.intelliquor.cloud.shop.terminal.model.req;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @PackageName com.intelliquor.cloud.shop.terminal.model.req
 * @ClassName TotalReq
 * @description: TODO
 * @datetime 2023年 07月 20日 15:42
 * @version: 1.0
 */
@Data
public class TotalReq implements Serializable {

    private static final long serialVersionUID = 1L;

    private String keys;

    /*@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone="GMT+8")*/
    private String startDate;

    /*@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone="GMT+8")*/
    private String endDate;
}
