package com.intelliquor.cloud.shop.terminal.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.intelliquor.cloud.shop.terminal.model.TaskConfParamField;
import com.intelliquor.cloud.shop.terminal.model.req.TaskConfParamFieldReq;
import com.intelliquor.cloud.shop.terminal.service.ITaskConfParamFieldService;
import com.intelliquor.cloud.shop.terminal.dao.TaskConfParamFieldMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
* <AUTHOR>
* @description 针对表【t_task_conf_param_field(参数值配置)】的数据库操作Service实现
* @createDate 2023-07-24 11:30:54
*/
@Slf4j
@Service
public class TaskConfParamFieldServiceImpl extends ServiceImpl<TaskConfParamFieldMapper, TaskConfParamField>
    implements ITaskConfParamFieldService {

    @Autowired
    private TaskConfParamFieldMapper taskConfParamFieldMapper;

    @Override
    public void add(TaskConfParamFieldReq req) {
        TaskConfParamField field = new TaskConfParamField();
        BeanUtils.copyProperties(req,field);
        field.setIsDelete(0);
        field.setIsNew(1);
        field.setCreateTime(new Date());
        this.save(field);
    }

    @Override
    public void update(TaskConfParamFieldReq req) {
        TaskConfParamField field = taskConfParamFieldMapper.selectById(req.getId());
        BeanUtils.copyProperties(req,field);
        field.setUpdateTime(new Date());
        this.updateById(field);
    }

    @Override
    public void delete(TaskConfParamFieldReq req) {
        TaskConfParamField field = new TaskConfParamField();
        field.setId(req.getId());
        field.setDelTime(new Date());
        field.setDelUser(req.getDelUser());
        field.setIsDelete(1);
        field.setIsNew(0);
        this.updateById(field);
    }

    @Override
    public void deleteByParamId(Long paramId, Integer userId) {
        QueryWrapper<TaskConfParamField> qw = new QueryWrapper<>();
        qw.eq("param_id", paramId);
        TaskConfParamField field = new TaskConfParamField();
        field.setDelUser(userId);
        field.setDelTime(new Date());
        field.setIsDelete(1);
        taskConfParamFieldMapper.update(field,qw);
    }

    @Override
    public void updateNewByParamId(Long paramId, Integer userId, List<Long> ids, Integer isNew) {
        QueryWrapper<TaskConfParamField> qw = new QueryWrapper<>();
        if(CollectionUtils.isNotEmpty(ids)){
            qw.in("id", ids);
        }
        if(Objects.nonNull(paramId)){
            qw.eq("param_id", paramId);
        }
        qw.eq("is_new", isNew == 0 ? 1 : 0);
        TaskConfParamField field = new TaskConfParamField();
        field.setDelUser(userId);
        field.setDelTime(new Date());
        field.setIsDelete(isNew == 0 ? 1 : 0);
        field.setIsNew(isNew);
        taskConfParamFieldMapper.update(field,qw);
    }
}




