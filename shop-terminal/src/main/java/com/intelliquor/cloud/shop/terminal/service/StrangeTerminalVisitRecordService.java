package com.intelliquor.cloud.shop.terminal.service;

import com.github.pagehelper.PageInfo;
import com.intelliquor.cloud.shop.terminal.model.StrangeTerminalVisitRecordModel;
import com.intelliquor.cloud.shop.terminal.model.dto.StrangeTerminalVisitRecordDto;
import com.intelliquor.cloud.shop.terminal.model.req.StrangeTerminalVisitRecordListByBrokerIdReq;
import com.intelliquor.cloud.shop.terminal.model.req.StrangeTerminalVisitRecordReq;
import com.intelliquor.cloud.shop.terminal.model.resp.StrangeTerminalVisitRecordResp;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date: 2023/7/6
 */
public interface StrangeTerminalVisitRecordService {

    /**
     * 新增拜访记录
     */
    void insertReturnKey(StrangeTerminalVisitRecordModel model);

    /**
     * 根据陌生终端id获取陌生终端拜访详情
     * id: 主键ID
     */
    StrangeTerminalVisitRecordModel getStrangeTerminalVisitRecordById(Integer id);

    /**
     * 根据终端id获取拜访列表
     * strangeTerminalVisitRecordReq: 陌生拜访记录表req实体
     */
    PageInfo<StrangeTerminalVisitRecordModel> getRecordListByShopId(StrangeTerminalVisitRecordReq strangeTerminalVisitRecordReq);

    /**
     * 统计范围时间内陌生终端点数
     * @param startDate 起始时间
     * @param endDate 截止时间
     * @return
     */
    void statisticsStrangeTerminalPoint(String startDate, String endDate);

    StrangeTerminalVisitRecordDto statisticsStrangeTerminalPointByTmId(Integer tmId, String startTime, String endTime);

    PageInfo<StrangeTerminalVisitRecordResp> getRecordListByBrokerId(StrangeTerminalVisitRecordListByBrokerIdReq strangeTerminalVisitRecordReq);

    void visitReview(StrangeTerminalVisitRecordReq model);


    List<StrangeTerminalVisitRecordModel> getCurrentMonthRecordListByShopIds(List<Integer> shopIds);

    Map<Integer, Integer> getCurrentMonthRecordCountByShopIds(List<Integer> shopIds);

    /**
     * 根据陌生拜访得分记录的业代id和年月，获取拜访记录得分
     * @param brokerId
     * @param year
     * @param month
     * @return
     */
    List<StrangeTerminalVisitRecordModel> getVisitRecordScoreByStaffIdAndYearAndMonth(Integer brokerId, Integer year, Integer month);

}
