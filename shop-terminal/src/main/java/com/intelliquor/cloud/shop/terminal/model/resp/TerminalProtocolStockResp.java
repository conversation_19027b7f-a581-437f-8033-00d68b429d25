package com.intelliquor.cloud.shop.terminal.model.resp;

import lombok.Data;

import java.util.Date;

/**
 * 协议信息，以及该协议对应的进货量 实体
 *
 * @Date：2023-05-25 16:36
 * @author：Panys
 * @version：1.0
 */
@Data
public class TerminalProtocolStockResp {

    /**
     * t_terminal_protocol表的主键id
     */
    private Integer id;

    /**
     * 协议类型：0:主协议 1:附加协议
     */
    private Integer protocolType;

    /**
     * 终端等级编码（t_terminal_shop_level的id）
     */
    private String levelCode;

    /**
     * t_member_shop的主键id
     */
    private Integer memberShopId;

    /**
     * 协议生效时间
     */
    private Date effectiveTime;

    /**
     * 年进货量
     */
    private Integer yearScanInNum;
}
