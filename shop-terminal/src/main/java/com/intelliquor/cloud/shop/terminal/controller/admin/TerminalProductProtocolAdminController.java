package com.intelliquor.cloud.shop.terminal.controller.admin;

import com.github.pagehelper.PageInfo;
import com.intelliquor.cloud.shop.common.exception.RestResponse;
import com.intelliquor.cloud.shop.common.model.UserContext;
import com.intelliquor.cloud.shop.common.model.TerminalProductProtocolModel;
import com.intelliquor.cloud.shop.terminal.service.ITerminalProductProtocolService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * 后台-产品协议配置
 * </p>
 *
 * <AUTHOR>
 * @since 2023-01-13
 */
@RestController
@RequestMapping("/terminal/TerminalProductProtocol")
public class TerminalProductProtocolAdminController {

    @Autowired
    private ITerminalProductProtocolService iTerminalProductProtocolService;

    @Autowired
    private UserContext userContext;

    /**
     * 分页查询
     * @param page
     * @param limit
     * @param model
     * @return
     */
    @GetMapping("/selectList")
    public RestResponse<PageInfo<TerminalProductProtocolModel>> selectList(@RequestParam(value = "page",defaultValue = "1")Integer page,
                                                                  @RequestParam(value = "limit",defaultValue = "10")Integer limit,
                                                                  TerminalProductProtocolModel model){
        model.setCompanyId(userContext.getUserInfo().getIntCompanyId());
        List<TerminalProductProtocolModel> selectDataList = iTerminalProductProtocolService.selectList(page,limit,model);
        PageInfo<TerminalProductProtocolModel> pageInfo = new PageInfo<>(selectDataList);
        return RestResponse.success("查询成功",pageInfo);
    }

    /**
     * 保存
     * @param model
     * @return
     */
    @PostMapping("/save")
    public RestResponse<String> save(@RequestBody TerminalProductProtocolModel model){
        model.setCompanyId(userContext.getUserInfo().getIntCompanyId());
        model.setCreateUserId(userContext.getUserInfo().getUserId());
        model.setCreateUserName(userContext.getUserInfo().getUsername());
        iTerminalProductProtocolService.save(model);
        return RestResponse.success("保存成功");
    }

    /**
     * 更新
     * */
    @PutMapping("/update")
    public RestResponse<String> update(@RequestBody TerminalProductProtocolModel model){
        model.setUpdateTime(new Date());
        iTerminalProductProtocolService.updateById(model);
        return RestResponse.success("修改成功");
    }

    /**
     * 获取详情
     * */
    @GetMapping("/getById")
    public RestResponse<TerminalProductProtocolModel> getById(Integer id){
        TerminalProductProtocolModel model = iTerminalProductProtocolService.getById(id);
        return RestResponse.success(model);
    }

    /**
     * 刪除
     * */
    @DeleteMapping("/delete")
    public RestResponse<String> delete(Integer id){
        TerminalProductProtocolModel model = new TerminalProductProtocolModel();
        model.setId(id);
        model.setIsDelete(1);
        model.setUpdateTime(new Date());
        iTerminalProductProtocolService.updateById(model);
        return RestResponse.success("删除成功");
    }
}
