package com.intelliquor.cloud.shop.terminal.controller.admin;

import cn.hutool.core.collection.CollectionUtil;
import com.intelliquor.cloud.shop.common.exception.RestResponse;
import com.intelliquor.cloud.shop.terminal.model.TerminalVisitRecordScoreModel;
import com.intelliquor.cloud.shop.terminal.model.req.TerminalVisitRecordScoreReq;
import com.intelliquor.cloud.shop.terminal.service.ITerminalVisitRecordScoreService;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpStatus;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 业代-拜访记录得分 前端控制器
 *
 * <AUTHOR>
 * @since 2023-08-19
 */
//@Slf4j
//@RestController
//@RequestMapping("/terminalVisitRecordScore")
public class TerminalVisitRecordScoreController {

//    @Resource
//    private ITerminalVisitRecordScoreService terminalVisitRecordScoreService;
//
//    @PostMapping(value = "/countVisitRecordScore")
//    @ApiOperation(value = "计算拜访记录得分", notes = "计算拜访记录得分", httpMethod = "POST")
//    public RestResponse<Boolean> countVisitRecordScore(@RequestBody TerminalVisitRecordScoreReq terminalVisitRecordScoreReq) {
//        boolean result = false;
//        String msg = "";
//        // 获取三方业代拜访记录并带出得分，存入拜访记录得分表
//        List<TerminalVisitRecordScoreModel> terminalVisitRecordScoreList = terminalVisitRecordScoreService.countVisitRecordScore(terminalVisitRecordScoreReq);
//        if(!CollectionUtil.isEmpty(terminalVisitRecordScoreList)){
//            log.info("获取拜访记录得分数据条目数: " + terminalVisitRecordScoreList.size());
//            result = terminalVisitRecordScoreService.saveBatch(terminalVisitRecordScoreList);
//            if(result){
//                msg = "处理拜访记录得分数据条目数: " + terminalVisitRecordScoreList.size();
//            } else {
//                msg = "拜访记录得分数据批量入库异常";
//            }
//        }else {
//            msg = "未获取到拜访记录得分数据";
//        }
//
//        return new RestResponse(HttpStatus.SC_OK, msg, result);
//    }
}
