package com.intelliquor.cloud.shop.terminal.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.intelliquor.cloud.shop.terminal.dao.VisitTerminalConfigDao;
import com.intelliquor.cloud.shop.terminal.model.VisitTerminalConfigModel;
import com.intelliquor.cloud.shop.terminal.model.config.ResultFlag;
import com.intelliquor.cloud.shop.terminal.model.req.VisitTerminalConfigReq;
import com.intelliquor.cloud.shop.terminal.model.resp.VisitTerminalConfigResp;
import com.intelliquor.cloud.shop.terminal.service.VisitTerminalConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 拜访审核设置
 */
@Slf4j
@Service
public class VisitTerminalConfigServiceImpl extends ServiceImpl<VisitTerminalConfigDao, VisitTerminalConfigModel> implements VisitTerminalConfigService {

    @Resource
    public VisitTerminalConfigDao visitTerminalConfigDao;

    @Override
    public PageInfo<VisitTerminalConfigResp> page(int pageNum, int pageSize) {
        PageHelper.startPage(pageNum, pageSize);
        List<VisitTerminalConfigResp> list = visitTerminalConfigDao.selectListPage();
        PageInfo<VisitTerminalConfigResp> pageInfo = new PageInfo<>(list);
        return pageInfo;
    }

    @Override
    public ResultFlag updateConfig(VisitTerminalConfigReq entity) {
        VisitTerminalConfigModel config = new VisitTerminalConfigModel();
        BeanUtils.copyProperties(entity,config);
        if (config.getId() == null) {
            throw new RuntimeException("修改配置id不能为空");
        }
        Date date = new Date();
        config.setUpdateTime(date);
        int result = this.baseMapper.updateById(config);
        ResultFlag flag = new ResultFlag();
        flag.setFlag(result);
        return flag;
    }

    @Override
    public ResultFlag save(VisitTerminalConfigReq req) {
        VisitTerminalConfigModel config = new VisitTerminalConfigModel();
        BeanUtils.copyProperties(req, config);
        Date date = new Date();
        config.setCreateTime(date);
        this.baseMapper.insert(config);
        ResultFlag flag = new ResultFlag();
        flag.setFlag(1);
        return flag;
    }

    @Override
    public void delete(VisitTerminalConfigReq req) {
        VisitTerminalConfigModel config = new VisitTerminalConfigModel();
        BeanUtils.copyProperties(req, config);
        config.setDelTime(new Date());
        config.setIsDelete(1);
        this.updateById(config);
    }

    @Override
    public VisitTerminalConfigResp getById(Integer id) {
        VisitTerminalConfigModel visitTerminalConfigModel = this.baseMapper.selectById(id);
        VisitTerminalConfigResp resp = new VisitTerminalConfigResp();
        BeanUtils.copyProperties(visitTerminalConfigModel, resp);
        return resp;
    }
}
