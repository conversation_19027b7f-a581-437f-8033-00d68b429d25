package com.intelliquor.cloud.shop.terminal.controller.applet;

import com.intelliquor.cloud.shop.common.dao.TerminalDisplayActivityConfigDao;
import com.intelliquor.cloud.shop.common.exception.BusinessException;
import com.intelliquor.cloud.shop.common.exception.RestResponse;
import com.intelliquor.cloud.shop.common.model.TerminalDisplayActivityConfigModel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequestMapping("/terminalDisplayActivityConfig")

public class TerminalDisplayActivityConfigController {
    @Autowired
    private TerminalDisplayActivityConfigDao terminalDisplayActivityConfigDao;
    /**
     * 根据陈列类别id，获取陈列面要求
     */
    @GetMapping("/getTerminalDisplaySurface")
    public RestResponse<TerminalDisplayActivityConfigModel> getTerminalDisplaySurface(@Validated  Long displayTypeId) {
        try {
            // 查询数据
            TerminalDisplayActivityConfigModel terminalDisplayActivityConfigModel = terminalDisplayActivityConfigDao.selectById(displayTypeId);
            if(terminalDisplayActivityConfigModel == null){
                throw new BusinessException("未查询到陈列类型配置信息");
            }
            // 返回成功的信息
            return RestResponse.success("终端小程序端获取陈列面参数成功", terminalDisplayActivityConfigModel);
        } catch (BusinessException e) {
            log.error("终端小程序端获取陈列面参数失败", e);
            return RestResponse.error(Integer.parseInt(e.getCode()), e.getMessage());
        } catch (Exception e) {
            log.error("终端小程序端获取陈列面参数失败", e);
            return RestResponse.error(e.getMessage());
        }
    }
}
