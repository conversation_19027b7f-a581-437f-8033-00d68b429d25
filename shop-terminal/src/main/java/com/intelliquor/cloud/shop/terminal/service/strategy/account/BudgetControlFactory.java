package com.intelliquor.cloud.shop.terminal.service.strategy.account;

import com.intelliquor.cloud.shop.common.model.resp.BudgetControlInfoResp;
import com.intelliquor.cloud.shop.terminal.model.resp.DisplayResultResp;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Component
public class BudgetControlFactory {
    private final Map<String, AbstractBudgetControlStrategy> strategyMap;

    @Autowired
    public BudgetControlFactory(List<AbstractBudgetControlStrategy> strategies) {
        this.strategyMap = strategies.stream()
                .collect(Collectors.toMap(AbstractBudgetControlStrategy::getType, Function.identity()));
    }

    /**
     * 预算控制分发
     * @param type 预算类型
     * @param displayResultResp 展示结果
     */
    public Boolean budgetControl(String type, DisplayResultResp displayResultResp,String batchNumber,Integer operateType) {
        AbstractBudgetControlStrategy strategy = strategyMap.get(type);
        if (strategy == null) {
            throw new IllegalArgumentException("未知type: " + type);
        }
        return strategy.budgetControl(batchNumber, displayResultResp, operateType);
    }
}
