package com.intelliquor.cloud.shop.terminal.model.resp;

import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.intelliquor.cloud.shop.common.model.resp.TerminalProtocolResp;
import com.intelliquor.cloud.shop.common.utils.CodeConstant;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

@Data
public class GtOpenTerminalShopAuditResp{

    /**
     * 终端主键id
     * */
    private Integer id;
    private Integer terminalShopId;

    /**
     * 终端名称
     * */
    private String shopName;

    /**
     * 终端副编码
     * */
    private String deputyCode;

    /**
     * 终端主编码
     * */
    private String mainCode;

    /**
     * 标记 1-普通、2-多关注、3-重点关注
     */
    private Integer tag;
    private String tagName;

    /**
     * 终端负责人姓名
     */
    private String leaderName;

    /**
     * 终端负责人电话
     */
    private String leaderPhone;

    /**
     * 终端门头照
     */
    private String headImg;

    /**
     * 备注
     */
    private String remark;
    /**
     * 行政区域省
     * */
    private String province;

    /**
     * 行政区域市
     * */
    private String city;

    /**
     * 行政区域区
     * */
    private String district;
    //省 市区
    private String areaName;

    /**
     * 详细地址
     */
    private String address;

    /**
     * 是否形象店 0- 否 1-是
     */
    private Integer isImage;
    private String isImageName;

    /**
     * 形象店门头照
     * */
    private String imageHeadPicture;


    /**
     * 有无营业执照
     * */
    private Integer whetherLicense;
    private String whetherLicenseName;

    /**
     * 营业执照照片
     */
    private String licenseImg;

    /**
     * 营业执照编号
     */
    private String licenseCode;

    /**
     * 激活状态 0- 未激活 1-已激活
     */
    private Integer status;


    /**
     * 创建时间
     * */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 商户Id
     */
    private Integer companyId;

    /**
     * 联盟终端店id
     * */
    private Integer memberShopId;

    /**
     * 新终端类型
     * */
    private Integer shopType;
    private String shopTypeName;


    /**
     * 食品经营许可证
     * */
    private String foodBusinessLicense;

    /**
     * 是否是自营终端
     * */
    private Integer whetherProprietaryTrading;
    private String whetherProprietaryTradingName;

    /**
     * 收货仓库地址 = 收货仓库省+ 收货仓库市 + 收货仓库区 +收货仓库地址
     * */
    private String receivingWarehouseAddress;


    /**
     * 联系人姓名
     * */
    private String contactName;

    /**
     * 联系人级别
     * */
    private String contactLevel;

    /**
     * 联系人手机号
     * */
    private String contactPhone;

    /**
     * 收款方式
     * */
    private Integer receivingPaymentType;
    private String receivingPaymentTypeName;

    /**
     * 收款人名称
     * */
    private String receivingPaymentName;

    /**
     * 收款人账户
     * */
    private String receivingPaymentAccount;

    /**
     * 收款银行
     * */
    private String receivingPaymentBank;

    /**
     * 收款人账户图片
     * */
    private String receivingPaymentAccountPicture;

    /**
     * 分销商名称
     */
    private String distributorName;

    /*********经销商信息【BEGIN】************/
    //经销商名称
    private String dealerName;
    // 经销商编码
    private String dealerCode;
    //合同编码
    private String contractCodeName;
    private String contractCode;// 合同编码
    //合同类型(2:国标合同,3:酱酒合同，4、5、6高端酒合同，)
    private Integer  contractType;
    private String contractTypeName;
    /*********经销商信息【END】************/

    public String getContractTypeName() {
        return CodeConstant.CONTRACT_TYPE_MAP.get(this.contractType);
    }

    public String getShopTypeName() {
        return CodeConstant.SHOP_TYPE_MAP.get(this.shopType.toString());
    }
    public String getTagName() {
        return CodeConstant.TAG_MAP.get(this.tag);
    }

    public String getWhetherProprietaryTradingName() {return CodeConstant.YES_NO_MAP.get(this.whetherProprietaryTrading);}
    public String getIsImageName() {
        return CodeConstant.YES_NO_MAP.get(this.isImage);
    }
    public String getReceivingPaymentTypeName() {return CodeConstant.RECEIVING_PAYMENT_TYPE_MAP.get(this.receivingPaymentType);}
    public String getWhetherLicenseName() {
        return CodeConstant.YES_NO_MAP.get(this.whetherLicense);
    }


    private String rawData;

    /**
     * 终端关联的协议列表，当前终端最多只会有一个协议正在审核中
     */
    private List<TerminalProtocolResp> protocolList;

    /**
     * 相似终端列表
     */
    private List<SimilarTerminalResp> similarTerminalList;

    /**
     * 经销商合同对应的商品列表信息
     */
    private JSONObject goods;

    /**
     * 分公司名称
     */
    private String affiliateName;

    /**
     * 大区名称
     */
    private String regionName;

    /**
     * 客户经理
     */
    private String accountManagerName;

}
