package com.intelliquor.cloud.shop.terminal.dao;

import com.intelliquor.cloud.shop.terminal.model.TerminalAccountManagerDealerModel;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.intelliquor.cloud.shop.terminal.model.resp.TerminalAccountManagerDealerResp;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-05
 */
@Repository
public interface TerminalAccountManagerDealerDao extends BaseMapper<TerminalAccountManagerDealerModel> {

    Integer insertBatchTerminalAccountManagerDealer(@Param("accountManagerId")Integer accountManagerId,
                                                    @Param("dealerDataList")List<Map<String,Object>> dealerDataList,
                                                    @Param("createTime")Date createTime);

    List<TerminalAccountManagerDealerResp> selectDealerByAccountManagerId(@Param("id") Integer id,
                                                                          @Param("nameOrPhone") String nameOrPhone);

    List<TerminalAccountManagerDealerResp> selectDealerByDealerId(@Param("id") Integer id);

    List<TerminalAccountManagerDealerResp> selectShopByAccountManagerId(@Param("shopIdList")List<Integer> shopIdList,
                                                                        @Param("nameOrPhone") String nameOrPhone);

    List<Integer> selectDealerId(@Param("DealerList")List<TerminalAccountManagerDealerModel> DealerList);

    List<Map<String,Object>> selectParentId(@Param("DealerList")List<Integer> DealerList , @Param("dealerCode")String dealerCode );

    List<Integer> selectShopByDealerId(@Param("DealerIdList")List<Integer> DealerIdList);

    List<Integer> selectShopIdByAccountManagerId(@Param("accountManagerId")Integer accountManagerId);


}
