package com.intelliquor.cloud.shop.terminal.controller.admin;

import com.github.pagehelper.PageInfo;
import com.intelliquor.cloud.shop.common.exception.RestResponse;
import com.intelliquor.cloud.shop.terminal.model.req.TerminalDivideReq;
import com.intelliquor.cloud.shop.terminal.model.resp.TerminalDivideListResp;
import com.intelliquor.cloud.shop.terminal.service.ITerminalDividerRecordService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 终端划分
 *
 * @module 国台终端
 */
@Slf4j
@RestController
@RequestMapping("/admin/terminalDivide")
public class TerminalDivideAdminController {

    @Resource
    private ITerminalDividerRecordService terminalDividerRecordService;

    /**
     * 划分记录列表
     */
    @GetMapping("/selectTerminalDivideList")
    public RestResponse<PageInfo<TerminalDivideListResp>> selectTerminalDivideList(@RequestParam(value = "page", defaultValue = "1") int page,
                                                                                   @RequestParam(value = "limit", defaultValue = "10") int limit,
                                                                                   TerminalDivideReq terminalDivideReq) {
        //查询
        List<TerminalDivideListResp> selectDataList = terminalDividerRecordService.selectTerminalDivideList(page, limit, terminalDivideReq);
        //分页
        PageInfo<TerminalDivideListResp> pageInfo = new PageInfo<>(selectDataList);
        //返回数据
        return RestResponse.success(pageInfo);
    }
}
