package com.intelliquor.cloud.shop.terminal.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.intelliquor.cloud.shop.common.model.TerminalProtocolModel;
import com.intelliquor.cloud.shop.common.model.vo.TerminalProtocolModelVo;
import com.intelliquor.cloud.shop.terminal.model.req.MaxLevelProtocalReq;
import com.intelliquor.cloud.shop.terminal.model.resp.MaxLevelProtocalResp;
import com.intelliquor.cloud.shop.terminal.model.resp.TerminalProtocolModelResp;
import com.intelliquor.cloud.shop.terminal.model.resp.TerminalProtocolStockResp;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 终端协议表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-18
 */
@Mapper
public interface TerminalProtocolDao extends BaseMapper<TerminalProtocolModel> {

    Integer deleteByShopId(Integer shopId);

    void updateStatus(@Param("id") Integer id, @Param("status") Integer status);

    String getLevelNameByLevelCode(@Param("levelCode") String levelCode);


    TerminalProtocolModel selectProtocolByTerminalShopId(Integer  terminalShopId);

    void deleteProtocolById(@Param("id") Long id);


    /**
     *  临时使用
     */
    List<TerminalProtocolModel> selectProtocolList();

    List<TerminalProtocolModel> selectProtocolListByTerminalShopId(@Param("terminalShopId") Integer terminalShopId);

    List<TerminalProtocolStockResp> getTerminalProtocolInfoList();

    Integer getStockNumberByShopIdAndTime(@Param("shopId") Integer shopId,
                                          @Param("beginTime")  String beginTime,
                                          @Param("endTime") String endTime);

    Integer getYearScanInNumByShopIdAndYear(@Param("memberShopId") Integer memberShopId,
                                          @Param("year") String year);

    Integer updateFinishStatusByIds(@Param("idList") List<Integer> idList);

    List<TerminalProtocolModelResp> selectProtocolListByTerminalShopId2(@Param("terminalShopId") Integer terminalShopId);

    List<TerminalProtocolModelResp> selectProtocolListByTerminalShopId3(@Param("terminalShopId") Integer terminalShopId);

    TerminalProtocolModelResp getProtocolById(@Param("id") Integer id);

    /**
     * 查询待处理终端协议
     * @param userIds
     * @return
     */
    List<TerminalProtocolModelVo> todoProtocol(@Param("userIds") List<Integer> userIds);

    List<TerminalProtocolModel> getProtocolTotalDisplaySurface(@Param("id") Integer id);



    List<MaxLevelProtocalResp> getMaxLevelProtocolList(@Param("req") MaxLevelProtocalReq req);

    List<TerminalProtocolModel> getProtocolByShopIds(@Param("shopIds") List<Integer> shopIds);
}
