package com.intelliquor.cloud.shop.terminal.model.resp;

import lombok.Data;

@Data
public class HomepageResp {

    /**
     * 终端采集数量
     * */
    private String terminalGatherNumberByDayAndMonth;

    /**
     * 终端激活数量
     * */
    private String terminalActivateNumberByDayAndMonth;

    /**
     * 终端扫码收货数量
     * */
    private String terminalScanReceivingNumberByDayAndMonth;

    /**
     * 终端扫码收货的金额
     * */
    private String terminalScanReceivingAmountByDayAndMonth;


    /**
     * 是否有待办审批[经理界面需要提示]
     */
    private int approvalNum;

    public String getTerminalGatherNumberByDayAndMonth(){
        if(null == this.terminalGatherNumberByDayAndMonth){
            this.terminalGatherNumberByDayAndMonth = "0/0";
        }
        return terminalGatherNumberByDayAndMonth;
    }

    public String getTerminalActivateNumberByDayAndMonth(){
        if(null == this.terminalActivateNumberByDayAndMonth){
            this.terminalActivateNumberByDayAndMonth = "0/0";
        }
        return terminalActivateNumberByDayAndMonth;
    }

    public String getTerminalScanReceivingNumberByDayAndMonth(){
        if(null == this.terminalScanReceivingNumberByDayAndMonth){
            this.terminalScanReceivingNumberByDayAndMonth = "0/0";
        }
        return terminalScanReceivingNumberByDayAndMonth;
    }

    public String getTerminalScanReceivingAmountByDayAndMonth(){
        if(null == this.terminalScanReceivingAmountByDayAndMonth){
            this.terminalScanReceivingAmountByDayAndMonth = "0/0";
        }
        return terminalScanReceivingAmountByDayAndMonth;
    }
}
