flowchart TD
    A[ConsumerCheckListener.rewardDetailReceiving 处理detailId级别的奖励接收] --> B{检查detailId是否为空}
    B -- 是 --> C[记录日志并返回]
    B -- 否 --> D[调用rewardReceivingService.dealDetailDealerReward 处理经销商奖励]
    D --> E[调用rewardReceivingService.dealDetailTerminalReward 处理终端奖励]
    E --> F[结束处理]
    
    subgraph 经销商奖励处理[处理经销商奖励子流程]
        direction TB
        D1[dealDetailDealerReward detailId] --> D2{检查detailId有效性}
        D2 -- 无效 --> D3[抛出BusinessException异常]
        D2 -- 有效 --> D4[查询TerminalScanDetailModel<br/>根据detailId查询t_terminal_scan_detail_plus表]
        D4 --> D5[查询TerminalScanBalanceCommonModel<br/>根据balanceId查询t_terminal_scan_balance_common表]
        D5 --> D6[获取receiptType<br/>从TerminalScanBalanceCommonModel中获取isOneFlag字段]
        D6 --> D7[调用dealDetailDealer方法]
        
        D7 --> D8[dealDetailDealer方法]
        D8 --> D9{检查是否已处理经销商奖励<br/>判断TerminalScanDetailModel.isDealerReward是否等于1}
        D9 -- 是 --> D10[记录日志并抛出异常]
        D9 -- 否 --> D11{检查是否已删除<br/>判断TerminalScanDetailModel.isDelete是否等于1}
        D11 -- 是 --> D12[记录日志并抛出异常]
        D11 -- 否 --> D13{检查是否已退货<br/>判断TerminalScanDetailModel.returnGoods是否等于1}
        D13 -- 是 --> D14[记录日志并抛出异常]
        D13 -- 否 --> D15{检查qrcode是否为空<br/>判断TerminalScanDetailModel.qrcode是否为空}
        D15 -- 是 --> D16[记录日志并抛出异常]
        D15 -- 否 --> D17{检查是否已发放经销商奖励<br/>查询t_cloud_dealer_reward_record表<br/>条件:detailId=id AND isDelete=0 AND originType=1}
        D17 -- 是 --> D18[记录日志并抛出异常]
        D17 -- 否 --> D19{设置Redis锁防止重复处理<br/>Key: REDIS_AWARD_DEALER_TERMINAL_SCAN+detailId}
        D19 --> D20{是否获取到锁}
        D20 -- 否 --> D21[记录日志并抛出异常]
        D20 -- 是 --> D22[调用webCodeCommonComponent.dealCodeInfo获取码信息<br/>根据qrcode获取码的详细信息]
        
        D22 --> D23{获取码信息是否成功}
        D23 -- 否 --> D24[记录TerminalScanRecordModel并返回]
        D23 -- 是 --> D25[更新TerminalScanDetailModel的码信息<br/>将码信息更新到TerminalScanDetailModel对象中]
        D25 --> D26[更新TerminalScanDetailCodeModel<br/>更新t_terminal_scan_detail_code表中detail_id对应的记录]
        D26 --> D27[查询ShopModel<br/>根据shopId查询t_shop表获取终端信息]
        D27 --> D28{查询ShopModel是否成功}
        D28 -- 否 --> D29[记录日志并抛出异常]
        D28 -- 是 --> D30[查询CloudDealerInfoModel<br/>根据dealerCode查询t_cloud_dealer_info表获取经销商信息]
        D30 --> D31[初始化RewardReason<br/>创建奖励发放原因记录对象]
        D31 --> D32{判断dealerInfo.getAccountType和shopInfo.getIsMember<br/>判断经销商类型和终端是否为会员}
        D32 -- 符合条件 --> D33[调用sendAwardDealerService.sendAwardDealerByOrderNew 发放经销商奖励]
        D32 -- 不符合条件 --> D34[设置rewardReason.isSave=true]
        D33 --> D35[更新TerminalScanDetailModel.setIsDealerReward=1<br/>标记经销商奖励已处理]
        D34 --> D35
        D35 --> D36{rewardReason.isSave是否为true}
        D36 -- 是 --> D37[保存或更新RewardReason<br/>INSERT/UPDATE t_reward_reason表]
        D36 -- 否 --> D38[跳过保存RewardReason]
        D37 --> D38
        D38 --> D39[删除Redis锁]
        D39 --> D40[记录日志]
    end
    
    subgraph 终端奖励处理[处理终端奖励子流程]
        direction TB
        E1[dealDetailTerminalReward detailId] --> E2{检查detailId有效性}
        E2 -- 无效 --> E3[抛出BusinessException异常]
        E2 -- 有效 --> E4[查询TerminalScanDetailModel<br/>根据detailId查询t_terminal_scan_detail_plus表]
        E4 --> E5[查询TerminalScanBalanceCommonModel<br/>根据balanceId查询t_terminal_scan_balance_common表]
        E5 --> E6[获取receiptType<br/>从TerminalScanBalanceCommonModel中获取isOneFlag字段]
        E6 --> E7[初始化virtualAmountTwo为0]
        E7 --> E8[调用dealDetailTerminal方法]
        
        E8 --> E9[dealDetailTerminal方法]
        E9 --> E10{检查是否已处理终端奖励<br/>判断TerminalScanDetailModel.isShopReward是否等于1}
        E10 -- 是 --> E11[记录日志并抛出异常]
        E10 -- 否 --> E12{检查是否已删除<br/>判断TerminalScanDetailModel.isDelete是否等于1}
        E12 -- 是 --> E13[记录日志并抛出异常]
        E12 -- 否 --> E14{检查是否已退货<br/>判断TerminalScanDetailModel.returnGoods是否等于1}
        E14 -- 是 --> E15[记录日志并抛出异常]
        E14 -- 否 --> E16{检查qrcode是否为空<br/>判断TerminalScanDetailModel.qrcode是否为空}
        E16 -- 是 --> E17[记录日志并抛出异常]
        E16 -- 否 --> E18{检查是否已发放终端奖励<br/>查询t_terminal_reward_record_new表<br/>条件:detailId=id AND isDelete=0 AND source=1 AND shopId=shopId}
        E18 -- 是 --> E19[记录日志并抛出异常]
        E18 -- 否 --> E20{检查是否已计算终端积分<br/>查询t_activity_reward_record表<br/>条件:originId=id AND isDelete=0 AND activityType=1}
        E20 -- 是 --> E21[记录日志并抛出异常]
        E20 -- 否 --> E22{设置Redis锁防止重复处理<br/>Key: REDIS_AWARD_TERMINAL_TERMINAL_SCAN+detailId}
        E22 --> E23{是否获取到锁}
        E23 -- 否 --> E24[记录日志并抛出异常]
        E23 -- 是 --> E25[调用webCodeCommonComponent.dealCodeInfo获取码信息<br/>根据qrcode获取码的详细信息]
        
        E25 --> E26{获取码信息是否成功}
        E26 -- 否 --> E27[记录TerminalScanRecordModel并抛出异常]
        E26 -- 是 --> E28[更新TerminalScanDetailModel的码信息<br/>将码信息更新到TerminalScanDetailModel对象中]
        E28 --> E29[查询ShopModel<br/>根据shopId查询t_shop表获取终端信息]
        E29 --> E30{查询ShopModel是否成功}
        E30 -- 否 --> E31[记录日志并抛出异常]
        E30 -- 是 --> E32[查询TerminalShopContractCommonModel<br/>根据memberShopId查询t_terminal_shop_contract_common表获取终端合同]
        E32 --> E33[初始化RewardReason<br/>创建奖励发放原因记录对象]
        E33 --> E34{判断dealerInfo.getAccountType和shopInfo.getIsMember<br/>判断经销商类型和终端是否为会员}
        E34 -- 符合条件 --> E35[调用terminalRewardRecordService.computeShopReward 计算终端奖励]
        E34 -- 不符合条件 --> E36[设置rewardReason.isSave=true]
        E35 --> E37[更新TerminalScanDetailModel.setIsShopReward=1和virtualAmount<br/>标记终端奖励已处理并设置虚拟金额]
        E36 --> E37
        E37 --> E38{rewardReason.isSave是否为true}
        E38 -- 是 --> E39[保存或更新RewardReason<br/>INSERT/UPDATE t_reward_reason表]
        E38 -- 否 --> E40[跳过保存RewardReason]
        E39 --> E40
        E40 --> E41[更新TerminalScanDetailModel]
        E41 --> E42[调用integralSyncService.send同步积分]
        E42 --> E43[删除Redis锁]
        E43 --> E44[记录日志]
    end
    
    subgraph 经销商奖励详细处理[sendAwardDealerService.sendAwardDealerByOrderNew详细处理]
        direction TB
        F1[sendAwardDealerService.sendAwardDealerByOrderNew 发放经销商奖励] --> F2[获取dealerOutTime<br/>从码信息中获取经销商出库时间]
        F2 --> F3[获取收货类型名称<br/>根据receiptType获取收货类型:扫码收货/出库收货/一键收货/经理代收]
        F3 --> F4[获取订单信息<br/>从TerminalScanDetailModel中获取订单相关信息]
        F4 --> F5[初始化ExtendDataBean<br/>封装扩展数据:商品名称、数量、终端名称等]
        F5 --> F6{检查码信息列表是否为空}
        F6 -- 是 --> F7[记录日志并返回]
        F6 -- 否 --> F8[获取经销商编码<br/>从码信息中获取dealerCode]
        F8 --> F9{经销商编码是否为空}
        F9 -- 是 --> F10[记录日志并返回]
        F9 -- 否 --> F11[查询经销商信息<br/>SELECT * FROM t_cloud_dealer_info WHERE dealerCode=dealerCode]
        F11 --> F12[按商品分组码信息<br/>将码信息按商品编码进行分组]
        F12 --> F13[遍历商品分组]
        F13 --> F14[计算商品数量<br/>统计当前商品的总数量]
        F14 --> F15[查询终端合同<br/>SELECT * FROM t_terminal_shop_contract_common WHERE memberShopId=shopId ORDER BY id DESC LIMIT 1]
        F15 --> F16{合同是否存在}
        F16 -- 否 --> F17[记录日志并返回]
        F16 -- 是 --> F18[查询合同信息<br/>SELECT * FROM t_dealer_contract_rel WHERE contractCode=contractCode]
        F18 --> F19[设置经销商类型<br/>从合同信息中获取经销商类型]
        F19 --> F20{检查重复处理<br/>使用Redis锁防止重复处理}
        F20 --> F21{检查是否已发放奖励<br/>SELECT * FROM t_activity_reward_record WHERE originId=detailId AND activityType=1 AND eventType=4 AND originTable=t_terminal_scan_detail AND isDelete=0 LIMIT 1}
        F21 --> F22{是否已发放奖励}
        F22 -- 是 --> F23[记录日志并返回]
        F22 -- 否 --> F24{检查是否为餐饮终端}
        F24 -- 是 --> F25[记录日志并返回 禁用餐饮终端动销奖励]
        F24 -- 否 --> F26[查询经销商活动]
        F26 --> F27[筛选匹配的活动]
        F27 --> F28[遍历活动列表]
        F28 --> F29[检查活动条件]
        F29 --> F30{条件是否满足}
        F30 -- 否 --> F31[继续下一个活动]
        F30 -- 是 --> F32[计算奖励金额<br/>活动奖励金额*商品数量]
        F32 --> F33[判断其他规则]
        F33 --> F34{是否满足其他规则}
        F34 -- 是 --> F35[插入活动奖励记录<br/>INSERT INTO t_activity_reward_record]
        F34 -- 否 --> F36[插入异常奖励记录<br/>INSERT INTO t_activity_reward_exception_record]
        F35 --> F37[更新扫码明细活动id]
        F36 --> F37
        F37 --> F38[结束]
    end
    
    subgraph 终端奖励详细处理[terminalRewardRecordService.computeShopReward详细处理]
        direction TB
        G1[terminalRewardRecordService.computeShopReward 计算终端奖励] --> G2[获取dealerOutTime<br/>从码信息中获取经销商出库时间]
        G2 --> G3[获取收货类型名称<br/>根据receiptType获取收货类型:扫码收货/出库收货/一键收货/经理代收]
        G3 --> G4[获取订单信息<br/>从TerminalScanDetailModel中获取订单相关信息]
        G4 --> G5[初始化ExtendDataBean<br/>封装扩展数据:商品名称、数量、终端名称等]
        G5 --> G6{检查码信息列表是否为空}
        G6 -- 是 --> G7[记录日志并返回]
        G6 -- 否 --> G8[获取商品编码和数量]
        G8 --> G9[查询终端店类型]
        G9 --> G10[查询可参与的活动<br/>SELECT * FROM t_reward_activity_common WHERE goodsCode=? AND accountType=? AND activityTime<=? AND companyId=? AND rewardType=1]
        G10 --> G11[按活动子类型分组]
        G11 --> G12[遍历活动子类型]
        G12 --> G13[筛选活动列表]
        G13 --> G14{活动列表是否为空}
        G14 -- 是 --> G15[记录日志并继续]
        G14 -- 否 --> G16[遍历活动列表]
        G16 --> G17[设置查询参数]
        G17 --> G18[查询活动范围]
        G18 --> G19{活动范围是否匹配}
        G19 -- 否 --> G20[继续下一个活动]
        G19 -- 是 --> G21{检查出库时间限制}
        G21 --> G22{是否开启会员奖励}
        G22 -- 否 --> G23[继续下一个活动]
        G22 -- 是 --> G24{检查终端类型是否匹配}
        G24 --> G25{是否满足条件}
        G25 -- 否 --> G26[继续下一个活动]
        G25 -- 是 --> G27{检查活动限制}
        G27 --> G28{是否满足限制}
        G28 -- 否 --> G29[继续下一个活动]
        G28 -- 是 --> G30[检查额外条件]
        G30 --> G31{是否满足额外条件}
        G31 -- 否 --> G32[继续下一个活动]
        G31 -- 是 --> G33{检查奖励金额是否为0}
        G33 --> G34{奖励金额是否为0}
        G34 -- 是 --> G35[继续下一个活动]
        G34 -- 否 --> G36[设置备注信息]
        G36 --> G37{检查重复执行}
        G37 --> G38{检查是否已发放奖励<br/>SELECT * FROM t_activity_reward_record WHERE originId=detailId AND activityType=1 AND eventType=? AND originTable=t_terminal_scan_detail AND isDelete=0 AND JSON查询extend_data字段}
        G38 --> G39{是否已发放奖励}
        G39 -- 是 --> G40[跳出循环]
        G39 -- 否 --> G41[判断其他规则]
        G41 --> G42{是否满足其他规则}
        G42 -- 是 --> G43[插入活动奖励记录<br/>INSERT INTO t_activity_reward_record]
        G42 -- 否 --> G44[插入异常奖励记录<br/>INSERT INTO t_activity_reward_exception_record]
        G43 --> G45[更新扫码明细活动id]
        G44 --> G45
        G45 --> G46[结束]
    end
    
    subgraph 数据库查询详情[数据库查询详情]
        direction TB
        DB1[查询TerminalScanDetailModel<br/>SELECT * FROM t_terminal_scan_detail_plus WHERE id=detailId<br/>获取扫码收货详情信息]
        DB2[查询TerminalScanBalanceCommonModel<br/>SELECT * FROM t_terminal_scan_balance_common WHERE id=balanceId<br/>获取扫码收货主表信息]
        DB3[查询ShopModel<br/>SELECT * FROM t_shop WHERE id=shopId<br/>获取终端店铺信息]
        DB4[查询CloudDealerInfoModel<br/>SELECT * FROM t_cloud_dealer_info WHERE dealerCode=shop.dealerCode<br/>获取经销商信息]
        DB5[查询TerminalShopContractCommonModel<br/>SELECT * FROM t_terminal_shop_contract_common WHERE memberShopId=shopId<br/>获取终端合同信息]
        DB6[检查是否已发放经销商奖励<br/>SELECT * FROM t_cloud_dealer_reward_record WHERE detailId=id AND isDelete=0 AND originType=1 LIMIT 1<br/>检查经销商奖励是否已发放]
        DB7[检查是否已发放终端奖励<br/>SELECT * FROM t_terminal_reward_record_new WHERE detailId=id AND isDelete=0 AND source=1 AND shopId=shopId LIMIT 1<br/>检查终端奖励是否已发放]
        DB8[检查是否已计算终端积分<br/>SELECT * FROM t_activity_reward_record WHERE originId=id AND isDelete=0 AND activityType=1 LIMIT 1<br/>检查终端积分是否已计算]
        DB9[保存或更新RewardReason<br/>INSERT/UPDATE t_reward_reason<br/>保存奖励发放原因记录]
    end
    
    D --> 经销商奖励处理
    E --> 终端奖励处理
    D33 --> 经销商奖励详细处理
    E35 --> 终端奖励详细处理
    
    DB1 -.-> D4
    DB2 -.-> D5
    DB3 -.-> D27
    DB3 -.-> E29
    DB4 -.-> D30
    DB5 -.-> E32
    DB6 -.-> D17
    DB7 -.-> E18
    DB8 -.-> E20
    DB9 -.-> D37