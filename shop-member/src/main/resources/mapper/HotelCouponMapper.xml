<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.intelliquor.cloud.shop.member.dao.HotelCouponDao">

    <sql id="baseColumn">
        id,
        name,
        hotel_id,
        coupon_id,
        start_time,
        end_time,
        status,
        create_time,
        create_user_id,
        is_delete,
        coupon_ratio
    </sql>

    <resultMap id="baseResultMap" type="com.intelliquor.cloud.shop.member.model.HotelCouponModel">
        <id property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="hotelId" column="hotel_id"/>
        <result property="hotelName" column="hotel_name"/>
        <result property="hotelCode" column="hotel_code"/>
        <result property="couponId" column="coupon_id"/>
        <result property="startTime" column="start_time"/>
        <result property="endTime" column="end_time"/>
        <result property="status" column="status"/>
        <result property="createTime" column="create_time"/>
        <result property="createUserId" column="create_user_id"/>
        <result property="isDelete" column="is_delete"/>
        <result property="goodsCode" column="goods_code"/>
        <result property="goodsName" column="goods_name"/>
        <result property="couponName" column="coupon_name"/>
        <result property="amount" column="amount"/>
        <result property="couponRatio" column="coupon_ratio"/>
        <result property="totalAmount" column="total_amount"/>
        <result property="totalCount" column="total_count"/>
        <result property="residueAmount" column="residue_amount"/>
        <result property="residueCount" column="residue_count"/>
        <result property="usedAmount" column="used_amount"/>
        <result property="usedCount" column="used_count"/>
        <result property="unusedAmount" column="unused_amount"/>
        <result property="unusedCount" column="unused_count"/>
    </resultMap>

    <sql id="selectiveWhere">
        <where>
            <if test="name != null and name != ''">
                AND b.name like CONCAT('%',#{name},'%')
            </if>
            <if test="hotelName != null and hotelName != ''">
                AND e.name like CONCAT('%',#{hotelName},'%')
            </if>
            <if test="hotelCode != null and hotelCode != ''">
                AND e.code like CONCAT('%',#{hotelCode},'%')
            </if>
            <if test="status != null and status != ''">
                AND b.status = #{status}
            </if>
            <if test="searchDateBegin!=null and searchDateBegin !=''">
                and b.end_time >= str_to_date(CONCAT(#{searchDateBegin} , ' 00:00:00'), '%Y-%m-%d %H:%i:%s')
            </if>
            <if test="searchDateEnd!=null and searchDateEnd !=''">
                and b.start_time &lt;= str_to_date(CONCAT(#{searchDateEnd} , ' 23:59:59'), '%Y-%m-%d %H:%i:%s')
            </if>
            <if test="1==1">
                AND b.is_delete = 0
            </if>
        </where>
    </sql>


    <select id="selectListByPage" resultMap="baseResultMap">
        SELECT
        b. ID,
        b. NAME,
        b.hotel_id,
        e.name hotel_name,
        e.code hotel_code,
        b.coupon_id,
        b.start_time,
        b.end_time,
        b.status,
        b.create_time,
        b.create_user_id,
        b.is_delete,
        d.goods_code,
        d.goods_name,
        d.coupon_name,
        d.amount,
        b.coupon_ratio,
        ifnull(f.total_amount,0) total_amount,
        ifnull(f.total_count,0) total_count,
        ifnull(f.residue_amount,0) residue_amount,
        ifnull(f.residue_count,0) residue_count,
        ifnull(f.unused_amount,0) unused_amount,
        ifnull(f.unused_count,0) unused_count,
        ifnull(f.used_amount,0) used_amount,
        ifnull(f.used_count,0) used_count
        FROM t_member_hotel_coupon b
        LEFT JOIN t_member_coupon_template d ON b.coupon_id = d. ID
        LEFT JOIN t_member_hotel e ON b.hotel_id = e.id
        LEFT JOIN (
        SELECT
        b1.activity_id,
        b1.total_amount,
        b1.total_count,
        b2.residue_amount,
        b2.residue_count,
        b3.unused_amount,
        b3.unused_count,
        b4.used_amount,
        b4.used_count
        FROM
        (
        SELECT
        activity_id,
        SUM(coupon_amount) total_amount,
        COUNT(1) total_count
        FROM
        t_member_hotel_coupon_details
        WHERE
        is_delete = 0
        GROUP BY
        activity_id
        ) b1
        LEFT JOIN (
        SELECT
        activity_id,
        SUM(coupon_amount) residue_amount,
        COUNT(1) residue_count
        FROM
        t_member_hotel_coupon_details
        WHERE
        is_delete = 0
        AND status = 0
        GROUP BY
        activity_id
        ) b2 ON b1.activity_id = b2.activity_id
        LEFT JOIN (
        SELECT
        activity_id,
        SUM(coupon_amount) unused_amount,
        COUNT(1) unused_count
        FROM
        t_member_hotel_coupon_details
        WHERE
        is_delete = 0
        AND status = 1
        GROUP BY
        activity_id
        ) b3 ON b1.activity_id = b3.activity_id
        LEFT JOIN (
        SELECT
        activity_id,
        SUM(coupon_amount) used_amount,
        COUNT(1) used_count
        FROM
        t_member_hotel_coupon_details
        WHERE
        is_delete = 0
        AND status = 2
        GROUP BY
        activity_id
        ) b4 ON b1.activity_id = b4.activity_id
        ) f ON b.id = f.activity_id
        <include refid="selectiveWhere"/>
        ORDER BY b.id DESC
    </select>

    <select id="selectListByCount" resultType="long">
        SELECT COUNT(1)
        FROM t_member_hotel_coupon b
        LEFT JOIN t_member_coupon_template d ON b.coupon_id = d. ID
        LEFT JOIN t_member_hotel e ON b.hotel_id = e.id
        <include refid="selectiveWhere"/>
    </select>

    <insert id="insert" parameterType="com.intelliquor.cloud.shop.member.model.HotelCouponModel">
        INSERT INTO t_member_hotel_coupon(
            name,
            hotel_id,
            coupon_id,
            start_time,
            end_time,
            status,
            create_time,
            create_user_id,
            is_delete,
            coupon_ratio
        )VALUES(
            #{name},
            #{hotelId},
            #{couponId},
            #{startTime},
            #{endTime},
            #{status},
            #{createTime},
            #{createUserId},
            #{isDelete},
            #{couponRatio}
        )
    </insert>

    <update id="update" parameterType="com.intelliquor.cloud.shop.member.model.HotelCouponModel">
        UPDATE t_member_hotel_coupon
        <set>
            <if test="name != null and name !=''  ">
                name = #{name},
            </if>
            <if test="hotelId != null and hotelId !=''  ">
                hotel_id = #{hotelId},
            </if>
            <if test="couponId != null and couponId !=''  ">
                coupon_id = #{couponId},
            </if>
            <if test="startTime != null and startTime !=''  ">
                start_time = #{startTime},
            </if>
            <if test="endTime != null and endTime !=''  ">
                end_time = #{endTime},
            </if>
            <if test="status != null and status !=''  ">
                status = #{status},
            </if>
            <if test="createTime != null and createTime !=''  ">
                create_time = #{createTime},
            </if>
            <if test="createUserId != null and createUserId !=''  ">
                create_user_id = #{createUserId},
            </if>
            <if test="isDelete != null and isDelete !=''  ">
                is_delete = #{isDelete},
            </if>
            <if test="couponRatio != null ">
                coupon_ratio = #{couponRatio},
            </if>
        </set>
        WHERE id = #{id}
    </update>

    <delete id="delete" parameterType="integer">
        DELETE FROM t_member_hotel_coupon
        WHERE id = #{id}
    </delete>

    <select id="getById" resultMap="baseResultMap">
        SELECT
        <include refid="baseColumn"/>
        FROM t_member_hotel_coupon
        WHERE id = #{id}
    </select>

    <select id="getByHotelAndGoodsCode" resultMap="baseResultMap">
        SELECT
            b. ID,
            b. NAME,
            b.hotel_id,
            b.coupon_id,
            b.start_time,
            b.end_time,
            b.status,
            b.create_time,
            b.create_user_id,
            b.is_delete,
            d.goods_code,
            d.goods_name,
            d.coupon_name,
            d.amount,
            b.coupon_ratio
        FROM
            t_member_hotel_coupon b
        LEFT JOIN t_member_coupon_template d ON b.coupon_id = d. ID
        WHERE
            b.hotel_id = #{hotelId}
        AND d.goods_code = #{goodsCode}
        AND b.is_delete = 0 and b.status = '0'
    </select>

    <select id="selectHotelCouponList" resultMap="baseResultMap">
        SELECT
        b. ID,
        b. NAME,
        b.hotel_id,
        e.name hotel_name,
        e.code hotel_code,
        b.coupon_id,
        b.start_time,
        b.end_time,
        b.status,
        b.create_time,
        b.create_user_id,
        b.is_delete,
        d.goods_code,
        d.goods_name,
        d.coupon_name,
        d.amount,
        b.coupon_ratio
        FROM t_member_hotel_coupon b
        LEFT JOIN t_member_coupon_template d ON b.coupon_id = d. ID
        LEFT JOIN t_member_hotel e ON b.hotel_id = e.id
        <where>
            <if test="name != null and name != ''">
                AND b.name like CONCAT('%',#{name},'%')
            </if>
            <if test="hotelName != null and hotelName != ''">
                AND e.name like CONCAT('%',#{hotelName},'%')
            </if>
            <if test="hotelId != null and hotelId != ''">
                AND b.hotel_id = #{hotelId}
            </if>
            <if test="couponId != null and couponId != ''">
                AND b.coupon_id = #{couponId}
            </if>
            <if test="hotelCode != null and hotelCode != ''">
                AND e.code like CONCAT('%',#{hotelCode},'%')
            </if>
            <if test="status != null and status != ''">
                AND b.status = #{status}
            </if>
            <if test="searchDateBegin!=null and searchDateBegin !=''">
                and b.end_time >= str_to_date(CONCAT(#{searchDateBegin} ,' 00:00:00'), '%Y-%m-%d %H:%i:%s')

            </if>
            <if test="searchDateEnd!=null and searchDateEnd !=''">
                and b.start_time &lt;= str_to_date(CONCAT(#{searchDateEnd} ,' 23:59:59'), '%Y-%m-%d %H:%i:%s')
            </if>
            <if test="1==1">
                AND b.is_delete = 0
            </if>
        </where>
    </select>
</mapper>