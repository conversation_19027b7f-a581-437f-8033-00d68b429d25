package com.intelliquor.cloud.shop.member.service;


import com.intelliquor.cloud.shop.common.utils.TimeUtilis;
import com.intelliquor.cloud.shop.member.dao.CustomerCapcodeDetailDao;
import com.intelliquor.cloud.shop.member.model.CustomerCapcodeDetailModel;
import com.intelliquor.cloud.shop.system.service.WebSrvComponent;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Service
public class CustomerCapcodeDetailServiceImpl {

    @Autowired
    private CustomerCapcodeDetailDao customerCapcodeDetailDao;
    @Autowired
    private HotelCouponDetailsService hotelCouponDetailsService;

    /**
     * 插入数据
     *
     * @param qrcode:箱码或者是平码
     * @param type           1:箱码 2:平码
     * @param effective      1:有效 0无效
     * @param qrCodeMap      扫码获取的信息
     */
    @Async
    public void insert(String qrcode, String type, String effective, Map<String, String> qrCodeMap) throws Exception {
        List<Map<String, String>> list = WebSrvComponent.getCapCode(qrcode, type);
        for (int i = 0; i < list.size(); i++) {
            Map<String, String> map = list.get(i);
            String code_ping = map.get("code_ping");
            String code_xiang = map.get("code_xiang");
            String intercode = map.get("intercode");
            String outercode = map.get("outercode");
            String intTime = map.get("IntTime");
            intTime = TimeUtilis.dateFormat(intTime, "yyyy-MM-dd HH:mm:ss");
            CustomerCapcodeDetailModel customerCapcodeDetailModel = new CustomerCapcodeDetailModel();
            customerCapcodeDetailModel.setQrcode(code_xiang);
            customerCapcodeDetailModel.setCodein(code_ping);
            customerCapcodeDetailModel.setIntercode(intercode);
            customerCapcodeDetailModel.setOutercode(outercode);
            customerCapcodeDetailModel.setInttime(intTime);
            customerCapcodeDetailModel.setGoodsCode(qrCodeMap.get("WBProductNo"));
            customerCapcodeDetailModel.setCreateTime(TimeUtilis.getCurrentDateStringAll());
            List<CustomerCapcodeDetailModel> modelList = customerCapcodeDetailDao.selectList(code_ping);
            if (modelList == null || modelList.size() == 0) {
                customerCapcodeDetailDao.insert(customerCapcodeDetailModel);
//                //扫码有效,生成优惠券
//                if("1".equals(effective)) {
//                    //生成优惠券
//                    HotelCouponDetailsModel param = new HotelCouponDetailsModel();
//                    param.setGoodsCode(qrCodeMap.get("WBProductNo"));
//                    param.setCustomerId(Long.valueOf(user.getId()));
//                    param.setOutQrcode(code_ping);
//                    param.setInnerQrcode(intercode);
//                    param.setBoxOutQrcode(qrcode);
//                    hotelCouponDetailsService.generateCoupon(param);
//                }
            }
        }
    }
}
