package com.intelliquor.cloud.shop.member.service;

import cn.binarywang.wx.miniapp.bean.WxMaJscode2SessionResult;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Maps;
import com.intelliquor.cloud.shop.common.config.WxOpenServiceConfig;
import com.intelliquor.cloud.shop.common.constant.CommonConstant;
import com.intelliquor.cloud.shop.common.dao.*;
import com.intelliquor.cloud.shop.common.entity.Response;
import com.intelliquor.cloud.shop.common.enums.AccountTypeEnum;
import com.intelliquor.cloud.shop.common.enums.DeleteFlagEnum;
import com.intelliquor.cloud.shop.common.enums.MemberShopStatusEnum;
import com.intelliquor.cloud.shop.common.enums.TerminalAuditStatusEnum;
import com.intelliquor.cloud.shop.common.exception.BusinessException;
import com.intelliquor.cloud.shop.common.model.*;
import com.intelliquor.cloud.shop.common.model.resp.MemberShopResp;
import com.intelliquor.cloud.shop.common.model.resp.TerminalShopResp;
import com.intelliquor.cloud.shop.common.service.ITerminalShopCommonService;
import com.intelliquor.cloud.shop.common.service.TerminalShopInfoScheduleCommonService;
import com.intelliquor.cloud.shop.common.utils.AesUtil;
import com.intelliquor.cloud.shop.common.utils.HttpUtils;
import com.intelliquor.cloud.shop.common.utils.ObjectUtil;
import com.intelliquor.cloud.shop.common.utils.SearchUtil;
import com.intelliquor.cloud.shop.member.model.HotelInfoModel;
import com.intelliquor.cloud.shop.common.model.resp.CreditAmount;
import com.intelliquor.cloud.shop.system.service.DataCenterService;
import com.intelliquor.cloud.shop.system.service.PayInfoService;
import com.intelliquor.cloud.shop.system.util.RedisUtil;
import com.intelliquor.cloud.shop.system.util.SignUtils;
import io.swagger.models.auth.In;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.open.api.WxOpenMaService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import org.springframework.web.client.RestTemplate;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 描述：终端人员管理 服务实现层
 *
 * <AUTHOR>
 * @date 2019-06-24
 */
@Slf4j
@Service
public class ShopUserService {

    @Value("${APP_ID_NEW}")
    private String appIdNew;

    @Value("${APP_SECRET_NEW}")
    private String appSecretNew;

    @Value("${SESSION_KEY_URL}")
    private String sessionKeyUrl;

    @Autowired
    private TerminalShopInfoScheduleCommonDao terminalShopInfoScheduleCommonDao;

    @Autowired
    private MemberShopCommonDao memberShopCommonDao;

    @Autowired
    private ShopUserDao shopUserDao;

    @Autowired
    private ShopDao shopDao;

    @Autowired
    private TerminalShopCommonDao terminalShopCommonDao;

    @Autowired
    private HotelInfoService hotelInfoService;

    @Autowired
    private DataCenterService dataCenterService;

    @Autowired
    private PayInfoService payInfoService;

    @Autowired
    protected WxOpenServiceConfig wxOpenService;

    @Autowired
    private WxOpenServiceConfig wxOpenServiceConfig;

    @Autowired
    private RestTemplate restTemplate;

    @Value("${wechat.getPhoneNumberUrl:''}")
    private String getPhoneNumberUrl;

    @Autowired
    private RedisUtil redisUtil;
    @Value("${terminal_sign_key}")
    private String md5Key;

    @Autowired
    private TerminalShopInfoScheduleCommonService terminalShopInfoScheduleCommonService;

    @Autowired
    private ITerminalShopCommonService terminalShopCommonService;


    /**
     * 查询数据
     *
     * @return
     */
    public List<ShopUserModel> selectList(Map<String, Object> searchMap) {
        return shopUserDao.selectList(searchMap);
    }

    /**
     * 小程序门店管理列表
     *
     * @param searchMap
     * @return
     */
    public List<ShopUserModel> selectShopList(Map<String, Object> searchMap) {
        return shopUserDao.selectShopList(searchMap);
    }


    /**
     * 新增数据
     *
     * @param model
     */
    public Response<String> insert(ShopUserModel model) {
        ShopUserModel shop = new ShopUserModel();
        shop.setPhone(model.getPhone());
        shop.setCompanyId(model.getCompanyId());
        List<ShopUserModel> list = shopUserDao.selectList(SearchUtil.getSearch(shop));
        for (ShopUserModel user : list) {
            // 同一个店手机号相同时报错
            if (user.getPhone().equals(model.getPhone()) && user.getShopId().equals(model.getShopId())) {
                return Response.fail("手机号已存在");
            }
            // 处理openid
            if (ObjectUtil.isNotEmpty(user.getOpenId())) {
                model.setOpenId(user.getOpenId());
            }
        }

        shopUserDao.insert(model);
        return Response.ok("保存成功");
    }

    /**
     * 新增数据
     *
     * @param model
     */
    public Integer create(ShopUserModel model) {
        return shopUserDao.insert(model);
    }

    /**
     * 酒店新营销新增员工数据
     *
     * @param model
     */
    public Response<String> dealHotelUser(ShopUserModel model) {
        ShopUserModel shop = new ShopUserModel();
        shop.setPhone(model.getPhone());
        List<ShopUserModel> list = shopUserDao.selectList(SearchUtil.getSearch(shop));
        if (!CollectionUtils.isEmpty(list)) {
            return Response.fail("手机号已存在");
        } else {
            if (null == model.getId()) {
                model.setPrimaryAccount(0);
                shopUserDao.insert(model);
                return Response.ok("保存成功");
            } else {
                HotelInfoModel hotel = hotelInfoService.getById(model.getHotelId());
                ShopModel shopModel = shopDao.getByInvitationCode(hotel.getInvitationCode());
                if (null != shopModel) {
                    model.setShopId(shopModel.getId());
                }
                shopUserDao.update(model);
                return Response.ok("更新成功");
            }

        }

    }

    /**
     * 更新统称
     * @param shopUserModel
     */
    public void updateGenericTerm(ShopUserModel shopUserModel){
        shopUserDao.update(shopUserModel);
    }

    /**
     * 更新数据
     *
     * @param model
     */
    @Transactional(rollbackFor = Exception.class)
    public Response<String> update(ShopUserModel model) {
        ShopUserModel shop = new ShopUserModel();
        shop.setId(model.getId());
        shop.setPhone(model.getPhone());
        shop.setCompanyId(model.getCompanyId());
        List<ShopUserModel> list = shopUserDao.selectList(SearchUtil.getSearch(shop));
        for (ShopUserModel user : list) {
            // 同一个店手机号相同,但是id不同时报错
            if (user.getPhone().equals(model.getPhone())
                    && user.getShopId().equals(model.getShopId())
                    && !user.getId().equals(model.getId())
            ) {
                return Response.fail("手机号已存在");
            }
        }

        shopUserDao.update(model);

        // 更细当前手机号下店的用户openId
        // 查询当前登录人手机号下的店铺信息
        Map<String, Object> map = Maps.newHashMap();
        map.put("phone", model.getPhone());
        map.put("companyId", model.getCompanyId());
        List<ShopUserModel> shopUserModels = shopUserDao.selectShopList(map);

        // 如果openId查询为空，重新查询一次用户信息，赋值到openId中
        if (StringUtils.isEmpty(model.getOpenId())) {
            ShopUserModel byId = shopUserDao.getById(model.getId());
            if (ObjectUtil.isNotEmpty(byId)) {
                model.setOpenId(byId.getOpenId());
            }
        }

        // 为了防止退出登录后，/getShopByOpenid接口根据openId获取店列表找不到
        shopUserDao.updateBatchOpenId(model.getOpenId(), shopUserModels);

        return Response.ok("保存成功");

    }

    /**
     * 更新头像
     *
     * @param id
     * @param photo
     */
    public void updatePhoto(Integer id, String photo) {
        ShopUserModel user = shopUserDao.getById(id);
        user.setPhoto(photo);
        shopUserDao.update(user);
    }

    /**
     * 更新联系人
     *
     * @param id
     * @param linkman
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateLinkman(Integer id, String linkman) {
        ShopUserModel user = shopUserDao.getById(id);
        user.setName(linkman);
        shopUserDao.update(user);
        //如果是主账号需要更新终端信息
        if (Integer.valueOf(1).equals(user.getPrimaryAccount())) {
            ShopModel shop = shopDao.getById(user.getShopId());
            String old = shop.getLinkman();
            shop.setLinkman(linkman);
            shop.setUpdateTime(new Date());
            shopDao.update(shop);
            //发送信息到智盈
            dataCenterService.sendChangeContactsInfo(shop.getId(), old, linkman, shop.getCompanyId());
        }
    }

    /**
     * 更新联系电话
     *
     * @param id
     * @param phone
     */
    @Transactional(rollbackFor = Exception.class)
    public void updatePhone(Integer id, String phone) {
        ShopUserModel user = shopUserDao.getById(id);
        user.setPhone(phone);
        shopUserDao.update(user);
        //如果是主账号需要更新终端信息
        if (Integer.valueOf(1).equals(user.getPrimaryAccount())) {
            ShopModel shop = shopDao.getById(user.getShopId());
            String old = shop.getLinkphone();
            shop.setLinkphone(phone);
            shop.setUpdateTime(new Date());
            shopDao.update(shop);
            //发送信息到智盈
            dataCenterService.sendMobilePhoneChangeInfo(shop.getId(), old, phone, shop.getCompanyId());
        }
    }

    /**
     * 更新名称
     *
     * @param id
     * @param name
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateName(Integer id, String name) {
        ShopUserModel user = shopUserDao.getById(id);
        //如果是主账号需要更新终端信息
        if (Integer.valueOf(1).equals(user.getPrimaryAccount())) {
            ShopModel shop = shopDao.getById(user.getShopId());
            shop.setName(name);
            shop.setUpdateTime(new Date());
            shopDao.update(shop);
            //发送信息到智盈 TODO
        }
    }

    /**
     * 更新微信号
     *
     * @param id
     * @param wechatNum
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateWechatNum(Integer id, String wechatNum) {
        ShopUserModel user = shopUserDao.getById(id);
        // 更新主账号信息
        if (Integer.valueOf(1).equals(user.getPrimaryAccount())) {
            user.setWechatNum(wechatNum);
            shopUserDao.update(user);
        } else {
            ShopUserModel primaryAccount = this.getPrimaryAccountByShopId(user.getShopId());
            primaryAccount.setWechatNum(wechatNum);
            shopUserDao.update(primaryAccount);
        }
        // 发送信息到智盈 TODO
    }

    /**
     * 更新地址
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateAddress(Integer userId, ShopModel model) {
        ShopUserModel user = shopUserDao.getById(userId);
        //如果是主账号需要更新终端地址信息
        if (Integer.valueOf(1).equals(user.getPrimaryAccount())) {
            ShopModel shop = shopDao.getById(user.getShopId());
            shop.setProvince(model.getProvince());
            shop.setCity(model.getCity());
            shop.setDistrict(model.getDistrict());
            shop.setTownship(model.getTownship());
            shop.setAddress(model.getAddress());
            shop.setLatitude(model.getLatitude());
            shop.setLongitude(model.getLongitude());
            shop.setUpdateTime(new Date());
            shopDao.update(shop);
            //发送信息到智盈 TODO
        }
    }

    /**
     * 更新状态
     *
     * @param id
     * @param status
     */
    public void changeStatus(Integer id, Integer status) {
        ShopUserModel user = shopUserDao.getById(id);
        user.setStatus(status);
        shopUserDao.update(user);
    }

    /**
     * 更新数据
     *
     * @param model
     */
    public void updateDrawMoney(ShopUserModel model) {
        ShopUserModel shop = new ShopUserModel();
        shop.setId(model.getId());
        shop.setAmount(model.getAmount());
        shopUserDao.update(model);
    }

    /**
     * 删除数据
     *
     * @param id
     */
    public void delete(Integer id) {
        shopUserDao.delete(id);
    }

    /**
     * 根据ID查询数据
     *
     * @param id
     */
    public ShopUserModel getById(Integer id) {
        return shopUserDao.getById(id);
    }

    /**
     * 根据ID查询数据
     *
     * @param userId 当前用户id
     * @param shopId 当前终端店id
     */
    public ShopUserModel loadById(Integer userId, Integer shopId) {
        return shopUserDao.loadById(userId, shopId);
    }

    /**
     * 根据手机号查询
     *
     * @param phone
     * @return
     */
    public List<ShopUserModel> loadByPhone(String phone) {
        return shopUserDao.loadByPhone(phone);
    }

    /**
     * 根据手机号查询
     *
     * @param phone
     * @return
     */
    public List<ShopUserModel> loadByPhoneAndCompanyId(String phone, Integer companyId) {
        return shopUserDao.loadByPhoneAndCompanyId(phone, companyId);
    }

    public List<ShopUserModel> getInfoByOpenId(String phone, String openId, Integer companyId) {
        return shopUserDao.getInfoByOpenId(phone, openId, companyId);
    }

    public int updateById(Integer id) {
        return shopUserDao.updateById(id);
    }

    /**
     * 根据shopId删除数据
     *
     * @param shopId
     */
    public void deleteByShopId(Integer shopId) {
        shopUserDao.deleteByShopId(shopId);
    }


    /**
     * 根据shopId查询数据
     *
     * @return
     */
    public List<ShopUserModel> getByShopId(Map<String, Object> searchMap) {
        return shopUserDao.getByShopId(searchMap);
    }

    /**
     * 从微信查询openIdxu
     *
     * @param code
     * @return
     */
    @Deprecated
    public String getOpenId(String code, String appId, String secret) {
        //组装参数
        Map<String, String> parameters = new HashMap<>();
        parameters.put("appid", appId);
        parameters.put("secret", secret);
        parameters.put("js_code", code);
        parameters.put("grant_type", "authorization_code");
        //向微信请求
        String result = HttpUtils.sendGet(sessionKeyUrl, parameters);
        System.out.println(result);
        JSONObject object = JSONObject.parseObject(result);
        String openid = object.getString("openid");
        return openid;
    }

    /**
     * 从微信查询openIdxu
     *
     * @param code
     * @return
     */
    public String getOpenId(String code, String appId) {
        String openid = "";
        try {
            WxOpenMaService wxOpenMaService = wxOpenService.getWxOpenComponentService().getWxMaServiceByAppid(appId);
            WxMaJscode2SessionResult result = wxOpenMaService.jsCode2SessionInfo(code);
            log.info("获取OpenId结果：{}", JSONObject.toJSONString(result));
            openid = result.getOpenid();
        } catch (Exception e) {
            log.error("获取openid失败，原因", e);
        }
        return openid;
    }

    /**
     * 从微信查询openIdxu
     *
     * @param code
     * @return
     */
    @Deprecated
    public String getSessionKey(String code, String appId, String secret) {
        //组装参数
        Map<String, String> parameters = new HashMap<>();
        parameters.put("appid", appId);
        parameters.put("secret", secret);
        parameters.put("js_code", code);
        parameters.put("grant_type", "authorization_code");
        //向微信请求
        String result = HttpUtils.sendGet(sessionKeyUrl, parameters);
        System.out.println(result);
        JSONObject object = JSONObject.parseObject(result);
        String sessionKey = object.getString("session_key");
        return sessionKey;
    }

    /**
     * 从微信查询openIdxu
     *
     * @param code
     * @return
     */
    public String getSessionKey(String code, String appId) {
        String sessionKey = "";
        try {
            WxOpenMaService wxOpenMaService = wxOpenService.getWxOpenComponentService().getWxMaServiceByAppid(appId);
            WxMaJscode2SessionResult result = wxOpenMaService.jsCode2SessionInfo(code);
            log.info("获取sessionKey结果：{}", JSONObject.toJSONString(result));
            sessionKey = result.getSessionKey();
        } catch (Exception e) {
            log.error("获取sessionKey失败，原因", e);
        }
        return sessionKey;
    }

    /**
     * 对encryptedData加密数据进行AES解密
     *
     * @param encryptedData
     * @param iv
     * @param code
     */
    @Deprecated
    public Map<String, String> getUnionId(String encryptedData, String iv, String code, String appId, String secret) {
        try {
            Map<String, String> response = new HashMap<>();
            //组装参数
            Map<String, String> parameters = new HashMap<>();
            parameters.put("appid", appId);
            parameters.put("secret", secret);
            parameters.put("js_code", code);
            parameters.put("grant_type", "authorization_code");
            //向微信请求
            String result = HttpUtils.sendGet(sessionKeyUrl, parameters);
            JSONObject object = JSONObject.parseObject(result);
            String sessionKey = object.getString("session_key");
            String unionResult = AesUtil.decrypt(encryptedData, sessionKey, iv, "UTF-8");
            log.info("unionResult===" + unionResult);
            if (null != result && result.length() > 0) {
                JSONObject userInfoJSON = JSONObject.parseObject(unionResult);
                String unionId = (String) userInfoJSON.get("unionId");
                String avatarUrl = (String) userInfoJSON.get("avatarUrl");
                response.put("unionId", unionId);
                response.put("avatarUrl", avatarUrl);
                return response;
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 对encryptedData加密数据进行AES解密
     *
     * @param encryptedData
     * @param iv
     * @param code
     */
    public Map<String, String> getUnionId(String encryptedData, String iv, String code, String appId) {
        try {
            Map<String, String> response = new HashMap<>();
            WxOpenMaService wxOpenMaService = wxOpenService.getWxOpenComponentService().getWxMaServiceByAppid(appId);
            WxMaJscode2SessionResult result = wxOpenMaService.jsCode2SessionInfo(code);
            log.info("获取微信结果：{}", JSONObject.toJSONString(result));
            String sessionKey = result.getSessionKey();
            String unionResult = AesUtil.decrypt(encryptedData, sessionKey, iv, "UTF-8");
            log.info("unionResult===" + unionResult);
            if (null != result) {
                JSONObject userInfoJSON = JSONObject.parseObject(unionResult);
                String unionId = (String) userInfoJSON.get("unionId");
                String avatarUrl = (String) userInfoJSON.get("avatarUrl");
                response.put("unionId", unionId);
                response.put("avatarUrl", avatarUrl);
                return response;
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 对encryptedData加密数据进行AES解密
     *
     * @param encryptedData
     * @param iv
     * @param code
     */
    public Map<String, String> getAppUnionId(String encryptedData, String iv, String code, String appId, String secret, String sessionKey) {
        try {
            Map<String, String> response = new HashMap<>();
            //组装参数
          /*  Map<String, String> parameters = new HashMap<>();
            parameters.put("appid", appId);
            parameters.put("secret", secret);
            parameters.put("js_code", code);
            parameters.put("grant_type", "authorization_code");*/
            //向微信请求
            String unionResult = AesUtil.decrypt(encryptedData, sessionKey, iv, "UTF-8");
            log.info("unionResult===" + unionResult);
            if (null != unionResult && unionResult.length() > 0) {
                JSONObject userInfoJSON = JSONObject.parseObject(unionResult);
                String unionId = (String) userInfoJSON.get("unionId");
                String avatarUrl = (String) userInfoJSON.get("avatarUrl");
                response.put("unionId", unionId);
                response.put("avatarUrl", avatarUrl);
                return response;
            }

        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 根据角色ID查询
     *
     * @param roleId
     * @return
     */
    public List<ShopUserModel> getByRoleId(Integer roleId) {
        return shopUserDao.getByRoleId(roleId);
    }


    /**
     * 获取用户钱包信息
     *
     * @param id
     * @return
     */
    public Map selectAmountByUserId(Integer id) {
        return shopUserDao.selectAmountByUserId(id);
    }


    /**
     * 获取终端主账号
     *
     * @param shopId
     * @return
     */
    public ShopUserModel getPrimaryAccountByShopId(Integer shopId) {
        return shopUserDao.getPrimaryAccountByShopId(shopId);
    }

    /**
     * 获取用户的数据
     *
     * @param searchMap
     * @return
     */
    public List<UserModel> getList(Map<String, Object> searchMap) {
        return shopUserDao.getUserList(searchMap);

    }

    /**
     * 功能描述: 退出登录
     *
     * @param shopModel
     * @return void
     * @auther: tms
     * @date: 2021/01/22 15:23
     */
    public void logOut(ShopModel shopModel) {
        // 根据id获取用户信息
        ShopUserModel shopUserModel = shopUserDao.getById(shopModel.getId());
        shopUserDao.logOut(shopUserModel.getOpenId());
    }

    public void updateWecat(Integer id, String wechatNum) {
        ShopUserModel user = shopUserDao.getById(id);
        user.setWechatNum(wechatNum);
        shopUserDao.update(user);
    }

    /**
     * 从微信查询手机号
     *
     * @param code
     * @return
     */
    public String getPhoneByWechatCode(String code, String appId) {
        String token = getAuthAccessToken(appId);
        String phone = "";
        if (!StringUtils.isEmpty(token)) {
            Map<String, Object> param = new HashMap<>();
            param.put("code", code);
            String url = getPhoneNumberUrl + token;
            log.info("查询微信绑定手机号的地址:{}", url);
            try {
                HttpHeaders headers = new HttpHeaders();
                HttpEntity<Map<String, Object>> entity = new HttpEntity<>(param, headers);
                String responseMessage = restTemplate.postForObject(url, entity, String.class);
                log.info("查询微信绑定手机号的返回数据:{}", responseMessage);
                if (!StringUtils.isEmpty(responseMessage)) {
                    JSONObject re = JSONObject.parseObject(responseMessage);
                    if (re != null && re.getInteger("errcode") == 0) {
                        if (re.getJSONObject("phone_info") != null) {
                            phone = re.getJSONObject("phone_info").getString("phoneNumber");
                        }
                    } else {
                        throw new BusinessException("获取微信绑定手机号失败:" + re.get("errmsg").toString());
                    }
                }
            } catch (Exception e) {
                log.info("获取微信绑定手机号失败:{}", e.getMessage());
                throw new BusinessException(e.getMessage());
            }
        } else {
            throw new BusinessException("获取微信access_token失败");
        }
        return phone;
    }

    /**
     * 获取微信第三方access_token
     *
     * @param appId
     * @return
     */
    public String getAuthAccessToken(String appId) {
        String token = "";
        try {
            token = wxOpenServiceConfig.getWxOpenComponentService().getAuthorizerAccessToken(appId, Boolean.FALSE);
        } catch (WxErrorException e) {
            throw new RuntimeException(e);
        }
        return token;
    }

    /**
     * 验证用户身份
     *
     * @param verifyCode 验证码
     * @param
     */
    public void verifyUser(String verifyCode,
                           String userPhone) {

        //redis验证码
        Object checkCode = redisUtil.get("CLOUD_DEALER_LOGIN_VER_CODE_" + userPhone);

        //判断验证码是否正确 如果不相同就抛异常返回
        if (!verifyCode.equals(checkCode)) {
            throw new BusinessException("400", "验证码错误");
        }
    }


    /**
     * 验证用户身份
     *
     * @param verifyCode 验证码
     * @param
     */
    @Transactional
    public void updateUserPassword(String verifyCode,
                                   String userPhone,
                                   String password,
                                   Integer userId) {

        //redis验证码
        Object checkCode = redisUtil.get("CLOUD_DEALER_LOGIN_VER_CODE_" + userPhone);

        //判断验证码是否正确 如果不相同就抛异常返回
        if (!verifyCode.equals(checkCode)) {
            throw new BusinessException("400", "操作已过期");
        }

        //如果相同就根据userId 修改用户密码
        String md5Password = SignUtils.md5(password, md5Key);

        //去修改
        if (shopUserDao.updatePasswordByUserId(userId, md5Password) < 1) {
            throw new BusinessException("500", "修改账户密码失败");
        }

        //没问题最后在删除redis的验证码
        redisUtil.delete("CLOUD_DEALER_LOGIN_VER_CODE_" + userPhone);
    }

    /**
     * 根据终端和终端用户关系表查询可以选择的终端店
     */
    public List<ShopUserModel> selectShopByShopUserRelation(String phone, Integer companyId) {
        return shopUserDao.selectShopByShopUserRelation(phone, companyId);
    }

    /**
     * 根据终端和终端用户关系表查询可以选择的终端店
     */
    public List<ShopUserModel> selectShopByShopUserRelationNew(String phone, Integer companyId) {
        return shopUserDao.selectShopByShopUserRelationNew(phone, companyId);
    }

    /**
     * 根据终端和终端用户关系表查询可以选择的终端店
     */
    public List<ShopUserModel> selectShopByUserId(Integer userId, Integer companyId) {
        return shopUserDao.selectShopByUserId(userId, companyId);
    }

    /**
     * 根据memberShopId查询t_terminal_shop中是否存在会员
     *
     * @param memberShopId
     * @return
     */
    public Long countTerminalShopMemberByMemberShopId(Integer memberShopId) {
        LambdaQueryWrapper<TerminalShopCommonModel> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(TerminalShopCommonModel::getMemberShopId, memberShopId);
        wrapper.eq(TerminalShopCommonModel::getShopType, 5);
        wrapper.eq(TerminalShopCommonModel::getIsDelete, 0);
        return terminalShopCommonDao.selectCount(wrapper);
    }

    /**
     * 根据终端和终端用户关系表查询可以选择的终端店
     *
     * @param userId    用户id
     * @param companyId 公司ID
     * @return
     */
    public List<ShopUserModel> selectShopByUserId2(Integer userId, Integer companyId) {
        if (userId == null || companyId == null) {
            throw new BusinessException("400", "userId或companyId不能为空，userId：" + userId + "，companyId：" + companyId);
        }
        return shopUserDao.selectShopByUserId2(userId, companyId);
    }


    /**
     * 根据终端和终端用户关系表查询可以选择的终端店
     *
     * @param userId    用户id
     * @param companyId 公司ID
     * @return
     */
    public List<ShopUserModel> selectBindShopByUserId(Integer userId, Integer companyId,Integer status)  {
        if (userId == null || companyId == null) {
            throw new BusinessException("400", "userId或companyId不能为空，userId：" + userId + "，companyId：" + companyId);
        }
        return shopUserDao.selectBindShopByUserId(userId, companyId,status);
    }


    /**
     * 根据终端和终端用户关系表查询可以选择的终端店
     *
     * @param phone
     * @param companyId
     * @return
     */
    public List<ShopUserModel> selectShopByShopUserRelationNew2(String phone, Integer companyId) {
        if (StringUtils.isEmpty(phone) || companyId == null) {
            throw new BusinessException("400", "phone或companyId不能为空，phone：" + phone + "，companyId：" + companyId);
        }
        return shopUserDao.selectShopByShopUserRelationNew2(phone, companyId);
    }


    /**
     * 根据终端和终端用户关系表查询可以选择的终端店
     *
     * @param phone
     * @param companyId
     * @return
     */
    public List<ShopUserModel> selectShopByShopUserRelationAndActivate(String phone, Integer companyId) {
        if (StringUtils.isEmpty(phone) || companyId == null) {
            throw new BusinessException("400", "phone或companyId不能为空，phone：" + phone + "，companyId：" + companyId);
        }
        return shopUserDao.selectShopByShopUserRelationAndActivate(phone, companyId);
    }

    /**
     * 根据shopId更新t_menber_shop的status为 启用或禁用
     *
     * @param shopId shopId
     * @param status 状态（启用：0， 禁用：1）
     */
    public void updateStatusByIdAndStatus(Integer shopId, Integer status) {
        if (shopId == null || status == null) {
            throw new BusinessException("400", "shopId或status不能为空！shopId：" + shopId + "，status" + status);
        }
        shopUserDao.updateStatusByIdAndStatus(shopId, status);
    }

    /**
     * 根据主键id查询终端信息
     *
     * @param shopId 主键id
     * @return 结果对象
     */
    public MemberShopResp getMemberShopById(Integer shopId) {
        if (shopId == null) {
            throw new BusinessException("400", "shopId不能为空！shopId：" + shopId);
        }
        return shopUserDao.getMemberShopById(shopId);
    }

    /**
     * 根据shopId查询主协议
     *
     * @param shopId shopId
     * @return 结果
     */
    public TerminalProtocolModel getProtocolByShopId(Integer shopId) {
        if (shopId == null) {
            throw new BusinessException("400", "shopId不能为空！shopId：" + shopId);
        }
        return shopUserDao.getProtocolByShopId(shopId);
    }

    /**
     * 根shopId查询终端信息
     *
     * @param shopId shopId
     * @return 结果
     */
    public TerminalShopResp getTerminalShopByShopId(Integer shopId) {
        return shopUserDao.getTerminalShopByShopId(shopId);
    }

    /**
     * 根据id（t_member_user的主键id）修改账号登录的终端
     *
     * @param id            主键id
     * @param loginTerminal 更新的值
     */
    public void updateLoginTerminalById(Integer id, String loginTerminal) {
        shopUserDao.updateLoginTerminalById(id, loginTerminal);
    }

    /**
     * 根据userId查询授信额度信息
     *
     * @param userId 用户id
     * @return CreditAmount
     */
    public CreditAmount getCreditAmount(Integer userId) {
        return shopUserDao.getCreditAmount(userId);
    }

    public  List<ShopUserModel>  convertShopUserList(List<ShopUserModel> shopUserList) {
        List<ShopUserModel> newShopUserList = new ArrayList<>();
        shopUserList.forEach(item -> {
//          如果是5为终端类型，关联附表查询是否激活，激活添加到新集合中，非5直接添加到新集合中
            if (item.getAccountType() == AccountTypeEnum.account_type_five.getType()) {
                LambdaQueryWrapper<TerminalShopInfoScheduleCommonModel> qw = Wrappers.lambdaQuery();
                qw.eq(TerminalShopInfoScheduleCommonModel::getTerminalShopId, item.getTerminalShopId());
                qw.eq(TerminalShopInfoScheduleCommonModel::getIsDelete, DeleteFlagEnum.NOT_DELETE.getKey());
                TerminalShopInfoScheduleCommonModel terminalShopInfoScheduleModel = terminalShopInfoScheduleCommonDao.selectOne(qw);
                if (Objects.isNull(terminalShopInfoScheduleModel)) {
                    throw new BusinessException("终端附表数据不存在！");
                }
//              金星需求：终端审批中不能登录,与振哥确定方案：关联member_shop表中status需要满足是启用的才可以登录
                LambdaQueryWrapper<MemberShopCommon> memberShopWrapper = Wrappers.lambdaQuery();
                memberShopWrapper.eq(MemberShopCommon::getId, terminalShopInfoScheduleModel.getMemberShopId());
                MemberShopCommon memberShopCommon = memberShopCommonDao.selectOne(memberShopWrapper);
                if (Objects.isNull(memberShopCommon)) {
                    throw new BusinessException("终端会员表数据不存在！");
                }
                if (terminalShopInfoScheduleModel.getStatus() == TerminalAuditStatusEnum.ACTIVATED.getKey() && memberShopCommon.getStatus() == MemberShopStatusEnum.STATUS_ENABLE.getStatus()){
                    newShopUserList.add(item);
                }
            }else{
                newShopUserList.add(item);
            }
        });
        return newShopUserList;
    }

    public List<ShopUserModel> filterShopUserList(List<ShopUserModel> shopUserList) {
        List<ShopUserModel> newShopUserList = new ArrayList<>();
        // 获取终端类型数据
        List<ShopUserModel> terminalShopList = shopUserList.stream()
                .filter(item -> item.getAccountType() == AccountTypeEnum.account_type_five.getType())
                .collect(Collectors.toList());
        List<Integer> terminalShopId = terminalShopList.stream()
                .map(ShopUserModel::getTerminalShopId)
                .collect(Collectors.toList());

        // 附表数据
        List<TerminalShopInfoScheduleCommonModel> terminalShopInfoScheduleList = terminalShopInfoScheduleCommonService.getTerminalShopInfoScheduleList(terminalShopId);
        List<Integer> terminalShopInfoScheduleId = terminalShopInfoScheduleList.stream()
                .map(TerminalShopInfoScheduleCommonModel::getTerminalShopId)
                .collect(Collectors.toList());

        // 获取所有非终端类型数据
        List<ShopUserModel> nonTerminalShopList = shopUserList.stream()
                .filter(item -> item.getAccountType() != AccountTypeEnum.account_type_five.getType())
                .collect(Collectors.toList());
        Boolean isNonTerminalShopListEmpty = CollectionUtils.isEmpty(nonTerminalShopList);

        if (CollectionUtils.isEmpty(terminalShopInfoScheduleId) && isNonTerminalShopListEmpty) {
            throw new BusinessException("该用户不存在");
        }

        // 获取未合并/主店终端类型数据
        if (CollectionUtils.isNotEmpty(terminalShopInfoScheduleId)) {
            List<TerminalShopCommonModel> terminalShopMergeList = terminalShopCommonService.getTerminalShopMergeList(terminalShopInfoScheduleId);
            if (CollectionUtils.isEmpty(terminalShopMergeList) && isNonTerminalShopListEmpty) {
                throw new BusinessException("该用户不存在");
            }

            if (CollectionUtils.isNotEmpty(terminalShopMergeList)) {
                List<Integer> terminalShopMergeId =  terminalShopMergeList.stream()
                        .map(TerminalShopCommonModel::getId)
                        .collect(Collectors.toList());
                List<Integer> filterTerminalShopActivatedId = terminalShopInfoScheduleCommonService.getActivatedTerminalShopIds(terminalShopMergeId);
                if (CollectionUtils.isEmpty(filterTerminalShopActivatedId) && isNonTerminalShopListEmpty) {
                    throw new BusinessException("该用户未激活");
                }

                if (CollectionUtils.isNotEmpty(filterTerminalShopActivatedId)) {
                    List<Integer> filterShopUserId = terminalShopCommonService.getEnabledTerminalShopIds(filterTerminalShopActivatedId);
                    List<ShopUserModel> filterShopUserList = terminalShopList.stream()
                            .filter(item -> filterShopUserId.contains(item.getTerminalShopId()))
                            .collect(Collectors.toList());
                    if (CollectionUtils.isEmpty(filterShopUserList) && isNonTerminalShopListEmpty) {
                        throw new BusinessException("该用户未启用");
                    }
                    newShopUserList.addAll(filterShopUserList);
                }
            }
        }
        // 添加非终端类型用户
        if (CollectionUtils.isNotEmpty(nonTerminalShopList)) {
            newShopUserList.addAll(nonTerminalShopList);
        }
        return newShopUserList;
    }
}
